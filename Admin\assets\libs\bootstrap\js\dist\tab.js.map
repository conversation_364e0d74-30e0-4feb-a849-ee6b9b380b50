{"version": 3, "file": "tab.js", "sources": ["../src/tab.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, getElementFromSelector, getNextActiveElement, isDisabled } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}`\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_DROPDOWN = 'dropdown'\n\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_MENU = '.dropdown-menu'\nconst NOT_SELECTOR_DROPDOWN_TOGGLE = ':not(.dropdown-toggle)'\n\nconst SELECTOR_TAB_PANEL = '.list-group, .nav, [role=\"tablist\"]'\nconst SELECTOR_OUTER = '.nav-item, .list-group-item'\nconst SELECTOR_INNER = `.nav-link${NOT_SELECTOR_DROPDOWN_TOGGLE}, .list-group-item${NOT_SELECTOR_DROPDOWN_TOGGLE}, [role=\"tab\"]${NOT_SELECTOR_DROPDOWN_TOGGLE}`\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]' // todo:v6: could be only `tab`\nconst SELECTOR_INNER_ELEM = `${SELECTOR_INNER}, ${SELECTOR_DATA_TOGGLE}`\n\nconst SELECTOR_DATA_TOGGLE_ACTIVE = `.${CLASS_NAME_ACTIVE}[data-bs-toggle=\"tab\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"pill\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"list\"]`\n\n/**\n * Class definition\n */\n\nclass Tab extends BaseComponent {\n  constructor(element) {\n    super(element)\n    this._parent = this._element.closest(SELECTOR_TAB_PANEL)\n\n    if (!this._parent) {\n      return\n      // todo: should Throw exception on v6\n      // throw new TypeError(`${element.outerHTML} has not a valid parent ${SELECTOR_INNER_ELEM}`)\n    }\n\n    // Set up initial aria attributes\n    this._setInitialAttributes(this._parent, this._getChildren())\n\n    EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n  }\n\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() { // Shows this elem and deactivate the active sibling if exists\n    const innerElem = this._element\n    if (this._elemIsActive(innerElem)) {\n      return\n    }\n\n    // Search for active tab on same parent to deactivate it\n    const active = this._getActiveElem()\n\n    const hideEvent = active ?\n      EventHandler.trigger(active, EVENT_HIDE, { relatedTarget: innerElem }) :\n      null\n\n    const showEvent = EventHandler.trigger(innerElem, EVENT_SHOW, { relatedTarget: active })\n\n    if (showEvent.defaultPrevented || (hideEvent && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._deactivate(active, innerElem)\n    this._activate(innerElem, active)\n  }\n\n  // Private\n  _activate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n\n    this._activate(getElementFromSelector(element)) // Search and activate/show the proper section\n\n    const complete = () => {\n      if (element.getAttribute('role') !== 'tab') {\n        element.classList.add(CLASS_NAME_SHOW)\n        return\n      }\n\n      element.removeAttribute('tabindex')\n      element.setAttribute('aria-selected', true)\n      this._toggleDropDown(element, true)\n      EventHandler.trigger(element, EVENT_SHOWN, {\n        relatedTarget: relatedElem\n      })\n    }\n\n    this._queueCallback(complete, element, element.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _deactivate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.remove(CLASS_NAME_ACTIVE)\n    element.blur()\n\n    this._deactivate(getElementFromSelector(element)) // Search and deactivate the shown section too\n\n    const complete = () => {\n      if (element.getAttribute('role') !== 'tab') {\n        element.classList.remove(CLASS_NAME_SHOW)\n        return\n      }\n\n      element.setAttribute('aria-selected', false)\n      element.setAttribute('tabindex', '-1')\n      this._toggleDropDown(element, false)\n      EventHandler.trigger(element, EVENT_HIDDEN, { relatedTarget: relatedElem })\n    }\n\n    this._queueCallback(complete, element, element.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _keydown(event) {\n    if (!([ARROW_LEFT_KEY, ARROW_RIGHT_KEY, ARROW_UP_KEY, ARROW_DOWN_KEY].includes(event.key))) {\n      return\n    }\n\n    event.stopPropagation()// stopPropagation/preventDefault both added to support up/down keys without scrolling the page\n    event.preventDefault()\n    const isNext = [ARROW_RIGHT_KEY, ARROW_DOWN_KEY].includes(event.key)\n    const nextActiveElement = getNextActiveElement(this._getChildren().filter(element => !isDisabled(element)), event.target, isNext, true)\n\n    if (nextActiveElement) {\n      nextActiveElement.focus({ preventScroll: true })\n      Tab.getOrCreateInstance(nextActiveElement).show()\n    }\n  }\n\n  _getChildren() { // collection of inner elements\n    return SelectorEngine.find(SELECTOR_INNER_ELEM, this._parent)\n  }\n\n  _getActiveElem() {\n    return this._getChildren().find(child => this._elemIsActive(child)) || null\n  }\n\n  _setInitialAttributes(parent, children) {\n    this._setAttributeIfNotExists(parent, 'role', 'tablist')\n\n    for (const child of children) {\n      this._setInitialAttributesOnChild(child)\n    }\n  }\n\n  _setInitialAttributesOnChild(child) {\n    child = this._getInnerElement(child)\n    const isActive = this._elemIsActive(child)\n    const outerElem = this._getOuterElement(child)\n    child.setAttribute('aria-selected', isActive)\n\n    if (outerElem !== child) {\n      this._setAttributeIfNotExists(outerElem, 'role', 'presentation')\n    }\n\n    if (!isActive) {\n      child.setAttribute('tabindex', '-1')\n    }\n\n    this._setAttributeIfNotExists(child, 'role', 'tab')\n\n    // set attributes to the related panel too\n    this._setInitialAttributesOnTargetPanel(child)\n  }\n\n  _setInitialAttributesOnTargetPanel(child) {\n    const target = getElementFromSelector(child)\n\n    if (!target) {\n      return\n    }\n\n    this._setAttributeIfNotExists(target, 'role', 'tabpanel')\n\n    if (child.id) {\n      this._setAttributeIfNotExists(target, 'aria-labelledby', `#${child.id}`)\n    }\n  }\n\n  _toggleDropDown(element, open) {\n    const outerElem = this._getOuterElement(element)\n    if (!outerElem.classList.contains(CLASS_DROPDOWN)) {\n      return\n    }\n\n    const toggle = (selector, className) => {\n      const element = SelectorEngine.findOne(selector, outerElem)\n      if (element) {\n        element.classList.toggle(className, open)\n      }\n    }\n\n    toggle(SELECTOR_DROPDOWN_TOGGLE, CLASS_NAME_ACTIVE)\n    toggle(SELECTOR_DROPDOWN_MENU, CLASS_NAME_SHOW)\n    outerElem.setAttribute('aria-expanded', open)\n  }\n\n  _setAttributeIfNotExists(element, attribute, value) {\n    if (!element.hasAttribute(attribute)) {\n      element.setAttribute(attribute, value)\n    }\n  }\n\n  _elemIsActive(elem) {\n    return elem.classList.contains(CLASS_NAME_ACTIVE)\n  }\n\n  // Try to get the inner element (usually the .nav-link)\n  _getInnerElement(elem) {\n    return elem.matches(SELECTOR_INNER_ELEM) ? elem : SelectorEngine.findOne(SELECTOR_INNER_ELEM, elem)\n  }\n\n  // Try to get the outer element (usually the .nav-item)\n  _getOuterElement(elem) {\n    return elem.closest(SELECTOR_OUTER) || elem\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tab.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  Tab.getOrCreateInstance(this).show()\n})\n\n/**\n * Initialize on focus\n */\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const element of SelectorEngine.find(SELECTOR_DATA_TOGGLE_ACTIVE)) {\n    Tab.getOrCreateInstance(element)\n  }\n})\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tab)\n\nexport default Tab\n"], "names": ["NAME", "DATA_KEY", "EVENT_KEY", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_CLICK_DATA_API", "EVENT_KEYDOWN", "EVENT_LOAD_DATA_API", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "CLASS_NAME_ACTIVE", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "CLASS_DROPDOWN", "SELECTOR_DROPDOWN_TOGGLE", "SELECTOR_DROPDOWN_MENU", "NOT_SELECTOR_DROPDOWN_TOGGLE", "SELECTOR_TAB_PANEL", "SELECTOR_OUTER", "SELECTOR_INNER", "SELECTOR_DATA_TOGGLE", "SELECTOR_INNER_ELEM", "SELECTOR_DATA_TOGGLE_ACTIVE", "Tab", "BaseComponent", "constructor", "element", "_parent", "_element", "closest", "_setInitialAttributes", "_get<PERSON><PERSON><PERSON>n", "EventHandler", "on", "event", "_keydown", "show", "innerElem", "_elemIsActive", "active", "_getActiveElem", "hideEvent", "trigger", "relatedTarget", "showEvent", "defaultPrevented", "_deactivate", "_activate", "relatedElem", "classList", "add", "getElementFromSelector", "complete", "getAttribute", "removeAttribute", "setAttribute", "_toggleDropDown", "_queueCallback", "contains", "remove", "blur", "includes", "key", "stopPropagation", "preventDefault", "isNext", "nextActiveElement", "getNextActiveElement", "filter", "isDisabled", "target", "focus", "preventScroll", "getOrCreateInstance", "SelectorEngine", "find", "child", "parent", "children", "_setAttributeIfNotExists", "_setInitialAttributesOnChild", "_getInnerElement", "isActive", "outerElem", "_getOuterElement", "_setInitialAttributesOnTargetPanel", "id", "open", "toggle", "selector", "className", "findOne", "attribute", "value", "hasAttribute", "elem", "matches", "jQueryInterface", "config", "each", "data", "undefined", "startsWith", "TypeError", "document", "tagName", "window", "defineJQueryPlugin"], "mappings": ";;;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAOA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,KAAb,CAAA;EACA,MAAMC,QAAQ,GAAG,QAAjB,CAAA;EACA,MAAMC,SAAS,GAAI,CAAGD,CAAAA,EAAAA,QAAS,CAA/B,CAAA,CAAA;EAEA,MAAME,UAAU,GAAI,CAAMD,IAAAA,EAAAA,SAAU,CAApC,CAAA,CAAA;EACA,MAAME,YAAY,GAAI,CAAQF,MAAAA,EAAAA,SAAU,CAAxC,CAAA,CAAA;EACA,MAAMG,UAAU,GAAI,CAAMH,IAAAA,EAAAA,SAAU,CAApC,CAAA,CAAA;EACA,MAAMI,WAAW,GAAI,CAAOJ,KAAAA,EAAAA,SAAU,CAAtC,CAAA,CAAA;EACA,MAAMK,oBAAoB,GAAI,CAAOL,KAAAA,EAAAA,SAAU,CAA/C,CAAA,CAAA;EACA,MAAMM,aAAa,GAAI,CAASN,OAAAA,EAAAA,SAAU,CAA1C,CAAA,CAAA;EACA,MAAMO,mBAAmB,GAAI,CAAMP,IAAAA,EAAAA,SAAU,CAA7C,CAAA,CAAA;EAEA,MAAMQ,cAAc,GAAG,WAAvB,CAAA;EACA,MAAMC,eAAe,GAAG,YAAxB,CAAA;EACA,MAAMC,YAAY,GAAG,SAArB,CAAA;EACA,MAAMC,cAAc,GAAG,WAAvB,CAAA;EAEA,MAAMC,iBAAiB,GAAG,QAA1B,CAAA;EACA,MAAMC,eAAe,GAAG,MAAxB,CAAA;EACA,MAAMC,eAAe,GAAG,MAAxB,CAAA;EACA,MAAMC,cAAc,GAAG,UAAvB,CAAA;EAEA,MAAMC,wBAAwB,GAAG,kBAAjC,CAAA;EACA,MAAMC,sBAAsB,GAAG,gBAA/B,CAAA;EACA,MAAMC,4BAA4B,GAAG,wBAArC,CAAA;EAEA,MAAMC,kBAAkB,GAAG,qCAA3B,CAAA;EACA,MAAMC,cAAc,GAAG,6BAAvB,CAAA;EACA,MAAMC,cAAc,GAAI,CAAWH,SAAAA,EAAAA,4BAA6B,qBAAoBA,4BAA6B,CAAA,cAAA,EAAgBA,4BAA6B,CAA9J,CAAA,CAAA;EACA,MAAMI,oBAAoB,GAAG,0EAA7B;;EACA,MAAMC,mBAAmB,GAAI,CAAA,EAAEF,cAAe,CAAA,EAAA,EAAIC,oBAAqB,CAAvE,CAAA,CAAA;EAEA,MAAME,2BAA2B,GAAI,CAAGZ,CAAAA,EAAAA,iBAAkB,4BAA2BA,iBAAkB,CAAA,0BAAA,EAA4BA,iBAAkB,CAArJ,uBAAA,CAAA,CAAA;EAEA;EACA;EACA;;EAEA,MAAMa,GAAN,SAAkBC,8BAAlB,CAAgC;IAC9BC,WAAW,CAACC,OAAD,EAAU;EACnB,IAAA,KAAA,CAAMA,OAAN,CAAA,CAAA;MACA,IAAKC,CAAAA,OAAL,GAAe,IAAKC,CAAAA,QAAL,CAAcC,OAAd,CAAsBZ,kBAAtB,CAAf,CAAA;;MAEA,IAAI,CAAC,IAAKU,CAAAA,OAAV,EAAmB;EACjB,MAAA,OADiB;EAGjB;EACD,KARkB;;;EAWnB,IAAA,IAAA,CAAKG,qBAAL,CAA2B,IAAA,CAAKH,OAAhC,EAAyC,IAAA,CAAKI,YAAL,EAAzC,CAAA,CAAA;;EAEAC,IAAAA,6BAAY,CAACC,EAAb,CAAgB,IAAA,CAAKL,QAArB,EAA+BxB,aAA/B,EAA8C8B,KAAK,IAAI,IAAA,CAAKC,QAAL,CAAcD,KAAd,CAAvD,CAAA,CAAA;EACD,GAf6B;;;EAkBf,EAAA,WAAJtC,IAAI,GAAG;EAChB,IAAA,OAAOA,IAAP,CAAA;EACD,GApB6B;;;EAuB9BwC,EAAAA,IAAI,GAAG;EAAE;MACP,MAAMC,SAAS,GAAG,IAAA,CAAKT,QAAvB,CAAA;;EACA,IAAA,IAAI,IAAKU,CAAAA,aAAL,CAAmBD,SAAnB,CAAJ,EAAmC;EACjC,MAAA,OAAA;EACD,KAJI;;;EAOL,IAAA,MAAME,MAAM,GAAG,IAAKC,CAAAA,cAAL,EAAf,CAAA;;MAEA,MAAMC,SAAS,GAAGF,MAAM,GACtBP,6BAAY,CAACU,OAAb,CAAqBH,MAArB,EAA6BxC,UAA7B,EAAyC;EAAE4C,MAAAA,aAAa,EAAEN,SAAAA;OAA1D,CADsB,GAEtB,IAFF,CAAA;MAIA,MAAMO,SAAS,GAAGZ,6BAAY,CAACU,OAAb,CAAqBL,SAArB,EAAgCpC,UAAhC,EAA4C;EAAE0C,MAAAA,aAAa,EAAEJ,MAAAA;EAAjB,KAA5C,CAAlB,CAAA;;MAEA,IAAIK,SAAS,CAACC,gBAAV,IAA+BJ,SAAS,IAAIA,SAAS,CAACI,gBAA1D,EAA6E;EAC3E,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKC,WAAL,CAAiBP,MAAjB,EAAyBF,SAAzB,CAAA,CAAA;;EACA,IAAA,IAAA,CAAKU,SAAL,CAAeV,SAAf,EAA0BE,MAA1B,CAAA,CAAA;EACD,GA5C6B;;;EA+C9BQ,EAAAA,SAAS,CAACrB,OAAD,EAAUsB,WAAV,EAAuB;MAC9B,IAAI,CAACtB,OAAL,EAAc;EACZ,MAAA,OAAA;EACD,KAAA;;EAEDA,IAAAA,OAAO,CAACuB,SAAR,CAAkBC,GAAlB,CAAsBxC,iBAAtB,CAAA,CAAA;;EAEA,IAAA,IAAA,CAAKqC,SAAL,CAAeI,4BAAsB,CAACzB,OAAD,CAArC,EAP8B;;;MAS9B,MAAM0B,QAAQ,GAAG,MAAM;EACrB,MAAA,IAAI1B,OAAO,CAAC2B,YAAR,CAAqB,MAArB,CAAA,KAAiC,KAArC,EAA4C;EAC1C3B,QAAAA,OAAO,CAACuB,SAAR,CAAkBC,GAAlB,CAAsBtC,eAAtB,CAAA,CAAA;EACA,QAAA,OAAA;EACD,OAAA;;QAEDc,OAAO,CAAC4B,eAAR,CAAwB,UAAxB,CAAA,CAAA;EACA5B,MAAAA,OAAO,CAAC6B,YAAR,CAAqB,eAArB,EAAsC,IAAtC,CAAA,CAAA;;EACA,MAAA,IAAA,CAAKC,eAAL,CAAqB9B,OAArB,EAA8B,IAA9B,CAAA,CAAA;;EACAM,MAAAA,6BAAY,CAACU,OAAb,CAAqBhB,OAArB,EAA8BxB,WAA9B,EAA2C;EACzCyC,QAAAA,aAAa,EAAEK,WAAAA;SADjB,CAAA,CAAA;OATF,CAAA;;EAcA,IAAA,IAAA,CAAKS,cAAL,CAAoBL,QAApB,EAA8B1B,OAA9B,EAAuCA,OAAO,CAACuB,SAAR,CAAkBS,QAAlB,CAA2B/C,eAA3B,CAAvC,CAAA,CAAA;EACD,GAAA;;EAEDmC,EAAAA,WAAW,CAACpB,OAAD,EAAUsB,WAAV,EAAuB;MAChC,IAAI,CAACtB,OAAL,EAAc;EACZ,MAAA,OAAA;EACD,KAAA;;EAEDA,IAAAA,OAAO,CAACuB,SAAR,CAAkBU,MAAlB,CAAyBjD,iBAAzB,CAAA,CAAA;EACAgB,IAAAA,OAAO,CAACkC,IAAR,EAAA,CAAA;;EAEA,IAAA,IAAA,CAAKd,WAAL,CAAiBK,4BAAsB,CAACzB,OAAD,CAAvC,EARgC;;;MAUhC,MAAM0B,QAAQ,GAAG,MAAM;EACrB,MAAA,IAAI1B,OAAO,CAAC2B,YAAR,CAAqB,MAArB,CAAA,KAAiC,KAArC,EAA4C;EAC1C3B,QAAAA,OAAO,CAACuB,SAAR,CAAkBU,MAAlB,CAAyB/C,eAAzB,CAAA,CAAA;EACA,QAAA,OAAA;EACD,OAAA;;EAEDc,MAAAA,OAAO,CAAC6B,YAAR,CAAqB,eAArB,EAAsC,KAAtC,CAAA,CAAA;EACA7B,MAAAA,OAAO,CAAC6B,YAAR,CAAqB,UAArB,EAAiC,IAAjC,CAAA,CAAA;;EACA,MAAA,IAAA,CAAKC,eAAL,CAAqB9B,OAArB,EAA8B,KAA9B,CAAA,CAAA;;EACAM,MAAAA,6BAAY,CAACU,OAAb,CAAqBhB,OAArB,EAA8B1B,YAA9B,EAA4C;EAAE2C,QAAAA,aAAa,EAAEK,WAAAA;SAA7D,CAAA,CAAA;OATF,CAAA;;EAYA,IAAA,IAAA,CAAKS,cAAL,CAAoBL,QAApB,EAA8B1B,OAA9B,EAAuCA,OAAO,CAACuB,SAAR,CAAkBS,QAAlB,CAA2B/C,eAA3B,CAAvC,CAAA,CAAA;EACD,GAAA;;IAEDwB,QAAQ,CAACD,KAAD,EAAQ;EACd,IAAA,IAAI,CAAE,CAAC5B,cAAD,EAAiBC,eAAjB,EAAkCC,YAAlC,EAAgDC,cAAhD,CAAA,CAAgEoD,QAAhE,CAAyE3B,KAAK,CAAC4B,GAA/E,CAAN,EAA4F;EAC1F,MAAA,OAAA;EACD,KAAA;;MAED5B,KAAK,CAAC6B,eAAN,EAAA,CALc;;EAMd7B,IAAAA,KAAK,CAAC8B,cAAN,EAAA,CAAA;EACA,IAAA,MAAMC,MAAM,GAAG,CAAC1D,eAAD,EAAkBE,cAAlB,CAAkCoD,CAAAA,QAAlC,CAA2C3B,KAAK,CAAC4B,GAAjD,CAAf,CAAA;MACA,MAAMI,iBAAiB,GAAGC,0BAAoB,CAAC,IAAA,CAAKpC,YAAL,EAAoBqC,CAAAA,MAApB,CAA2B1C,OAAO,IAAI,CAAC2C,gBAAU,CAAC3C,OAAD,CAAjD,CAAD,EAA8DQ,KAAK,CAACoC,MAApE,EAA4EL,MAA5E,EAAoF,IAApF,CAA9C,CAAA;;EAEA,IAAA,IAAIC,iBAAJ,EAAuB;QACrBA,iBAAiB,CAACK,KAAlB,CAAwB;EAAEC,QAAAA,aAAa,EAAE,IAAA;SAAzC,CAAA,CAAA;EACAjD,MAAAA,GAAG,CAACkD,mBAAJ,CAAwBP,iBAAxB,EAA2C9B,IAA3C,EAAA,CAAA;EACD,KAAA;EACF,GAAA;;EAEDL,EAAAA,YAAY,GAAG;EAAE;MACf,OAAO2C,+BAAc,CAACC,IAAf,CAAoBtD,mBAApB,EAAyC,IAAA,CAAKM,OAA9C,CAAP,CAAA;EACD,GAAA;;EAEDa,EAAAA,cAAc,GAAG;EACf,IAAA,OAAO,IAAKT,CAAAA,YAAL,EAAoB4C,CAAAA,IAApB,CAAyBC,KAAK,IAAI,IAAA,CAAKtC,aAAL,CAAmBsC,KAAnB,CAAlC,KAAgE,IAAvE,CAAA;EACD,GAAA;;EAED9C,EAAAA,qBAAqB,CAAC+C,MAAD,EAASC,QAAT,EAAmB;EACtC,IAAA,IAAA,CAAKC,wBAAL,CAA8BF,MAA9B,EAAsC,MAAtC,EAA8C,SAA9C,CAAA,CAAA;;EAEA,IAAA,KAAK,MAAMD,KAAX,IAAoBE,QAApB,EAA8B;QAC5B,IAAKE,CAAAA,4BAAL,CAAkCJ,KAAlC,CAAA,CAAA;EACD,KAAA;EACF,GAAA;;IAEDI,4BAA4B,CAACJ,KAAD,EAAQ;EAClCA,IAAAA,KAAK,GAAG,IAAA,CAAKK,gBAAL,CAAsBL,KAAtB,CAAR,CAAA;;EACA,IAAA,MAAMM,QAAQ,GAAG,IAAA,CAAK5C,aAAL,CAAmBsC,KAAnB,CAAjB,CAAA;;EACA,IAAA,MAAMO,SAAS,GAAG,IAAA,CAAKC,gBAAL,CAAsBR,KAAtB,CAAlB,CAAA;;EACAA,IAAAA,KAAK,CAACrB,YAAN,CAAmB,eAAnB,EAAoC2B,QAApC,CAAA,CAAA;;MAEA,IAAIC,SAAS,KAAKP,KAAlB,EAAyB;EACvB,MAAA,IAAA,CAAKG,wBAAL,CAA8BI,SAA9B,EAAyC,MAAzC,EAAiD,cAAjD,CAAA,CAAA;EACD,KAAA;;MAED,IAAI,CAACD,QAAL,EAAe;EACbN,MAAAA,KAAK,CAACrB,YAAN,CAAmB,UAAnB,EAA+B,IAA/B,CAAA,CAAA;EACD,KAAA;;MAED,IAAKwB,CAAAA,wBAAL,CAA8BH,KAA9B,EAAqC,MAArC,EAA6C,KAA7C,EAdkC;;;MAiBlC,IAAKS,CAAAA,kCAAL,CAAwCT,KAAxC,CAAA,CAAA;EACD,GAAA;;IAEDS,kCAAkC,CAACT,KAAD,EAAQ;EACxC,IAAA,MAAMN,MAAM,GAAGnB,4BAAsB,CAACyB,KAAD,CAArC,CAAA;;MAEA,IAAI,CAACN,MAAL,EAAa;EACX,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKS,wBAAL,CAA8BT,MAA9B,EAAsC,MAAtC,EAA8C,UAA9C,CAAA,CAAA;;MAEA,IAAIM,KAAK,CAACU,EAAV,EAAc;QACZ,IAAKP,CAAAA,wBAAL,CAA8BT,MAA9B,EAAsC,iBAAtC,EAA0D,CAAGM,CAAAA,EAAAA,KAAK,CAACU,EAAG,CAAtE,CAAA,CAAA,CAAA;EACD,KAAA;EACF,GAAA;;EAED9B,EAAAA,eAAe,CAAC9B,OAAD,EAAU6D,IAAV,EAAgB;EAC7B,IAAA,MAAMJ,SAAS,GAAG,IAAA,CAAKC,gBAAL,CAAsB1D,OAAtB,CAAlB,CAAA;;MACA,IAAI,CAACyD,SAAS,CAAClC,SAAV,CAAoBS,QAApB,CAA6B7C,cAA7B,CAAL,EAAmD;EACjD,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,MAAM2E,MAAM,GAAG,CAACC,QAAD,EAAWC,SAAX,KAAyB;QACtC,MAAMhE,OAAO,GAAGgD,+BAAc,CAACiB,OAAf,CAAuBF,QAAvB,EAAiCN,SAAjC,CAAhB,CAAA;;EACA,MAAA,IAAIzD,OAAJ,EAAa;EACXA,QAAAA,OAAO,CAACuB,SAAR,CAAkBuC,MAAlB,CAAyBE,SAAzB,EAAoCH,IAApC,CAAA,CAAA;EACD,OAAA;OAJH,CAAA;;EAOAC,IAAAA,MAAM,CAAC1E,wBAAD,EAA2BJ,iBAA3B,CAAN,CAAA;EACA8E,IAAAA,MAAM,CAACzE,sBAAD,EAAyBH,eAAzB,CAAN,CAAA;EACAuE,IAAAA,SAAS,CAAC5B,YAAV,CAAuB,eAAvB,EAAwCgC,IAAxC,CAAA,CAAA;EACD,GAAA;;EAEDR,EAAAA,wBAAwB,CAACrD,OAAD,EAAUkE,SAAV,EAAqBC,KAArB,EAA4B;EAClD,IAAA,IAAI,CAACnE,OAAO,CAACoE,YAAR,CAAqBF,SAArB,CAAL,EAAsC;EACpClE,MAAAA,OAAO,CAAC6B,YAAR,CAAqBqC,SAArB,EAAgCC,KAAhC,CAAA,CAAA;EACD,KAAA;EACF,GAAA;;IAEDvD,aAAa,CAACyD,IAAD,EAAO;EAClB,IAAA,OAAOA,IAAI,CAAC9C,SAAL,CAAeS,QAAf,CAAwBhD,iBAAxB,CAAP,CAAA;EACD,GA9L6B;;;IAiM9BuE,gBAAgB,CAACc,IAAD,EAAO;EACrB,IAAA,OAAOA,IAAI,CAACC,OAAL,CAAa3E,mBAAb,CAAoC0E,GAAAA,IAApC,GAA2CrB,+BAAc,CAACiB,OAAf,CAAuBtE,mBAAvB,EAA4C0E,IAA5C,CAAlD,CAAA;EACD,GAnM6B;;;IAsM9BX,gBAAgB,CAACW,IAAD,EAAO;EACrB,IAAA,OAAOA,IAAI,CAAClE,OAAL,CAAaX,cAAb,KAAgC6E,IAAvC,CAAA;EACD,GAxM6B;;;IA2MR,OAAfE,eAAe,CAACC,MAAD,EAAS;MAC7B,OAAO,IAAA,CAAKC,IAAL,CAAU,YAAY;EAC3B,MAAA,MAAMC,IAAI,GAAG7E,GAAG,CAACkD,mBAAJ,CAAwB,IAAxB,CAAb,CAAA;;EAEA,MAAA,IAAI,OAAOyB,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,QAAA,OAAA;EACD,OAAA;;EAED,MAAA,IAAIE,IAAI,CAACF,MAAD,CAAJ,KAAiBG,SAAjB,IAA8BH,MAAM,CAACI,UAAP,CAAkB,GAAlB,CAA9B,IAAwDJ,MAAM,KAAK,aAAvE,EAAsF;EACpF,QAAA,MAAM,IAAIK,SAAJ,CAAe,CAAmBL,iBAAAA,EAAAA,MAAO,GAAzC,CAAN,CAAA;EACD,OAAA;;QAEDE,IAAI,CAACF,MAAD,CAAJ,EAAA,CAAA;EACD,KAZM,CAAP,CAAA;EAaD,GAAA;;EAzN6B,CAAA;EA4NhC;EACA;EACA;;;AAEAlE,+BAAY,CAACC,EAAb,CAAgBuE,QAAhB,EAA0BrG,oBAA1B,EAAgDiB,oBAAhD,EAAsE,UAAUc,KAAV,EAAiB;IACrF,IAAI,CAAC,GAAD,EAAM,MAAN,CAAA,CAAc2B,QAAd,CAAuB,IAAA,CAAK4C,OAA5B,CAAJ,EAA0C;EACxCvE,IAAAA,KAAK,CAAC8B,cAAN,EAAA,CAAA;EACD,GAAA;;EAED,EAAA,IAAIK,gBAAU,CAAC,IAAD,CAAd,EAAsB;EACpB,IAAA,OAAA;EACD,GAAA;;EAED9C,EAAAA,GAAG,CAACkD,mBAAJ,CAAwB,IAAxB,EAA8BrC,IAA9B,EAAA,CAAA;EACD,CAVD,CAAA,CAAA;EAYA;EACA;EACA;;AACAJ,+BAAY,CAACC,EAAb,CAAgByE,MAAhB,EAAwBrG,mBAAxB,EAA6C,MAAM;IACjD,KAAK,MAAMqB,OAAX,IAAsBgD,+BAAc,CAACC,IAAf,CAAoBrD,2BAApB,CAAtB,EAAwE;MACtEC,GAAG,CAACkD,mBAAJ,CAAwB/C,OAAxB,CAAA,CAAA;EACD,GAAA;EACF,CAJD,CAAA,CAAA;EAKA;EACA;EACA;;AAEAiF,0BAAkB,CAACpF,GAAD,CAAlB;;;;;;;;"}