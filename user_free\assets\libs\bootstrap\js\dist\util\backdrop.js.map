{"version": 3, "file": "backdrop.js", "sources": ["../../src/util/backdrop.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { execute, executeAfterTransition, getElement, reflow } from './index'\nimport Config from './config'\n\n/**\n * Constants\n */\n\nconst NAME = 'backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nconst Default = {\n  className: 'modal-backdrop',\n  clickCallback: null,\n  isAnimated: false,\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  rootElement: 'body' // give the choice to place backdrop under different elements\n}\n\nconst DefaultType = {\n  className: 'string',\n  clickCallback: '(function|null)',\n  isAnimated: 'boolean',\n  isVisible: 'boolean',\n  rootElement: '(element|string)'\n}\n\n/**\n * Class definition\n */\n\nclass Backdrop extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    const element = this._getElement()\n    if (this._config.isAnimated) {\n      reflow(element)\n    }\n\n    element.classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._element.remove()\n    this._isAppended = false\n  }\n\n  // Private\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = this._config.className\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _configAfterMerge(config) {\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    const element = this._getElement()\n    this._config.rootElement.append(element)\n\n    EventHandler.on(element, EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated)\n  }\n}\n\nexport default Backdrop\n"], "names": ["NAME", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "EVENT_MOUSEDOWN", "<PERSON><PERSON><PERSON>", "className", "clickCallback", "isAnimated", "isVisible", "rootElement", "DefaultType", "Backdrop", "Config", "constructor", "config", "_config", "_getConfig", "_isAppended", "_element", "show", "callback", "execute", "_append", "element", "_getElement", "reflow", "classList", "add", "_emulateAnimation", "hide", "remove", "dispose", "EventHandler", "off", "backdrop", "document", "createElement", "_configAfterMerge", "getElement", "append", "on", "executeAfterTransition"], "mappings": ";;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAMA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,UAAb,CAAA;EACA,MAAMC,eAAe,GAAG,MAAxB,CAAA;EACA,MAAMC,eAAe,GAAG,MAAxB,CAAA;EACA,MAAMC,eAAe,GAAI,CAAeH,aAAAA,EAAAA,IAAK,CAA7C,CAAA,CAAA;EAEA,MAAMI,OAAO,GAAG;EACdC,EAAAA,SAAS,EAAE,gBADG;EAEdC,EAAAA,aAAa,EAAE,IAFD;EAGdC,EAAAA,UAAU,EAAE,KAHE;EAIdC,EAAAA,SAAS,EAAE,IAJG;EAIG;IACjBC,WAAW,EAAE,MALC;;EAAA,CAAhB,CAAA;EAQA,MAAMC,WAAW,GAAG;EAClBL,EAAAA,SAAS,EAAE,QADO;EAElBC,EAAAA,aAAa,EAAE,iBAFG;EAGlBC,EAAAA,UAAU,EAAE,SAHM;EAIlBC,EAAAA,SAAS,EAAE,SAJO;EAKlBC,EAAAA,WAAW,EAAE,kBAAA;EALK,CAApB,CAAA;EAQA;EACA;EACA;;EAEA,MAAME,QAAN,SAAuBC,uBAAvB,CAA8B;IAC5BC,WAAW,CAACC,MAAD,EAAS;EAClB,IAAA,KAAA,EAAA,CAAA;EACA,IAAA,IAAA,CAAKC,OAAL,GAAe,IAAA,CAAKC,UAAL,CAAgBF,MAAhB,CAAf,CAAA;MACA,IAAKG,CAAAA,WAAL,GAAmB,KAAnB,CAAA;MACA,IAAKC,CAAAA,QAAL,GAAgB,IAAhB,CAAA;EACD,GAN2B;;;EASV,EAAA,WAAPd,OAAO,GAAG;EACnB,IAAA,OAAOA,OAAP,CAAA;EACD,GAAA;;EAEqB,EAAA,WAAXM,WAAW,GAAG;EACvB,IAAA,OAAOA,WAAP,CAAA;EACD,GAAA;;EAEc,EAAA,WAAJV,IAAI,GAAG;EAChB,IAAA,OAAOA,IAAP,CAAA;EACD,GAnB2B;;;IAsB5BmB,IAAI,CAACC,QAAD,EAAW;EACb,IAAA,IAAI,CAAC,IAAA,CAAKL,OAAL,CAAaP,SAAlB,EAA6B;QAC3Ba,aAAO,CAACD,QAAD,CAAP,CAAA;EACA,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKE,OAAL,EAAA,CAAA;;EAEA,IAAA,MAAMC,OAAO,GAAG,IAAKC,CAAAA,WAAL,EAAhB,CAAA;;EACA,IAAA,IAAI,IAAKT,CAAAA,OAAL,CAAaR,UAAjB,EAA6B;QAC3BkB,YAAM,CAACF,OAAD,CAAN,CAAA;EACD,KAAA;;EAEDA,IAAAA,OAAO,CAACG,SAAR,CAAkBC,GAAlB,CAAsBzB,eAAtB,CAAA,CAAA;;MAEA,IAAK0B,CAAAA,iBAAL,CAAuB,MAAM;QAC3BP,aAAO,CAACD,QAAD,CAAP,CAAA;OADF,CAAA,CAAA;EAGD,GAAA;;IAEDS,IAAI,CAACT,QAAD,EAAW;EACb,IAAA,IAAI,CAAC,IAAA,CAAKL,OAAL,CAAaP,SAAlB,EAA6B;QAC3Ba,aAAO,CAACD,QAAD,CAAP,CAAA;EACA,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKI,WAAL,EAAmBE,CAAAA,SAAnB,CAA6BI,MAA7B,CAAoC5B,eAApC,CAAA,CAAA;;MAEA,IAAK0B,CAAAA,iBAAL,CAAuB,MAAM;EAC3B,MAAA,IAAA,CAAKG,OAAL,EAAA,CAAA;QACAV,aAAO,CAACD,QAAD,CAAP,CAAA;OAFF,CAAA,CAAA;EAID,GAAA;;EAEDW,EAAAA,OAAO,GAAG;MACR,IAAI,CAAC,IAAKd,CAAAA,WAAV,EAAuB;EACrB,MAAA,OAAA;EACD,KAAA;;EAEDe,IAAAA,6BAAY,CAACC,GAAb,CAAiB,IAAKf,CAAAA,QAAtB,EAAgCf,eAAhC,CAAA,CAAA;;MAEA,IAAKe,CAAAA,QAAL,CAAcY,MAAd,EAAA,CAAA;;MACA,IAAKb,CAAAA,WAAL,GAAmB,KAAnB,CAAA;EACD,GAjE2B;;;EAoE5BO,EAAAA,WAAW,GAAG;MACZ,IAAI,CAAC,IAAKN,CAAAA,QAAV,EAAoB;EAClB,MAAA,MAAMgB,QAAQ,GAAGC,QAAQ,CAACC,aAAT,CAAuB,KAAvB,CAAjB,CAAA;EACAF,MAAAA,QAAQ,CAAC7B,SAAT,GAAqB,IAAKU,CAAAA,OAAL,CAAaV,SAAlC,CAAA;;EACA,MAAA,IAAI,IAAKU,CAAAA,OAAL,CAAaR,UAAjB,EAA6B;EAC3B2B,QAAAA,QAAQ,CAACR,SAAT,CAAmBC,GAAnB,CAAuB1B,eAAvB,CAAA,CAAA;EACD,OAAA;;QAED,IAAKiB,CAAAA,QAAL,GAAgBgB,QAAhB,CAAA;EACD,KAAA;;EAED,IAAA,OAAO,KAAKhB,QAAZ,CAAA;EACD,GAAA;;IAEDmB,iBAAiB,CAACvB,MAAD,EAAS;EACxB;MACAA,MAAM,CAACL,WAAP,GAAqB6B,gBAAU,CAACxB,MAAM,CAACL,WAAR,CAA/B,CAAA;EACA,IAAA,OAAOK,MAAP,CAAA;EACD,GAAA;;EAEDQ,EAAAA,OAAO,GAAG;MACR,IAAI,IAAA,CAAKL,WAAT,EAAsB;EACpB,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,MAAMM,OAAO,GAAG,IAAKC,CAAAA,WAAL,EAAhB,CAAA;;EACA,IAAA,IAAA,CAAKT,OAAL,CAAaN,WAAb,CAAyB8B,MAAzB,CAAgChB,OAAhC,CAAA,CAAA;;EAEAS,IAAAA,6BAAY,CAACQ,EAAb,CAAgBjB,OAAhB,EAAyBpB,eAAzB,EAA0C,MAAM;EAC9CkB,MAAAA,aAAO,CAAC,IAAA,CAAKN,OAAL,CAAaT,aAAd,CAAP,CAAA;OADF,CAAA,CAAA;MAIA,IAAKW,CAAAA,WAAL,GAAmB,IAAnB,CAAA;EACD,GAAA;;IAEDW,iBAAiB,CAACR,QAAD,EAAW;MAC1BqB,4BAAsB,CAACrB,QAAD,EAAW,IAAKI,CAAAA,WAAL,EAAX,EAA+B,IAAKT,CAAAA,OAAL,CAAaR,UAA5C,CAAtB,CAAA;EACD,GAAA;;EAzG2B;;;;;;;;"}