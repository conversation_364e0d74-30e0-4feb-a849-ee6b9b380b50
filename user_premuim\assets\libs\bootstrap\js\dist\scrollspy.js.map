{"version": 3, "file": "scrollspy.js", "sources": ["../src/scrollspy.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, getElement, isDisabled, isVisible } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_TARGET_LINKS = '[href]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_LINK_ITEMS = `${SELECTOR_NAV_LINKS}, ${SELECTOR_NAV_ITEMS} > ${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst Default = {\n  offset: null, // TODO: v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: '0px 0px -25%',\n  smoothScroll: false,\n  target: null,\n  threshold: [0.1, 0.5, 1]\n}\n\nconst DefaultType = {\n  offset: '(number|null)', // TODO v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: 'string',\n  smoothScroll: 'boolean',\n  target: 'element',\n  threshold: 'array'\n}\n\n/**\n * Class definition\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    // this._element is the observablesContainer and config.target the menu links wrapper\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n    this._rootElement = getComputedStyle(this._element).overflowY === 'visible' ? null : this._element\n    this._activeTarget = null\n    this._observer = null\n    this._previousScrollData = {\n      visibleEntryTop: 0,\n      parentScrollTop: 0\n    }\n    this.refresh() // initialize\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  refresh() {\n    this._initializeTargetsAndObservables()\n    this._maybeEnableSmoothScroll()\n\n    if (this._observer) {\n      this._observer.disconnect()\n    } else {\n      this._observer = this._getNewObserver()\n    }\n\n    for (const section of this._observableSections.values()) {\n      this._observer.observe(section)\n    }\n  }\n\n  dispose() {\n    this._observer.disconnect()\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    // TODO: on v6 target should be given explicitly & remove the {target: 'ss-target'} case\n    config.target = getElement(config.target) || document.body\n\n    // TODO: v6 Only for backwards compatibility reasons. Use rootMargin only\n    config.rootMargin = config.offset ? `${config.offset}px 0px -30%` : config.rootMargin\n\n    if (typeof config.threshold === 'string') {\n      config.threshold = config.threshold.split(',').map(value => Number.parseFloat(value))\n    }\n\n    return config\n  }\n\n  _maybeEnableSmoothScroll() {\n    if (!this._config.smoothScroll) {\n      return\n    }\n\n    // unregister any previous listeners\n    EventHandler.off(this._config.target, EVENT_CLICK)\n\n    EventHandler.on(this._config.target, EVENT_CLICK, SELECTOR_TARGET_LINKS, event => {\n      const observableSection = this._observableSections.get(event.target.hash)\n      if (observableSection) {\n        event.preventDefault()\n        const root = this._rootElement || window\n        const height = observableSection.offsetTop - this._element.offsetTop\n        if (root.scrollTo) {\n          root.scrollTo({ top: height, behavior: 'smooth' })\n          return\n        }\n\n        // Chrome 60 doesn't support `scrollTo`\n        root.scrollTop = height\n      }\n    })\n  }\n\n  _getNewObserver() {\n    const options = {\n      root: this._rootElement,\n      threshold: this._config.threshold,\n      rootMargin: this._config.rootMargin\n    }\n\n    return new IntersectionObserver(entries => this._observerCallback(entries), options)\n  }\n\n  // The logic of selection\n  _observerCallback(entries) {\n    const targetElement = entry => this._targetLinks.get(`#${entry.target.id}`)\n    const activate = entry => {\n      this._previousScrollData.visibleEntryTop = entry.target.offsetTop\n      this._process(targetElement(entry))\n    }\n\n    const parentScrollTop = (this._rootElement || document.documentElement).scrollTop\n    const userScrollsDown = parentScrollTop >= this._previousScrollData.parentScrollTop\n    this._previousScrollData.parentScrollTop = parentScrollTop\n\n    for (const entry of entries) {\n      if (!entry.isIntersecting) {\n        this._activeTarget = null\n        this._clearActiveClass(targetElement(entry))\n\n        continue\n      }\n\n      const entryIsLowerThanPrevious = entry.target.offsetTop >= this._previousScrollData.visibleEntryTop\n      // if we are scrolling down, pick the bigger offsetTop\n      if (userScrollsDown && entryIsLowerThanPrevious) {\n        activate(entry)\n        // if parent isn't scrolled, let's keep the first visible item, breaking the iteration\n        if (!parentScrollTop) {\n          return\n        }\n\n        continue\n      }\n\n      // if we are scrolling up, pick the smallest offsetTop\n      if (!userScrollsDown && !entryIsLowerThanPrevious) {\n        activate(entry)\n      }\n    }\n  }\n\n  _initializeTargetsAndObservables() {\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n\n    const targetLinks = SelectorEngine.find(SELECTOR_TARGET_LINKS, this._config.target)\n\n    for (const anchor of targetLinks) {\n      // ensure that the anchor has an id and is not disabled\n      if (!anchor.hash || isDisabled(anchor)) {\n        continue\n      }\n\n      const observableSection = SelectorEngine.findOne(anchor.hash, this._element)\n\n      // ensure that the observableSection exists & is visible\n      if (isVisible(observableSection)) {\n        this._targetLinks.set(anchor.hash, anchor)\n        this._observableSections.set(anchor.hash, observableSection)\n      }\n    }\n  }\n\n  _process(target) {\n    if (this._activeTarget === target) {\n      return\n    }\n\n    this._clearActiveClass(this._config.target)\n    this._activeTarget = target\n    target.classList.add(CLASS_NAME_ACTIVE)\n    this._activateParents(target)\n\n    EventHandler.trigger(this._element, EVENT_ACTIVATE, { relatedTarget: target })\n  }\n\n  _activateParents(target) {\n    // Activate dropdown parents\n    if (target.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, target.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n      return\n    }\n\n    for (const listGroup of SelectorEngine.parents(target, SELECTOR_NAV_LIST_GROUP)) {\n      // Set triggered links parents as active\n      // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n      for (const item of SelectorEngine.prev(listGroup, SELECTOR_LINK_ITEMS)) {\n        item.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _clearActiveClass(parent) {\n    parent.classList.remove(CLASS_NAME_ACTIVE)\n\n    const activeNodes = SelectorEngine.find(`${SELECTOR_TARGET_LINKS}.${CLASS_NAME_ACTIVE}`, parent)\n    for (const node of activeNodes) {\n      node.classList.remove(CLASS_NAME_ACTIVE)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = ScrollSpy.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const spy of SelectorEngine.find(SELECTOR_DATA_SPY)) {\n    ScrollSpy.getOrCreateInstance(spy)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(ScrollSpy)\n\nexport default ScrollSpy\n"], "names": ["NAME", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "EVENT_ACTIVATE", "EVENT_CLICK", "EVENT_LOAD_DATA_API", "CLASS_NAME_DROPDOWN_ITEM", "CLASS_NAME_ACTIVE", "SELECTOR_DATA_SPY", "SELECTOR_TARGET_LINKS", "SELECTOR_NAV_LIST_GROUP", "SELECTOR_NAV_LINKS", "SELECTOR_NAV_ITEMS", "SELECTOR_LIST_ITEMS", "SELECTOR_LINK_ITEMS", "SELECTOR_DROPDOWN", "SELECTOR_DROPDOWN_TOGGLE", "<PERSON><PERSON><PERSON>", "offset", "rootMargin", "smoothScroll", "target", "threshold", "DefaultType", "ScrollSpy", "BaseComponent", "constructor", "element", "config", "_targetLinks", "Map", "_observableSections", "_rootElement", "getComputedStyle", "_element", "overflowY", "_activeTarget", "_observer", "_previousScrollData", "visibleEntryTop", "parentScrollTop", "refresh", "_initializeTargetsAndObservables", "_maybeEnableSmoothScroll", "disconnect", "_getNewObserver", "section", "values", "observe", "dispose", "_configAfterMerge", "getElement", "document", "body", "split", "map", "value", "Number", "parseFloat", "_config", "EventHandler", "off", "on", "event", "observableSection", "get", "hash", "preventDefault", "root", "window", "height", "offsetTop", "scrollTo", "top", "behavior", "scrollTop", "options", "IntersectionObserver", "entries", "_<PERSON><PERSON><PERSON><PERSON>", "targetElement", "entry", "id", "activate", "_process", "documentElement", "userScrollsDown", "isIntersecting", "_clearActiveClass", "entryIsLowerThanPrevious", "targetLinks", "SelectorEngine", "find", "anchor", "isDisabled", "findOne", "isVisible", "set", "classList", "add", "_activateParents", "trigger", "relatedTarget", "contains", "closest", "listGroup", "parents", "item", "prev", "parent", "remove", "activeNodes", "node", "jQueryInterface", "each", "data", "getOrCreateInstance", "undefined", "startsWith", "TypeError", "spy", "defineJQueryPlugin"], "mappings": ";;;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAOA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,WAAb,CAAA;EACA,MAAMC,QAAQ,GAAG,cAAjB,CAAA;EACA,MAAMC,SAAS,GAAI,CAAGD,CAAAA,EAAAA,QAAS,CAA/B,CAAA,CAAA;EACA,MAAME,YAAY,GAAG,WAArB,CAAA;EAEA,MAAMC,cAAc,GAAI,CAAUF,QAAAA,EAAAA,SAAU,CAA5C,CAAA,CAAA;EACA,MAAMG,WAAW,GAAI,CAAOH,KAAAA,EAAAA,SAAU,CAAtC,CAAA,CAAA;EACA,MAAMI,mBAAmB,GAAI,CAAA,IAAA,EAAMJ,SAAU,CAAA,EAAEC,YAAa,CAA5D,CAAA,CAAA;EAEA,MAAMI,wBAAwB,GAAG,eAAjC,CAAA;EACA,MAAMC,iBAAiB,GAAG,QAA1B,CAAA;EAEA,MAAMC,iBAAiB,GAAG,wBAA1B,CAAA;EACA,MAAMC,qBAAqB,GAAG,QAA9B,CAAA;EACA,MAAMC,uBAAuB,GAAG,mBAAhC,CAAA;EACA,MAAMC,kBAAkB,GAAG,WAA3B,CAAA;EACA,MAAMC,kBAAkB,GAAG,WAA3B,CAAA;EACA,MAAMC,mBAAmB,GAAG,kBAA5B,CAAA;EACA,MAAMC,mBAAmB,GAAI,CAAA,EAAEH,kBAAmB,CAAA,EAAA,EAAIC,kBAAmB,CAAKD,GAAAA,EAAAA,kBAAmB,CAAIE,EAAAA,EAAAA,mBAAoB,CAAzH,CAAA,CAAA;EACA,MAAME,iBAAiB,GAAG,WAA1B,CAAA;EACA,MAAMC,wBAAwB,GAAG,kBAAjC,CAAA;EAEA,MAAMC,OAAO,GAAG;EACdC,EAAAA,MAAM,EAAE,IADM;EACA;EACdC,EAAAA,UAAU,EAAE,cAFE;EAGdC,EAAAA,YAAY,EAAE,KAHA;EAIdC,EAAAA,MAAM,EAAE,IAJM;EAKdC,EAAAA,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CAAA;EALG,CAAhB,CAAA;EAQA,MAAMC,WAAW,GAAG;EAClBL,EAAAA,MAAM,EAAE,eADU;EACO;EACzBC,EAAAA,UAAU,EAAE,QAFM;EAGlBC,EAAAA,YAAY,EAAE,SAHI;EAIlBC,EAAAA,MAAM,EAAE,SAJU;EAKlBC,EAAAA,SAAS,EAAE,OAAA;EALO,CAApB,CAAA;EAQA;EACA;EACA;;EAEA,MAAME,SAAN,SAAwBC,8BAAxB,CAAsC;EACpCC,EAAAA,WAAW,CAACC,OAAD,EAAUC,MAAV,EAAkB;EAC3B,IAAA,KAAA,CAAMD,OAAN,EAAeC,MAAf,CAAA,CAD2B;;EAI3B,IAAA,IAAA,CAAKC,YAAL,GAAoB,IAAIC,GAAJ,EAApB,CAAA;EACA,IAAA,IAAA,CAAKC,mBAAL,GAA2B,IAAID,GAAJ,EAA3B,CAAA;EACA,IAAA,IAAA,CAAKE,YAAL,GAAoBC,gBAAgB,CAAC,KAAKC,QAAN,CAAhB,CAAgCC,SAAhC,KAA8C,SAA9C,GAA0D,IAA1D,GAAiE,KAAKD,QAA1F,CAAA;MACA,IAAKE,CAAAA,aAAL,GAAqB,IAArB,CAAA;MACA,IAAKC,CAAAA,SAAL,GAAiB,IAAjB,CAAA;EACA,IAAA,IAAA,CAAKC,mBAAL,GAA2B;EACzBC,MAAAA,eAAe,EAAE,CADQ;EAEzBC,MAAAA,eAAe,EAAE,CAAA;OAFnB,CAAA;MAIA,IAAKC,CAAAA,OAAL,GAb2B;EAc5B,GAfmC;;;EAkBlB,EAAA,WAAPxB,OAAO,GAAG;EACnB,IAAA,OAAOA,OAAP,CAAA;EACD,GAAA;;EAEqB,EAAA,WAAXM,WAAW,GAAG;EACvB,IAAA,OAAOA,WAAP,CAAA;EACD,GAAA;;EAEc,EAAA,WAAJxB,IAAI,GAAG;EAChB,IAAA,OAAOA,IAAP,CAAA;EACD,GA5BmC;;;EA+BpC0C,EAAAA,OAAO,GAAG;EACR,IAAA,IAAA,CAAKC,gCAAL,EAAA,CAAA;;EACA,IAAA,IAAA,CAAKC,wBAAL,EAAA,CAAA;;MAEA,IAAI,IAAA,CAAKN,SAAT,EAAoB;QAClB,IAAKA,CAAAA,SAAL,CAAeO,UAAf,EAAA,CAAA;EACD,KAFD,MAEO;EACL,MAAA,IAAA,CAAKP,SAAL,GAAiB,IAAKQ,CAAAA,eAAL,EAAjB,CAAA;EACD,KAAA;;MAED,KAAK,MAAMC,OAAX,IAAsB,IAAA,CAAKf,mBAAL,CAAyBgB,MAAzB,EAAtB,EAAyD;EACvD,MAAA,IAAA,CAAKV,SAAL,CAAeW,OAAf,CAAuBF,OAAvB,CAAA,CAAA;EACD,KAAA;EACF,GAAA;;EAEDG,EAAAA,OAAO,GAAG;MACR,IAAKZ,CAAAA,SAAL,CAAeO,UAAf,EAAA,CAAA;;EACA,IAAA,KAAA,CAAMK,OAAN,EAAA,CAAA;EACD,GAjDmC;;;IAoDpCC,iBAAiB,CAACtB,MAAD,EAAS;EACxB;EACAA,IAAAA,MAAM,CAACP,MAAP,GAAgB8B,gBAAU,CAACvB,MAAM,CAACP,MAAR,CAAV,IAA6B+B,QAAQ,CAACC,IAAtD,CAFwB;;EAKxBzB,IAAAA,MAAM,CAACT,UAAP,GAAoBS,MAAM,CAACV,MAAP,GAAiB,CAAEU,EAAAA,MAAM,CAACV,MAAO,CAAA,WAAA,CAAjC,GAAgDU,MAAM,CAACT,UAA3E,CAAA;;EAEA,IAAA,IAAI,OAAOS,MAAM,CAACN,SAAd,KAA4B,QAAhC,EAA0C;QACxCM,MAAM,CAACN,SAAP,GAAmBM,MAAM,CAACN,SAAP,CAAiBgC,KAAjB,CAAuB,GAAvB,EAA4BC,GAA5B,CAAgCC,KAAK,IAAIC,MAAM,CAACC,UAAP,CAAkBF,KAAlB,CAAzC,CAAnB,CAAA;EACD,KAAA;;EAED,IAAA,OAAO5B,MAAP,CAAA;EACD,GAAA;;EAEDe,EAAAA,wBAAwB,GAAG;EACzB,IAAA,IAAI,CAAC,IAAA,CAAKgB,OAAL,CAAavC,YAAlB,EAAgC;EAC9B,MAAA,OAAA;EACD,KAHwB;;;MAMzBwC,6BAAY,CAACC,GAAb,CAAiB,IAAA,CAAKF,OAAL,CAAatC,MAA9B,EAAsCjB,WAAtC,CAAA,CAAA;EAEAwD,IAAAA,6BAAY,CAACE,EAAb,CAAgB,IAAA,CAAKH,OAAL,CAAatC,MAA7B,EAAqCjB,WAArC,EAAkDK,qBAAlD,EAAyEsD,KAAK,IAAI;EAChF,MAAA,MAAMC,iBAAiB,GAAG,IAAKjC,CAAAA,mBAAL,CAAyBkC,GAAzB,CAA6BF,KAAK,CAAC1C,MAAN,CAAa6C,IAA1C,CAA1B,CAAA;;EACA,MAAA,IAAIF,iBAAJ,EAAuB;EACrBD,QAAAA,KAAK,CAACI,cAAN,EAAA,CAAA;EACA,QAAA,MAAMC,IAAI,GAAG,IAAKpC,CAAAA,YAAL,IAAqBqC,MAAlC,CAAA;UACA,MAAMC,MAAM,GAAGN,iBAAiB,CAACO,SAAlB,GAA8B,IAAA,CAAKrC,QAAL,CAAcqC,SAA3D,CAAA;;UACA,IAAIH,IAAI,CAACI,QAAT,EAAmB;YACjBJ,IAAI,CAACI,QAAL,CAAc;EAAEC,YAAAA,GAAG,EAAEH,MAAP;EAAeI,YAAAA,QAAQ,EAAE,QAAA;aAAvC,CAAA,CAAA;EACA,UAAA,OAAA;EACD,SAPoB;;;UAUrBN,IAAI,CAACO,SAAL,GAAiBL,MAAjB,CAAA;EACD,OAAA;OAbH,CAAA,CAAA;EAeD,GAAA;;EAEDzB,EAAAA,eAAe,GAAG;EAChB,IAAA,MAAM+B,OAAO,GAAG;QACdR,IAAI,EAAE,KAAKpC,YADG;EAEdV,MAAAA,SAAS,EAAE,IAAA,CAAKqC,OAAL,CAAarC,SAFV;QAGdH,UAAU,EAAE,IAAKwC,CAAAA,OAAL,CAAaxC,UAAAA;OAH3B,CAAA;EAMA,IAAA,OAAO,IAAI0D,oBAAJ,CAAyBC,OAAO,IAAI,IAAA,CAAKC,iBAAL,CAAuBD,OAAvB,CAApC,EAAqEF,OAArE,CAAP,CAAA;EACD,GAnGmC;;;IAsGpCG,iBAAiB,CAACD,OAAD,EAAU;EACzB,IAAA,MAAME,aAAa,GAAGC,KAAK,IAAI,IAAA,CAAKpD,YAAL,CAAkBoC,GAAlB,CAAuB,CAAA,CAAA,EAAGgB,KAAK,CAAC5D,MAAN,CAAa6D,EAAG,EAA1C,CAA/B,CAAA;;MACA,MAAMC,QAAQ,GAAGF,KAAK,IAAI;QACxB,IAAK3C,CAAAA,mBAAL,CAAyBC,eAAzB,GAA2C0C,KAAK,CAAC5D,MAAN,CAAakD,SAAxD,CAAA;;EACA,MAAA,IAAA,CAAKa,QAAL,CAAcJ,aAAa,CAACC,KAAD,CAA3B,CAAA,CAAA;OAFF,CAAA;;MAKA,MAAMzC,eAAe,GAAG,CAAC,IAAKR,CAAAA,YAAL,IAAqBoB,QAAQ,CAACiC,eAA/B,EAAgDV,SAAxE,CAAA;EACA,IAAA,MAAMW,eAAe,GAAG9C,eAAe,IAAI,IAAKF,CAAAA,mBAAL,CAAyBE,eAApE,CAAA;EACA,IAAA,IAAA,CAAKF,mBAAL,CAAyBE,eAAzB,GAA2CA,eAA3C,CAAA;;EAEA,IAAA,KAAK,MAAMyC,KAAX,IAAoBH,OAApB,EAA6B;EAC3B,MAAA,IAAI,CAACG,KAAK,CAACM,cAAX,EAA2B;UACzB,IAAKnD,CAAAA,aAAL,GAAqB,IAArB,CAAA;;EACA,QAAA,IAAA,CAAKoD,iBAAL,CAAuBR,aAAa,CAACC,KAAD,CAApC,CAAA,CAAA;;EAEA,QAAA,SAAA;EACD,OAAA;;EAED,MAAA,MAAMQ,wBAAwB,GAAGR,KAAK,CAAC5D,MAAN,CAAakD,SAAb,IAA0B,IAAKjC,CAAAA,mBAAL,CAAyBC,eAApF,CAR2B;;QAU3B,IAAI+C,eAAe,IAAIG,wBAAvB,EAAiD;EAC/CN,QAAAA,QAAQ,CAACF,KAAD,CAAR,CAD+C;;UAG/C,IAAI,CAACzC,eAAL,EAAsB;EACpB,UAAA,OAAA;EACD,SAAA;;EAED,QAAA,SAAA;EACD,OAlB0B;;;EAqB3B,MAAA,IAAI,CAAC8C,eAAD,IAAoB,CAACG,wBAAzB,EAAmD;UACjDN,QAAQ,CAACF,KAAD,CAAR,CAAA;EACD,OAAA;EACF,KAAA;EACF,GAAA;;EAEDvC,EAAAA,gCAAgC,GAAG;EACjC,IAAA,IAAA,CAAKb,YAAL,GAAoB,IAAIC,GAAJ,EAApB,CAAA;EACA,IAAA,IAAA,CAAKC,mBAAL,GAA2B,IAAID,GAAJ,EAA3B,CAAA;EAEA,IAAA,MAAM4D,WAAW,GAAGC,+BAAc,CAACC,IAAf,CAAoBnF,qBAApB,EAA2C,IAAKkD,CAAAA,OAAL,CAAatC,MAAxD,CAApB,CAAA;;EAEA,IAAA,KAAK,MAAMwE,MAAX,IAAqBH,WAArB,EAAkC;EAChC;QACA,IAAI,CAACG,MAAM,CAAC3B,IAAR,IAAgB4B,gBAAU,CAACD,MAAD,CAA9B,EAAwC;EACtC,QAAA,SAAA;EACD,OAAA;;EAED,MAAA,MAAM7B,iBAAiB,GAAG2B,+BAAc,CAACI,OAAf,CAAuBF,MAAM,CAAC3B,IAA9B,EAAoC,IAAA,CAAKhC,QAAzC,CAA1B,CANgC;;EAShC,MAAA,IAAI8D,eAAS,CAAChC,iBAAD,CAAb,EAAkC;UAChC,IAAKnC,CAAAA,YAAL,CAAkBoE,GAAlB,CAAsBJ,MAAM,CAAC3B,IAA7B,EAAmC2B,MAAnC,CAAA,CAAA;;UACA,IAAK9D,CAAAA,mBAAL,CAAyBkE,GAAzB,CAA6BJ,MAAM,CAAC3B,IAApC,EAA0CF,iBAA1C,CAAA,CAAA;EACD,OAAA;EACF,KAAA;EACF,GAAA;;IAEDoB,QAAQ,CAAC/D,MAAD,EAAS;EACf,IAAA,IAAI,IAAKe,CAAAA,aAAL,KAAuBf,MAA3B,EAAmC;EACjC,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKmE,iBAAL,CAAuB,IAAK7B,CAAAA,OAAL,CAAatC,MAApC,CAAA,CAAA;;MACA,IAAKe,CAAAA,aAAL,GAAqBf,MAArB,CAAA;EACAA,IAAAA,MAAM,CAAC6E,SAAP,CAAiBC,GAAjB,CAAqB5F,iBAArB,CAAA,CAAA;;MACA,IAAK6F,CAAAA,gBAAL,CAAsB/E,MAAtB,CAAA,CAAA;;EAEAuC,IAAAA,6BAAY,CAACyC,OAAb,CAAqB,KAAKnE,QAA1B,EAAoC/B,cAApC,EAAoD;EAAEmG,MAAAA,aAAa,EAAEjF,MAAAA;OAArE,CAAA,CAAA;EACD,GAAA;;IAED+E,gBAAgB,CAAC/E,MAAD,EAAS;EACvB;MACA,IAAIA,MAAM,CAAC6E,SAAP,CAAiBK,QAAjB,CAA0BjG,wBAA1B,CAAJ,EAAyD;EACvDqF,MAAAA,+BAAc,CAACI,OAAf,CAAuB/E,wBAAvB,EAAiDK,MAAM,CAACmF,OAAP,CAAezF,iBAAf,CAAjD,CACGmF,CAAAA,SADH,CACaC,GADb,CACiB5F,iBADjB,CAAA,CAAA;EAEA,MAAA,OAAA;EACD,KAAA;;MAED,KAAK,MAAMkG,SAAX,IAAwBd,+BAAc,CAACe,OAAf,CAAuBrF,MAAvB,EAA+BX,uBAA/B,CAAxB,EAAiF;EAC/E;EACA;QACA,KAAK,MAAMiG,IAAX,IAAmBhB,+BAAc,CAACiB,IAAf,CAAoBH,SAApB,EAA+B3F,mBAA/B,CAAnB,EAAwE;EACtE6F,QAAAA,IAAI,CAACT,SAAL,CAAeC,GAAf,CAAmB5F,iBAAnB,CAAA,CAAA;EACD,OAAA;EACF,KAAA;EACF,GAAA;;IAEDiF,iBAAiB,CAACqB,MAAD,EAAS;EACxBA,IAAAA,MAAM,CAACX,SAAP,CAAiBY,MAAjB,CAAwBvG,iBAAxB,CAAA,CAAA;EAEA,IAAA,MAAMwG,WAAW,GAAGpB,+BAAc,CAACC,IAAf,CAAqB,CAAEnF,EAAAA,qBAAsB,CAAGF,CAAAA,EAAAA,iBAAkB,CAAlE,CAAA,EAAqEsG,MAArE,CAApB,CAAA;;EACA,IAAA,KAAK,MAAMG,IAAX,IAAmBD,WAAnB,EAAgC;EAC9BC,MAAAA,IAAI,CAACd,SAAL,CAAeY,MAAf,CAAsBvG,iBAAtB,CAAA,CAAA;EACD,KAAA;EACF,GAvMmC;;;IA0Md,OAAf0G,eAAe,CAACrF,MAAD,EAAS;MAC7B,OAAO,IAAA,CAAKsF,IAAL,CAAU,YAAY;QAC3B,MAAMC,IAAI,GAAG3F,SAAS,CAAC4F,mBAAV,CAA8B,IAA9B,EAAoCxF,MAApC,CAAb,CAAA;;EAEA,MAAA,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,QAAA,OAAA;EACD,OAAA;;EAED,MAAA,IAAIuF,IAAI,CAACvF,MAAD,CAAJ,KAAiByF,SAAjB,IAA8BzF,MAAM,CAAC0F,UAAP,CAAkB,GAAlB,CAA9B,IAAwD1F,MAAM,KAAK,aAAvE,EAAsF;EACpF,QAAA,MAAM,IAAI2F,SAAJ,CAAe,CAAmB3F,iBAAAA,EAAAA,MAAO,GAAzC,CAAN,CAAA;EACD,OAAA;;QAEDuF,IAAI,CAACvF,MAAD,CAAJ,EAAA,CAAA;EACD,KAZM,CAAP,CAAA;EAaD,GAAA;;EAxNmC,CAAA;EA2NtC;EACA;EACA;;;AAEAgC,+BAAY,CAACE,EAAb,CAAgBO,MAAhB,EAAwBhE,mBAAxB,EAA6C,MAAM;IACjD,KAAK,MAAMmH,GAAX,IAAkB7B,+BAAc,CAACC,IAAf,CAAoBpF,iBAApB,CAAlB,EAA0D;MACxDgB,SAAS,CAAC4F,mBAAV,CAA8BI,GAA9B,CAAA,CAAA;EACD,GAAA;EACF,CAJD,CAAA,CAAA;EAMA;EACA;EACA;;AAEAC,0BAAkB,CAACjG,SAAD,CAAlB;;;;;;;;"}