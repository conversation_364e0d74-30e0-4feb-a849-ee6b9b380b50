{"version": 3, "file": "tooltip.js", "sources": ["../src/tooltip.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport { defineJQueryPlugin, findShadowRoot, getElement, getUID, isRTL, noop } from './util/index'\nimport { DefaultAllowlist } from './util/sanitizer'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport BaseComponent from './base-component'\nimport TemplateFactory from './util/template-factory'\n\n/**\n * Constants\n */\n\nconst NAME = 'tooltip'\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\nconst SELECTOR_MODAL = `.${CLASS_NAME_MODAL}`\n\nconst EVENT_MODAL_HIDE = 'hide.bs.modal'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\nconst EVENT_HIDE = 'hide'\nconst EVENT_HIDDEN = 'hidden'\nconst EVENT_SHOW = 'show'\nconst EVENT_SHOWN = 'shown'\nconst EVENT_INSERTED = 'inserted'\nconst EVENT_CLICK = 'click'\nconst EVENT_FOCUSIN = 'focusin'\nconst EVENT_FOCUSOUT = 'focusout'\nconst EVENT_MOUSEENTER = 'mouseenter'\nconst EVENT_MOUSELEAVE = 'mouseleave'\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  allowList: DefaultAllowlist,\n  animation: true,\n  boundary: 'clippingParents',\n  container: false,\n  customClass: '',\n  delay: 0,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  html: false,\n  offset: [0, 0],\n  placement: 'top',\n  popperConfig: null,\n  sanitize: true,\n  sanitizeFn: null,\n  selector: false,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n            '<div class=\"tooltip-arrow\"></div>' +\n            '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  title: '',\n  trigger: 'hover focus'\n}\n\nconst DefaultType = {\n  allowList: 'object',\n  animation: 'boolean',\n  boundary: '(string|element)',\n  container: '(string|element|boolean)',\n  customClass: '(string|function)',\n  delay: '(number|object)',\n  fallbackPlacements: 'array',\n  html: 'boolean',\n  offset: '(array|string|function)',\n  placement: '(string|function)',\n  popperConfig: '(null|object|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  selector: '(string|boolean)',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string'\n}\n\n/**\n * Class definition\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element, config)\n\n    // Private\n    this._isEnabled = true\n    this._timeout = 0\n    this._isHovered = null\n    this._activeTrigger = {}\n    this._popper = null\n    this._templateFactory = null\n    this._newContent = null\n\n    // Protected\n    this.tip = null\n\n    this._setListeners()\n\n    if (!this._config.selector) {\n      this._fixTitle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle() {\n    if (!this._isEnabled) {\n      return\n    }\n\n    this._activeTrigger.click = !this._activeTrigger.click\n    if (this._isShown()) {\n      this._leave()\n      return\n    }\n\n    this._enter()\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n\n    if (this._element.getAttribute('data-bs-original-title')) {\n      this._element.setAttribute('title', this._element.getAttribute('data-bs-original-title'))\n    }\n\n    this._disposePopper()\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this._isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOW))\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = (shadowRoot || this._element.ownerDocument.documentElement).contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    // todo v6 remove this OR make it optional\n    this._disposePopper()\n\n    const tip = this._getTipElement()\n\n    this._element.setAttribute('aria-describedby', tip.getAttribute('id'))\n\n    const { container } = this._config\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.append(tip)\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_INSERTED))\n    }\n\n    this._popper = this._createPopper(tip)\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    const complete = () => {\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOWN))\n\n      if (this._isHovered === false) {\n        this._leave()\n      }\n\n      this._isHovered = false\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  hide() {\n    if (!this._isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDE))\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const tip = this._getTipElement()\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n    this._isHovered = null // it is a trick to support manual triggering\n\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (!this._isHovered) {\n        this._disposePopper()\n      }\n\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDDEN))\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  update() {\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n  _isWithContent() {\n    return Boolean(this._getTitle())\n  }\n\n  _getTipElement() {\n    if (!this.tip) {\n      this.tip = this._createTipElement(this._newContent || this._getContentForTemplate())\n    }\n\n    return this.tip\n  }\n\n  _createTipElement(content) {\n    const tip = this._getTemplateFactory(content).toHtml()\n\n    // todo: remove this check on v6\n    if (!tip) {\n      return null\n    }\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n    // todo: on v6 the following can be achieved with CSS only\n    tip.classList.add(`bs-${this.constructor.NAME}-auto`)\n\n    const tipId = getUID(this.constructor.NAME).toString()\n\n    tip.setAttribute('id', tipId)\n\n    if (this._isAnimated()) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    return tip\n  }\n\n  setContent(content) {\n    this._newContent = content\n    if (this._isShown()) {\n      this._disposePopper()\n      this.show()\n    }\n  }\n\n  _getTemplateFactory(content) {\n    if (this._templateFactory) {\n      this._templateFactory.changeContent(content)\n    } else {\n      this._templateFactory = new TemplateFactory({\n        ...this._config,\n        // the `content` var has to be after `this._config`\n        // to override config.content in case of popover\n        content,\n        extraClass: this._resolvePossibleFunction(this._config.customClass)\n      })\n    }\n\n    return this._templateFactory\n  }\n\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TOOLTIP_INNER]: this._getTitle()\n    }\n  }\n\n  _getTitle() {\n    return this._resolvePossibleFunction(this._config.title) || this._element.getAttribute('data-bs-original-title')\n  }\n\n  // Private\n  _initializeOnDelegatedTarget(event) {\n    return this.constructor.getOrCreateInstance(event.delegateTarget, this._getDelegateConfig())\n  }\n\n  _isAnimated() {\n    return this._config.animation || (this.tip && this.tip.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _isShown() {\n    return this.tip && this.tip.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _createPopper(tip) {\n    const placement = typeof this._config.placement === 'function' ?\n      this._config.placement.call(this, tip, this._element) :\n      this._config.placement\n    const attachment = AttachmentMap[placement.toUpperCase()]\n    return Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _resolvePossibleFunction(arg) {\n    return typeof arg === 'function' ? arg.call(this._element) : arg\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            fallbackPlacements: this._config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this._config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'preSetPlacement',\n          enabled: true,\n          phase: 'beforeMain',\n          fn: data => {\n            // Pre-set Popper's placement attribute in order to read the arrow sizes properly.\n            // Otherwise, Popper mixes up the width and height dimensions since the initial arrow style is for top placement\n            this._getTipElement().setAttribute('data-popper-placement', data.state.placement)\n          }\n        }\n      ]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ')\n\n    for (const trigger of triggers) {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.eventName(EVENT_CLICK), this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context.toggle()\n        })\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSEENTER) :\n          this.constructor.eventName(EVENT_FOCUSIN)\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSELEAVE) :\n          this.constructor.eventName(EVENT_FOCUSOUT)\n\n        EventHandler.on(this._element, eventIn, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER] = true\n          context._enter()\n        })\n        EventHandler.on(this._element, eventOut, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER] =\n            context._element.contains(event.relatedTarget)\n\n          context._leave()\n        })\n      }\n    }\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n\n    if (!title) {\n      return\n    }\n\n    if (!this._element.getAttribute('aria-label') && !this._element.textContent.trim()) {\n      this._element.setAttribute('aria-label', title)\n    }\n\n    this._element.setAttribute('data-bs-original-title', title) // DO NOT USE IT. Is only for backwards compatibility\n    this._element.removeAttribute('title')\n  }\n\n  _enter() {\n    if (this._isShown() || this._isHovered) {\n      this._isHovered = true\n      return\n    }\n\n    this._isHovered = true\n\n    this._setTimeout(() => {\n      if (this._isHovered) {\n        this.show()\n      }\n    }, this._config.delay.show)\n  }\n\n  _leave() {\n    if (this._isWithActiveTrigger()) {\n      return\n    }\n\n    this._isHovered = false\n\n    this._setTimeout(() => {\n      if (!this._isHovered) {\n        this.hide()\n      }\n    }, this._config.delay.hide)\n  }\n\n  _setTimeout(handler, timeout) {\n    clearTimeout(this._timeout)\n    this._timeout = setTimeout(handler, timeout)\n  }\n\n  _isWithActiveTrigger() {\n    return Object.values(this._activeTrigger).includes(true)\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    for (const dataAttribute of Object.keys(dataAttributes)) {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttribute)) {\n        delete dataAttributes[dataAttribute]\n      }\n    }\n\n    config = {\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    config.container = config.container === false ? document.body : getElement(config.container)\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    for (const key in this._config) {\n      if (this.constructor.Default[key] !== this._config[key]) {\n        config[key] = this._config[key]\n      }\n    }\n\n    config.selector = false\n    config.trigger = 'manual'\n\n    // In the future can be replaced with:\n    // const keysWithDifferentValues = Object.entries(this._config).filter(entry => this.constructor.Default[entry[0]] !== this._config[entry[0]])\n    // `Object.fromEntries(keysWithDifferentValues)`\n    return config\n  }\n\n  _disposePopper() {\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n\n    if (this.tip) {\n      this.tip.remove()\n      this.tip = null\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tooltip.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tooltip)\n\nexport default Tooltip\n"], "names": ["NAME", "DISALLOWED_ATTRIBUTES", "Set", "CLASS_NAME_FADE", "CLASS_NAME_MODAL", "CLASS_NAME_SHOW", "SELECTOR_TOOLTIP_INNER", "SELECTOR_MODAL", "EVENT_MODAL_HIDE", "TRIGGER_HOVER", "TRIGGER_FOCUS", "TRIGGER_CLICK", "TRIGGER_MANUAL", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_INSERTED", "EVENT_CLICK", "EVENT_FOCUSIN", "EVENT_FOCUSOUT", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "AttachmentMap", "AUTO", "TOP", "RIGHT", "isRTL", "BOTTOM", "LEFT", "<PERSON><PERSON><PERSON>", "allowList", "DefaultAllowlist", "animation", "boundary", "container", "customClass", "delay", "fallbackPlacements", "html", "offset", "placement", "popperConfig", "sanitize", "sanitizeFn", "selector", "template", "title", "trigger", "DefaultType", "<PERSON><PERSON><PERSON>", "BaseComponent", "constructor", "element", "config", "<PERSON><PERSON>", "TypeError", "_isEnabled", "_timeout", "_isHovered", "_activeTrigger", "_popper", "_templateFactory", "_newContent", "tip", "_setListeners", "_config", "_fixTitle", "enable", "disable", "toggle<PERSON>nabled", "toggle", "click", "_isShown", "_leave", "_enter", "dispose", "clearTimeout", "EventHandler", "off", "_element", "closest", "_hideModalHandler", "getAttribute", "setAttribute", "_disposePopper", "show", "style", "display", "Error", "_isWithContent", "showEvent", "eventName", "shadowRoot", "findShadowRoot", "isInTheDom", "ownerDocument", "documentElement", "contains", "defaultPrevented", "_getTipElement", "append", "_createPopper", "classList", "add", "document", "concat", "body", "children", "on", "noop", "complete", "_queueCallback", "_isAnimated", "hide", "hideEvent", "remove", "_isWithActiveTrigger", "removeAttribute", "update", "Boolean", "_getTitle", "_createTipElement", "_getContentForTemplate", "content", "_getTemplateFactory", "toHtml", "tipId", "getUID", "toString", "<PERSON><PERSON><PERSON><PERSON>", "changeContent", "TemplateFactory", "extraClass", "_resolvePossibleFunction", "_initializeOnDelegatedTarget", "event", "getOrCreateInstance", "<PERSON><PERSON><PERSON><PERSON>", "_getDelegateConfig", "call", "attachment", "toUpperCase", "createPopper", "_getPopperConfig", "_getOffset", "split", "map", "value", "Number", "parseInt", "popperData", "arg", "defaultBsPopperConfig", "modifiers", "name", "options", "enabled", "phase", "fn", "data", "state", "triggers", "context", "eventIn", "eventOut", "type", "relatedTarget", "textContent", "trim", "_setTimeout", "handler", "timeout", "setTimeout", "Object", "values", "includes", "_getConfig", "dataAttributes", "Manipulator", "getDataAttributes", "dataAttribute", "keys", "has", "_mergeConfigObj", "_configAfterMerge", "_typeCheckConfig", "getElement", "key", "destroy", "jQueryInterface", "each", "defineJQueryPlugin"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAUA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,SAAb,CAAA;EACA,MAAMC,qBAAqB,GAAG,IAAIC,GAAJ,CAAQ,CAAC,UAAD,EAAa,WAAb,EAA0B,YAA1B,CAAR,CAA9B,CAAA;EAEA,MAAMC,eAAe,GAAG,MAAxB,CAAA;EACA,MAAMC,gBAAgB,GAAG,OAAzB,CAAA;EACA,MAAMC,eAAe,GAAG,MAAxB,CAAA;EAEA,MAAMC,sBAAsB,GAAG,gBAA/B,CAAA;EACA,MAAMC,cAAc,GAAI,CAAGH,CAAAA,EAAAA,gBAAiB,CAA5C,CAAA,CAAA;EAEA,MAAMI,gBAAgB,GAAG,eAAzB,CAAA;EAEA,MAAMC,aAAa,GAAG,OAAtB,CAAA;EACA,MAAMC,aAAa,GAAG,OAAtB,CAAA;EACA,MAAMC,aAAa,GAAG,OAAtB,CAAA;EACA,MAAMC,cAAc,GAAG,QAAvB,CAAA;EAEA,MAAMC,UAAU,GAAG,MAAnB,CAAA;EACA,MAAMC,YAAY,GAAG,QAArB,CAAA;EACA,MAAMC,UAAU,GAAG,MAAnB,CAAA;EACA,MAAMC,WAAW,GAAG,OAApB,CAAA;EACA,MAAMC,cAAc,GAAG,UAAvB,CAAA;EACA,MAAMC,WAAW,GAAG,OAApB,CAAA;EACA,MAAMC,aAAa,GAAG,SAAtB,CAAA;EACA,MAAMC,cAAc,GAAG,UAAvB,CAAA;EACA,MAAMC,gBAAgB,GAAG,YAAzB,CAAA;EACA,MAAMC,gBAAgB,GAAG,YAAzB,CAAA;EAEA,MAAMC,aAAa,GAAG;EACpBC,EAAAA,IAAI,EAAE,MADc;EAEpBC,EAAAA,GAAG,EAAE,KAFe;EAGpBC,EAAAA,KAAK,EAAEC,WAAK,EAAK,GAAA,MAAL,GAAc,OAHN;EAIpBC,EAAAA,MAAM,EAAE,QAJY;EAKpBC,EAAAA,IAAI,EAAEF,WAAK,EAAK,GAAA,OAAL,GAAe,MAAA;EALN,CAAtB,CAAA;EAQA,MAAMG,OAAO,GAAG;EACdC,EAAAA,SAAS,EAAEC,0BADG;EAEdC,EAAAA,SAAS,EAAE,IAFG;EAGdC,EAAAA,QAAQ,EAAE,iBAHI;EAIdC,EAAAA,SAAS,EAAE,KAJG;EAKdC,EAAAA,WAAW,EAAE,EALC;EAMdC,EAAAA,KAAK,EAAE,CANO;IAOdC,kBAAkB,EAAE,CAAC,KAAD,EAAQ,OAAR,EAAiB,QAAjB,EAA2B,MAA3B,CAPN;EAQdC,EAAAA,IAAI,EAAE,KARQ;EASdC,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CATM;EAUdC,EAAAA,SAAS,EAAE,KAVG;EAWdC,EAAAA,YAAY,EAAE,IAXA;EAYdC,EAAAA,QAAQ,EAAE,IAZI;EAadC,EAAAA,UAAU,EAAE,IAbE;EAcdC,EAAAA,QAAQ,EAAE,KAdI;EAedC,EAAAA,QAAQ,EAAE,sCACA,GAAA,mCADA,GAEA,mCAFA,GAGA,QAlBI;EAmBdC,EAAAA,KAAK,EAAE,EAnBO;EAoBdC,EAAAA,OAAO,EAAE,aAAA;EApBK,CAAhB,CAAA;EAuBA,MAAMC,WAAW,GAAG;EAClBlB,EAAAA,SAAS,EAAE,QADO;EAElBE,EAAAA,SAAS,EAAE,SAFO;EAGlBC,EAAAA,QAAQ,EAAE,kBAHQ;EAIlBC,EAAAA,SAAS,EAAE,0BAJO;EAKlBC,EAAAA,WAAW,EAAE,mBALK;EAMlBC,EAAAA,KAAK,EAAE,iBANW;EAOlBC,EAAAA,kBAAkB,EAAE,OAPF;EAQlBC,EAAAA,IAAI,EAAE,SARY;EASlBC,EAAAA,MAAM,EAAE,yBATU;EAUlBC,EAAAA,SAAS,EAAE,mBAVO;EAWlBC,EAAAA,YAAY,EAAE,wBAXI;EAYlBC,EAAAA,QAAQ,EAAE,SAZQ;EAalBC,EAAAA,UAAU,EAAE,iBAbM;EAclBC,EAAAA,QAAQ,EAAE,kBAdQ;EAelBC,EAAAA,QAAQ,EAAE,QAfQ;EAgBlBC,EAAAA,KAAK,EAAE,2BAhBW;EAiBlBC,EAAAA,OAAO,EAAE,QAAA;EAjBS,CAApB,CAAA;EAoBA;EACA;EACA;;EAEA,MAAME,OAAN,SAAsBC,8BAAtB,CAAoC;EAClCC,EAAAA,WAAW,CAACC,OAAD,EAAUC,MAAV,EAAkB;EAC3B,IAAA,IAAI,OAAOC,iBAAP,KAAkB,WAAtB,EAAmC;EACjC,MAAA,MAAM,IAAIC,SAAJ,CAAc,8DAAd,CAAN,CAAA;EACD,KAAA;;EAED,IAAA,KAAA,CAAMH,OAAN,EAAeC,MAAf,CAAA,CAL2B;;MAQ3B,IAAKG,CAAAA,UAAL,GAAkB,IAAlB,CAAA;MACA,IAAKC,CAAAA,QAAL,GAAgB,CAAhB,CAAA;MACA,IAAKC,CAAAA,UAAL,GAAkB,IAAlB,CAAA;MACA,IAAKC,CAAAA,cAAL,GAAsB,EAAtB,CAAA;MACA,IAAKC,CAAAA,OAAL,GAAe,IAAf,CAAA;MACA,IAAKC,CAAAA,gBAAL,GAAwB,IAAxB,CAAA;EACA,IAAA,IAAA,CAAKC,WAAL,GAAmB,IAAnB,CAd2B;;MAiB3B,IAAKC,CAAAA,GAAL,GAAW,IAAX,CAAA;;EAEA,IAAA,IAAA,CAAKC,aAAL,EAAA,CAAA;;EAEA,IAAA,IAAI,CAAC,IAAA,CAAKC,OAAL,CAAarB,QAAlB,EAA4B;EAC1B,MAAA,IAAA,CAAKsB,SAAL,EAAA,CAAA;EACD,KAAA;EACF,GAzBiC;;;EA4BhB,EAAA,WAAPrC,OAAO,GAAG;EACnB,IAAA,OAAOA,OAAP,CAAA;EACD,GAAA;;EAEqB,EAAA,WAAXmB,WAAW,GAAG;EACvB,IAAA,OAAOA,WAAP,CAAA;EACD,GAAA;;EAEc,EAAA,WAAJjD,IAAI,GAAG;EAChB,IAAA,OAAOA,IAAP,CAAA;EACD,GAtCiC;;;EAyClCoE,EAAAA,MAAM,GAAG;MACP,IAAKX,CAAAA,UAAL,GAAkB,IAAlB,CAAA;EACD,GAAA;;EAEDY,EAAAA,OAAO,GAAG;MACR,IAAKZ,CAAAA,UAAL,GAAkB,KAAlB,CAAA;EACD,GAAA;;EAEDa,EAAAA,aAAa,GAAG;EACd,IAAA,IAAA,CAAKb,UAAL,GAAkB,CAAC,IAAA,CAAKA,UAAxB,CAAA;EACD,GAAA;;EAEDc,EAAAA,MAAM,GAAG;MACP,IAAI,CAAC,IAAKd,CAAAA,UAAV,EAAsB;EACpB,MAAA,OAAA;EACD,KAAA;;MAED,IAAKG,CAAAA,cAAL,CAAoBY,KAApB,GAA4B,CAAC,IAAKZ,CAAAA,cAAL,CAAoBY,KAAjD,CAAA;;MACA,IAAI,IAAA,CAAKC,QAAL,EAAJ,EAAqB;EACnB,MAAA,IAAA,CAAKC,MAAL,EAAA,CAAA;;EACA,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKC,MAAL,EAAA,CAAA;EACD,GAAA;;EAEDC,EAAAA,OAAO,GAAG;MACRC,YAAY,CAAC,IAAKnB,CAAAA,QAAN,CAAZ,CAAA;EAEAoB,IAAAA,6BAAY,CAACC,GAAb,CAAiB,IAAA,CAAKC,QAAL,CAAcC,OAAd,CAAsB1E,cAAtB,CAAjB,EAAwDC,gBAAxD,EAA0E,KAAK0E,iBAA/E,CAAA,CAAA;;EAEA,IAAA,IAAI,KAAKF,QAAL,CAAcG,YAAd,CAA2B,wBAA3B,CAAJ,EAA0D;EACxD,MAAA,IAAA,CAAKH,QAAL,CAAcI,YAAd,CAA2B,OAA3B,EAAoC,IAAKJ,CAAAA,QAAL,CAAcG,YAAd,CAA2B,wBAA3B,CAApC,CAAA,CAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKE,cAAL,EAAA,CAAA;;EACA,IAAA,KAAA,CAAMT,OAAN,EAAA,CAAA;EACD,GAAA;;EAEDU,EAAAA,IAAI,GAAG;MACL,IAAI,IAAA,CAAKN,QAAL,CAAcO,KAAd,CAAoBC,OAApB,KAAgC,MAApC,EAA4C;EAC1C,MAAA,MAAM,IAAIC,KAAJ,CAAU,qCAAV,CAAN,CAAA;EACD,KAAA;;EAED,IAAA,IAAI,EAAE,IAAKC,CAAAA,cAAL,MAAyB,IAAKjC,CAAAA,UAAhC,CAAJ,EAAiD;EAC/C,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,MAAMkC,SAAS,GAAGb,6BAAY,CAAC9B,OAAb,CAAqB,IAAKgC,CAAAA,QAA1B,EAAoC,IAAA,CAAK5B,WAAL,CAAiBwC,SAAjB,CAA2B7E,UAA3B,CAApC,CAAlB,CAAA;EACA,IAAA,MAAM8E,UAAU,GAAGC,oBAAc,CAAC,IAAA,CAAKd,QAAN,CAAjC,CAAA;;EACA,IAAA,MAAMe,UAAU,GAAG,CAACF,UAAU,IAAI,KAAKb,QAAL,CAAcgB,aAAd,CAA4BC,eAA3C,EAA4DC,QAA5D,CAAqE,IAAA,CAAKlB,QAA1E,CAAnB,CAAA;;EAEA,IAAA,IAAIW,SAAS,CAACQ,gBAAV,IAA8B,CAACJ,UAAnC,EAA+C;EAC7C,MAAA,OAAA;EACD,KAfI;;;EAkBL,IAAA,IAAA,CAAKV,cAAL,EAAA,CAAA;;EAEA,IAAA,MAAMrB,GAAG,GAAG,IAAKoC,CAAAA,cAAL,EAAZ,CAAA;;MAEA,IAAKpB,CAAAA,QAAL,CAAcI,YAAd,CAA2B,kBAA3B,EAA+CpB,GAAG,CAACmB,YAAJ,CAAiB,IAAjB,CAA/C,CAAA,CAAA;;MAEA,MAAM;EAAEhD,MAAAA,SAAAA;EAAF,KAAA,GAAgB,KAAK+B,OAA3B,CAAA;;EAEA,IAAA,IAAI,CAAC,IAAA,CAAKc,QAAL,CAAcgB,aAAd,CAA4BC,eAA5B,CAA4CC,QAA5C,CAAqD,IAAKlC,CAAAA,GAA1D,CAAL,EAAqE;QACnE7B,SAAS,CAACkE,MAAV,CAAiBrC,GAAjB,CAAA,CAAA;EACAc,MAAAA,6BAAY,CAAC9B,OAAb,CAAqB,IAAA,CAAKgC,QAA1B,EAAoC,IAAK5B,CAAAA,WAAL,CAAiBwC,SAAjB,CAA2B3E,cAA3B,CAApC,CAAA,CAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAK4C,OAAL,GAAe,IAAA,CAAKyC,aAAL,CAAmBtC,GAAnB,CAAf,CAAA;EAEAA,IAAAA,GAAG,CAACuC,SAAJ,CAAcC,GAAd,CAAkBnG,eAAlB,EAjCK;EAoCL;EACA;EACA;;EACA,IAAA,IAAI,cAAkBoG,IAAAA,QAAQ,CAACR,eAA/B,EAAgD;EAC9C,MAAA,KAAK,MAAM5C,OAAX,IAAsB,EAAA,CAAGqD,MAAH,CAAU,GAAGD,QAAQ,CAACE,IAAT,CAAcC,QAA3B,CAAtB,EAA4D;EAC1D9B,QAAAA,6BAAY,CAAC+B,EAAb,CAAgBxD,OAAhB,EAAyB,WAAzB,EAAsCyD,UAAtC,CAAA,CAAA;EACD,OAAA;EACF,KAAA;;MAED,MAAMC,QAAQ,GAAG,MAAM;EACrBjC,MAAAA,6BAAY,CAAC9B,OAAb,CAAqB,IAAA,CAAKgC,QAA1B,EAAoC,IAAK5B,CAAAA,WAAL,CAAiBwC,SAAjB,CAA2B5E,WAA3B,CAApC,CAAA,CAAA;;EAEA,MAAA,IAAI,IAAK2C,CAAAA,UAAL,KAAoB,KAAxB,EAA+B;EAC7B,QAAA,IAAA,CAAKe,MAAL,EAAA,CAAA;EACD,OAAA;;QAED,IAAKf,CAAAA,UAAL,GAAkB,KAAlB,CAAA;OAPF,CAAA;;MAUA,IAAKqD,CAAAA,cAAL,CAAoBD,QAApB,EAA8B,KAAK/C,GAAnC,EAAwC,IAAKiD,CAAAA,WAAL,EAAxC,CAAA,CAAA;EACD,GAAA;;EAEDC,EAAAA,IAAI,GAAG;EACL,IAAA,IAAI,CAAC,IAAA,CAAKzC,QAAL,EAAL,EAAsB;EACpB,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,MAAM0C,SAAS,GAAGrC,6BAAY,CAAC9B,OAAb,CAAqB,IAAKgC,CAAAA,QAA1B,EAAoC,IAAA,CAAK5B,WAAL,CAAiBwC,SAAjB,CAA2B/E,UAA3B,CAApC,CAAlB,CAAA;;MACA,IAAIsG,SAAS,CAAChB,gBAAd,EAAgC;EAC9B,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,MAAMnC,GAAG,GAAG,IAAKoC,CAAAA,cAAL,EAAZ,CAAA;;EACApC,IAAAA,GAAG,CAACuC,SAAJ,CAAca,MAAd,CAAqB/G,eAArB,EAXK;EAcL;;EACA,IAAA,IAAI,cAAkBoG,IAAAA,QAAQ,CAACR,eAA/B,EAAgD;EAC9C,MAAA,KAAK,MAAM5C,OAAX,IAAsB,EAAA,CAAGqD,MAAH,CAAU,GAAGD,QAAQ,CAACE,IAAT,CAAcC,QAA3B,CAAtB,EAA4D;EAC1D9B,QAAAA,6BAAY,CAACC,GAAb,CAAiB1B,OAAjB,EAA0B,WAA1B,EAAuCyD,UAAvC,CAAA,CAAA;EACD,OAAA;EACF,KAAA;;EAED,IAAA,IAAA,CAAKlD,cAAL,CAAoBjD,aAApB,CAAA,GAAqC,KAArC,CAAA;EACA,IAAA,IAAA,CAAKiD,cAAL,CAAoBlD,aAApB,CAAA,GAAqC,KAArC,CAAA;EACA,IAAA,IAAA,CAAKkD,cAAL,CAAoBnD,aAApB,CAAA,GAAqC,KAArC,CAAA;EACA,IAAA,IAAA,CAAKkD,UAAL,GAAkB,IAAlB,CAxBK;;MA0BL,MAAMoD,QAAQ,GAAG,MAAM;QACrB,IAAI,IAAA,CAAKM,oBAAL,EAAJ,EAAiC;EAC/B,QAAA,OAAA;EACD,OAAA;;QAED,IAAI,CAAC,IAAK1D,CAAAA,UAAV,EAAsB;EACpB,QAAA,IAAA,CAAK0B,cAAL,EAAA,CAAA;EACD,OAAA;;EAED,MAAA,IAAA,CAAKL,QAAL,CAAcsC,eAAd,CAA8B,kBAA9B,CAAA,CAAA;;EACAxC,MAAAA,6BAAY,CAAC9B,OAAb,CAAqB,IAAA,CAAKgC,QAA1B,EAAoC,IAAK5B,CAAAA,WAAL,CAAiBwC,SAAjB,CAA2B9E,YAA3B,CAApC,CAAA,CAAA;OAVF,CAAA;;MAaA,IAAKkG,CAAAA,cAAL,CAAoBD,QAApB,EAA8B,KAAK/C,GAAnC,EAAwC,IAAKiD,CAAAA,WAAL,EAAxC,CAAA,CAAA;EACD,GAAA;;EAEDM,EAAAA,MAAM,GAAG;MACP,IAAI,IAAA,CAAK1D,OAAT,EAAkB;QAChB,IAAKA,CAAAA,OAAL,CAAa0D,MAAb,EAAA,CAAA;EACD,KAAA;EACF,GAxLiC;;;EA2LlC7B,EAAAA,cAAc,GAAG;EACf,IAAA,OAAO8B,OAAO,CAAC,IAAKC,CAAAA,SAAL,EAAD,CAAd,CAAA;EACD,GAAA;;EAEDrB,EAAAA,cAAc,GAAG;MACf,IAAI,CAAC,IAAKpC,CAAAA,GAAV,EAAe;QACb,IAAKA,CAAAA,GAAL,GAAW,IAAA,CAAK0D,iBAAL,CAAuB,IAAK3D,CAAAA,WAAL,IAAoB,IAAA,CAAK4D,sBAAL,EAA3C,CAAX,CAAA;EACD,KAAA;;EAED,IAAA,OAAO,KAAK3D,GAAZ,CAAA;EACD,GAAA;;IAED0D,iBAAiB,CAACE,OAAD,EAAU;MACzB,MAAM5D,GAAG,GAAG,IAAA,CAAK6D,mBAAL,CAAyBD,OAAzB,CAAkCE,CAAAA,MAAlC,EAAZ,CADyB;;;MAIzB,IAAI,CAAC9D,GAAL,EAAU;EACR,MAAA,OAAO,IAAP,CAAA;EACD,KAAA;;MAEDA,GAAG,CAACuC,SAAJ,CAAca,MAAd,CAAqBjH,eAArB,EAAsCE,eAAtC,CAAA,CARyB;;MAUzB2D,GAAG,CAACuC,SAAJ,CAAcC,GAAd,CAAmB,MAAK,IAAKpD,CAAAA,WAAL,CAAiBpD,IAAK,CAA9C,KAAA,CAAA,CAAA,CAAA;MAEA,MAAM+H,KAAK,GAAGC,YAAM,CAAC,IAAA,CAAK5E,WAAL,CAAiBpD,IAAlB,CAAN,CAA8BiI,QAA9B,EAAd,CAAA;EAEAjE,IAAAA,GAAG,CAACoB,YAAJ,CAAiB,IAAjB,EAAuB2C,KAAvB,CAAA,CAAA;;MAEA,IAAI,IAAA,CAAKd,WAAL,EAAJ,EAAwB;EACtBjD,MAAAA,GAAG,CAACuC,SAAJ,CAAcC,GAAd,CAAkBrG,eAAlB,CAAA,CAAA;EACD,KAAA;;EAED,IAAA,OAAO6D,GAAP,CAAA;EACD,GAAA;;IAEDkE,UAAU,CAACN,OAAD,EAAU;MAClB,IAAK7D,CAAAA,WAAL,GAAmB6D,OAAnB,CAAA;;MACA,IAAI,IAAA,CAAKnD,QAAL,EAAJ,EAAqB;EACnB,MAAA,IAAA,CAAKY,cAAL,EAAA,CAAA;;EACA,MAAA,IAAA,CAAKC,IAAL,EAAA,CAAA;EACD,KAAA;EACF,GAAA;;IAEDuC,mBAAmB,CAACD,OAAD,EAAU;MAC3B,IAAI,IAAA,CAAK9D,gBAAT,EAA2B;EACzB,MAAA,IAAA,CAAKA,gBAAL,CAAsBqE,aAAtB,CAAoCP,OAApC,CAAA,CAAA;EACD,KAFD,MAEO;QACL,IAAK9D,CAAAA,gBAAL,GAAwB,IAAIsE,gCAAJ,CAAoB,EAC1C,GAAG,KAAKlE,OADkC;EAE1C;EACA;UACA0D,OAJ0C;EAK1CS,QAAAA,UAAU,EAAE,IAAKC,CAAAA,wBAAL,CAA8B,IAAKpE,CAAAA,OAAL,CAAa9B,WAA3C,CAAA;EAL8B,OAApB,CAAxB,CAAA;EAOD,KAAA;;EAED,IAAA,OAAO,KAAK0B,gBAAZ,CAAA;EACD,GAAA;;EAED6D,EAAAA,sBAAsB,GAAG;MACvB,OAAO;QACL,CAACrH,sBAAD,GAA0B,IAAA,CAAKmH,SAAL,EAAA;OAD5B,CAAA;EAGD,GAAA;;EAEDA,EAAAA,SAAS,GAAG;EACV,IAAA,OAAO,IAAKa,CAAAA,wBAAL,CAA8B,IAAA,CAAKpE,OAAL,CAAanB,KAA3C,CAAqD,IAAA,IAAA,CAAKiC,QAAL,CAAcG,YAAd,CAA2B,wBAA3B,CAA5D,CAAA;EACD,GA9PiC;;;IAiQlCoD,4BAA4B,CAACC,KAAD,EAAQ;EAClC,IAAA,OAAO,IAAKpF,CAAAA,WAAL,CAAiBqF,mBAAjB,CAAqCD,KAAK,CAACE,cAA3C,EAA2D,IAAA,CAAKC,kBAAL,EAA3D,CAAP,CAAA;EACD,GAAA;;EAED1B,EAAAA,WAAW,GAAG;EACZ,IAAA,OAAO,KAAK/C,OAAL,CAAajC,SAAb,IAA2B,KAAK+B,GAAL,IAAY,IAAKA,CAAAA,GAAL,CAASuC,SAAT,CAAmBL,QAAnB,CAA4B/F,eAA5B,CAA9C,CAAA;EACD,GAAA;;EAEDsE,EAAAA,QAAQ,GAAG;MACT,OAAO,IAAA,CAAKT,GAAL,IAAY,IAAKA,CAAAA,GAAL,CAASuC,SAAT,CAAmBL,QAAnB,CAA4B7F,eAA5B,CAAnB,CAAA;EACD,GAAA;;IAEDiG,aAAa,CAACtC,GAAD,EAAM;EACjB,IAAA,MAAMvB,SAAS,GAAG,OAAO,IAAA,CAAKyB,OAAL,CAAazB,SAApB,KAAkC,UAAlC,GAChB,IAAKyB,CAAAA,OAAL,CAAazB,SAAb,CAAuBmG,IAAvB,CAA4B,IAA5B,EAAkC5E,GAAlC,EAAuC,IAAA,CAAKgB,QAA5C,CADgB,GAEhB,IAAA,CAAKd,OAAL,CAAazB,SAFf,CAAA;MAGA,MAAMoG,UAAU,GAAGtH,aAAa,CAACkB,SAAS,CAACqG,WAAV,EAAD,CAAhC,CAAA;EACA,IAAA,OAAOvF,iBAAM,CAACwF,YAAP,CAAoB,KAAK/D,QAAzB,EAAmChB,GAAnC,EAAwC,IAAKgF,CAAAA,gBAAL,CAAsBH,UAAtB,CAAxC,CAAP,CAAA;EACD,GAAA;;EAEDI,EAAAA,UAAU,GAAG;MACX,MAAM;EAAEzG,MAAAA,MAAAA;EAAF,KAAA,GAAa,KAAK0B,OAAxB,CAAA;;EAEA,IAAA,IAAI,OAAO1B,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,MAAA,OAAOA,MAAM,CAAC0G,KAAP,CAAa,GAAb,CAAA,CAAkBC,GAAlB,CAAsBC,KAAK,IAAIC,MAAM,CAACC,QAAP,CAAgBF,KAAhB,EAAuB,EAAvB,CAA/B,CAAP,CAAA;EACD,KAAA;;EAED,IAAA,IAAI,OAAO5G,MAAP,KAAkB,UAAtB,EAAkC;QAChC,OAAO+G,UAAU,IAAI/G,MAAM,CAAC+G,UAAD,EAAa,IAAA,CAAKvE,QAAlB,CAA3B,CAAA;EACD,KAAA;;EAED,IAAA,OAAOxC,MAAP,CAAA;EACD,GAAA;;IAED8F,wBAAwB,CAACkB,GAAD,EAAM;EAC5B,IAAA,OAAO,OAAOA,GAAP,KAAe,UAAf,GAA4BA,GAAG,CAACZ,IAAJ,CAAS,IAAA,CAAK5D,QAAd,CAA5B,GAAsDwE,GAA7D,CAAA;EACD,GAAA;;IAEDR,gBAAgB,CAACH,UAAD,EAAa;EAC3B,IAAA,MAAMY,qBAAqB,GAAG;EAC5BhH,MAAAA,SAAS,EAAEoG,UADiB;EAE5Ba,MAAAA,SAAS,EAAE,CACT;EACEC,QAAAA,IAAI,EAAE,MADR;EAEEC,QAAAA,OAAO,EAAE;YACPtH,kBAAkB,EAAE,IAAK4B,CAAAA,OAAL,CAAa5B,kBAAAA;EAD1B,SAAA;EAFX,OADS,EAOT;EACEqH,QAAAA,IAAI,EAAE,QADR;EAEEC,QAAAA,OAAO,EAAE;YACPpH,MAAM,EAAE,KAAKyG,UAAL,EAAA;EADD,SAAA;EAFX,OAPS,EAaT;EACEU,QAAAA,IAAI,EAAE,iBADR;EAEEC,QAAAA,OAAO,EAAE;YACP1H,QAAQ,EAAE,IAAKgC,CAAAA,OAAL,CAAahC,QAAAA;EADhB,SAAA;EAFX,OAbS,EAmBT;EACEyH,QAAAA,IAAI,EAAE,OADR;EAEEC,QAAAA,OAAO,EAAE;EACPvG,UAAAA,OAAO,EAAG,CAAA,CAAA,EAAG,IAAKD,CAAAA,WAAL,CAAiBpD,IAAK,CAAA,MAAA,CAAA;EAD5B,SAAA;EAFX,OAnBS,EAyBT;EACE2J,QAAAA,IAAI,EAAE,iBADR;EAEEE,QAAAA,OAAO,EAAE,IAFX;EAGEC,QAAAA,KAAK,EAAE,YAHT;UAIEC,EAAE,EAAEC,IAAI,IAAI;EACV;EACA;YACA,IAAK5D,CAAAA,cAAL,EAAsBhB,CAAAA,YAAtB,CAAmC,uBAAnC,EAA4D4E,IAAI,CAACC,KAAL,CAAWxH,SAAvE,CAAA,CAAA;EACD,SAAA;SAjCM,CAAA;OAFb,CAAA;MAwCA,OAAO,EACL,GAAGgH,qBADE;EAEL,MAAA,IAAI,OAAO,IAAKvF,CAAAA,OAAL,CAAaxB,YAApB,KAAqC,UAArC,GAAkD,IAAA,CAAKwB,OAAL,CAAaxB,YAAb,CAA0B+G,qBAA1B,CAAlD,GAAqG,IAAKvF,CAAAA,OAAL,CAAaxB,YAAtH,CAAA;OAFF,CAAA;EAID,GAAA;;EAEDuB,EAAAA,aAAa,GAAG;MACd,MAAMiG,QAAQ,GAAG,IAAA,CAAKhG,OAAL,CAAalB,OAAb,CAAqBkG,KAArB,CAA2B,GAA3B,CAAjB,CAAA;;EAEA,IAAA,KAAK,MAAMlG,OAAX,IAAsBkH,QAAtB,EAAgC;QAC9B,IAAIlH,OAAO,KAAK,OAAhB,EAAyB;UACvB8B,6BAAY,CAAC+B,EAAb,CAAgB,IAAA,CAAK7B,QAArB,EAA+B,IAAA,CAAK5B,WAAL,CAAiBwC,SAAjB,CAA2B1E,WAA3B,CAA/B,EAAwE,IAAKgD,CAAAA,OAAL,CAAarB,QAArF,EAA+F2F,KAAK,IAAI;EACtG,UAAA,MAAM2B,OAAO,GAAG,IAAA,CAAK5B,4BAAL,CAAkCC,KAAlC,CAAhB,CAAA;;EACA2B,UAAAA,OAAO,CAAC5F,MAAR,EAAA,CAAA;WAFF,CAAA,CAAA;EAID,OALD,MAKO,IAAIvB,OAAO,KAAKpC,cAAhB,EAAgC;UACrC,MAAMwJ,OAAO,GAAGpH,OAAO,KAAKvC,aAAZ,GACd,IAAA,CAAK2C,WAAL,CAAiBwC,SAAjB,CAA2BvE,gBAA3B,CADc,GAEd,IAAK+B,CAAAA,WAAL,CAAiBwC,SAAjB,CAA2BzE,aAA3B,CAFF,CAAA;UAGA,MAAMkJ,QAAQ,GAAGrH,OAAO,KAAKvC,aAAZ,GACf,IAAA,CAAK2C,WAAL,CAAiBwC,SAAjB,CAA2BtE,gBAA3B,CADe,GAEf,IAAK8B,CAAAA,WAAL,CAAiBwC,SAAjB,CAA2BxE,cAA3B,CAFF,CAAA;EAIA0D,QAAAA,6BAAY,CAAC+B,EAAb,CAAgB,IAAA,CAAK7B,QAArB,EAA+BoF,OAA/B,EAAwC,IAAA,CAAKlG,OAAL,CAAarB,QAArD,EAA+D2F,KAAK,IAAI;EACtE,UAAA,MAAM2B,OAAO,GAAG,IAAA,CAAK5B,4BAAL,CAAkCC,KAAlC,CAAhB,CAAA;;EACA2B,UAAAA,OAAO,CAACvG,cAAR,CAAuB4E,KAAK,CAAC8B,IAAN,KAAe,SAAf,GAA2B5J,aAA3B,GAA2CD,aAAlE,IAAmF,IAAnF,CAAA;;EACA0J,UAAAA,OAAO,CAACxF,MAAR,EAAA,CAAA;WAHF,CAAA,CAAA;EAKAG,QAAAA,6BAAY,CAAC+B,EAAb,CAAgB,IAAA,CAAK7B,QAArB,EAA+BqF,QAA/B,EAAyC,IAAA,CAAKnG,OAAL,CAAarB,QAAtD,EAAgE2F,KAAK,IAAI;EACvE,UAAA,MAAM2B,OAAO,GAAG,IAAA,CAAK5B,4BAAL,CAAkCC,KAAlC,CAAhB,CAAA;;YACA2B,OAAO,CAACvG,cAAR,CAAuB4E,KAAK,CAAC8B,IAAN,KAAe,UAAf,GAA4B5J,aAA5B,GAA4CD,aAAnE,CACE0J,GAAAA,OAAO,CAACnF,QAAR,CAAiBkB,QAAjB,CAA0BsC,KAAK,CAAC+B,aAAhC,CADF,CAAA;;EAGAJ,UAAAA,OAAO,CAACzF,MAAR,EAAA,CAAA;WALF,CAAA,CAAA;EAOD,OAAA;EACF,KAAA;;MAED,IAAKQ,CAAAA,iBAAL,GAAyB,MAAM;QAC7B,IAAI,IAAA,CAAKF,QAAT,EAAmB;EACjB,QAAA,IAAA,CAAKkC,IAAL,EAAA,CAAA;EACD,OAAA;OAHH,CAAA;;EAMApC,IAAAA,6BAAY,CAAC+B,EAAb,CAAgB,IAAA,CAAK7B,QAAL,CAAcC,OAAd,CAAsB1E,cAAtB,CAAhB,EAAuDC,gBAAvD,EAAyE,KAAK0E,iBAA9E,CAAA,CAAA;EACD,GAAA;;EAEDf,EAAAA,SAAS,GAAG;MACV,MAAMpB,KAAK,GAAG,IAAKiC,CAAAA,QAAL,CAAcG,YAAd,CAA2B,OAA3B,CAAd,CAAA;;MAEA,IAAI,CAACpC,KAAL,EAAY;EACV,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAI,CAAC,IAAKiC,CAAAA,QAAL,CAAcG,YAAd,CAA2B,YAA3B,CAAD,IAA6C,CAAC,KAAKH,QAAL,CAAcwF,WAAd,CAA0BC,IAA1B,EAAlD,EAAoF;EAClF,MAAA,IAAA,CAAKzF,QAAL,CAAcI,YAAd,CAA2B,YAA3B,EAAyCrC,KAAzC,CAAA,CAAA;EACD,KAAA;;MAED,IAAKiC,CAAAA,QAAL,CAAcI,YAAd,CAA2B,wBAA3B,EAAqDrC,KAArD,EAXU;;;EAYV,IAAA,IAAA,CAAKiC,QAAL,CAAcsC,eAAd,CAA8B,OAA9B,CAAA,CAAA;EACD,GAAA;;EAED3C,EAAAA,MAAM,GAAG;EACP,IAAA,IAAI,IAAKF,CAAAA,QAAL,EAAmB,IAAA,IAAA,CAAKd,UAA5B,EAAwC;QACtC,IAAKA,CAAAA,UAAL,GAAkB,IAAlB,CAAA;EACA,MAAA,OAAA;EACD,KAAA;;MAED,IAAKA,CAAAA,UAAL,GAAkB,IAAlB,CAAA;;MAEA,IAAK+G,CAAAA,WAAL,CAAiB,MAAM;QACrB,IAAI,IAAA,CAAK/G,UAAT,EAAqB;EACnB,QAAA,IAAA,CAAK2B,IAAL,EAAA,CAAA;EACD,OAAA;EACF,KAJD,EAIG,IAAKpB,CAAAA,OAAL,CAAa7B,KAAb,CAAmBiD,IAJtB,CAAA,CAAA;EAKD,GAAA;;EAEDZ,EAAAA,MAAM,GAAG;MACP,IAAI,IAAA,CAAK2C,oBAAL,EAAJ,EAAiC;EAC/B,MAAA,OAAA;EACD,KAAA;;MAED,IAAK1D,CAAAA,UAAL,GAAkB,KAAlB,CAAA;;MAEA,IAAK+G,CAAAA,WAAL,CAAiB,MAAM;QACrB,IAAI,CAAC,IAAK/G,CAAAA,UAAV,EAAsB;EACpB,QAAA,IAAA,CAAKuD,IAAL,EAAA,CAAA;EACD,OAAA;EACF,KAJD,EAIG,IAAKhD,CAAAA,OAAL,CAAa7B,KAAb,CAAmB6E,IAJtB,CAAA,CAAA;EAKD,GAAA;;EAEDwD,EAAAA,WAAW,CAACC,OAAD,EAAUC,OAAV,EAAmB;MAC5B/F,YAAY,CAAC,IAAKnB,CAAAA,QAAN,CAAZ,CAAA;EACA,IAAA,IAAA,CAAKA,QAAL,GAAgBmH,UAAU,CAACF,OAAD,EAAUC,OAAV,CAA1B,CAAA;EACD,GAAA;;EAEDvD,EAAAA,oBAAoB,GAAG;MACrB,OAAOyD,MAAM,CAACC,MAAP,CAAc,IAAA,CAAKnH,cAAnB,CAAmCoH,CAAAA,QAAnC,CAA4C,IAA5C,CAAP,CAAA;EACD,GAAA;;IAEDC,UAAU,CAAC3H,MAAD,EAAS;MACjB,MAAM4H,cAAc,GAAGC,4BAAW,CAACC,iBAAZ,CAA8B,IAAA,CAAKpG,QAAnC,CAAvB,CAAA;;MAEA,KAAK,MAAMqG,aAAX,IAA4BP,MAAM,CAACQ,IAAP,CAAYJ,cAAZ,CAA5B,EAAyD;EACvD,MAAA,IAAIjL,qBAAqB,CAACsL,GAAtB,CAA0BF,aAA1B,CAAJ,EAA8C;UAC5C,OAAOH,cAAc,CAACG,aAAD,CAArB,CAAA;EACD,OAAA;EACF,KAAA;;MAED/H,MAAM,GAAG,EACP,GAAG4H,cADI;QAEP,IAAI,OAAO5H,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAApD,CAAA;OAFF,CAAA;EAIAA,IAAAA,MAAM,GAAG,IAAA,CAAKkI,eAAL,CAAqBlI,MAArB,CAAT,CAAA;EACAA,IAAAA,MAAM,GAAG,IAAA,CAAKmI,iBAAL,CAAuBnI,MAAvB,CAAT,CAAA;;MACA,IAAKoI,CAAAA,gBAAL,CAAsBpI,MAAtB,CAAA,CAAA;;EACA,IAAA,OAAOA,MAAP,CAAA;EACD,GAAA;;IAEDmI,iBAAiB,CAACnI,MAAD,EAAS;EACxBA,IAAAA,MAAM,CAACnB,SAAP,GAAmBmB,MAAM,CAACnB,SAAP,KAAqB,KAArB,GAA6BsE,QAAQ,CAACE,IAAtC,GAA6CgF,gBAAU,CAACrI,MAAM,CAACnB,SAAR,CAA1E,CAAA;;EAEA,IAAA,IAAI,OAAOmB,MAAM,CAACjB,KAAd,KAAwB,QAA5B,EAAsC;QACpCiB,MAAM,CAACjB,KAAP,GAAe;UACbiD,IAAI,EAAEhC,MAAM,CAACjB,KADA;UAEb6E,IAAI,EAAE5D,MAAM,CAACjB,KAAAA;SAFf,CAAA;EAID,KAAA;;EAED,IAAA,IAAI,OAAOiB,MAAM,CAACP,KAAd,KAAwB,QAA5B,EAAsC;QACpCO,MAAM,CAACP,KAAP,GAAeO,MAAM,CAACP,KAAP,CAAakF,QAAb,EAAf,CAAA;EACD,KAAA;;EAED,IAAA,IAAI,OAAO3E,MAAM,CAACsE,OAAd,KAA0B,QAA9B,EAAwC;QACtCtE,MAAM,CAACsE,OAAP,GAAiBtE,MAAM,CAACsE,OAAP,CAAeK,QAAf,EAAjB,CAAA;EACD,KAAA;;EAED,IAAA,OAAO3E,MAAP,CAAA;EACD,GAAA;;EAEDqF,EAAAA,kBAAkB,GAAG;MACnB,MAAMrF,MAAM,GAAG,EAAf,CAAA;;EAEA,IAAA,KAAK,MAAMsI,GAAX,IAAkB,IAAA,CAAK1H,OAAvB,EAAgC;EAC9B,MAAA,IAAI,IAAKd,CAAAA,WAAL,CAAiBtB,OAAjB,CAAyB8J,GAAzB,CAAkC,KAAA,IAAA,CAAK1H,OAAL,CAAa0H,GAAb,CAAtC,EAAyD;UACvDtI,MAAM,CAACsI,GAAD,CAAN,GAAc,KAAK1H,OAAL,CAAa0H,GAAb,CAAd,CAAA;EACD,OAAA;EACF,KAAA;;MAEDtI,MAAM,CAACT,QAAP,GAAkB,KAAlB,CAAA;EACAS,IAAAA,MAAM,CAACN,OAAP,GAAiB,QAAjB,CAVmB;EAanB;EACA;;EACA,IAAA,OAAOM,MAAP,CAAA;EACD,GAAA;;EAED+B,EAAAA,cAAc,GAAG;MACf,IAAI,IAAA,CAAKxB,OAAT,EAAkB;QAChB,IAAKA,CAAAA,OAAL,CAAagI,OAAb,EAAA,CAAA;;QACA,IAAKhI,CAAAA,OAAL,GAAe,IAAf,CAAA;EACD,KAAA;;MAED,IAAI,IAAA,CAAKG,GAAT,EAAc;QACZ,IAAKA,CAAAA,GAAL,CAASoD,MAAT,EAAA,CAAA;QACA,IAAKpD,CAAAA,GAAL,GAAW,IAAX,CAAA;EACD,KAAA;EACF,GAxfiC;;;IA2fZ,OAAf8H,eAAe,CAACxI,MAAD,EAAS;MAC7B,OAAO,IAAA,CAAKyI,IAAL,CAAU,YAAY;QAC3B,MAAM/B,IAAI,GAAG9G,OAAO,CAACuF,mBAAR,CAA4B,IAA5B,EAAkCnF,MAAlC,CAAb,CAAA;;EAEA,MAAA,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,QAAA,OAAA;EACD,OAAA;;EAED,MAAA,IAAI,OAAO0G,IAAI,CAAC1G,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,QAAA,MAAM,IAAIE,SAAJ,CAAe,CAAmBF,iBAAAA,EAAAA,MAAO,GAAzC,CAAN,CAAA;EACD,OAAA;;QAED0G,IAAI,CAAC1G,MAAD,CAAJ,EAAA,CAAA;EACD,KAZM,CAAP,CAAA;EAaD,GAAA;;EAzgBiC,CAAA;EA4gBpC;EACA;EACA;;;AAEA0I,0BAAkB,CAAC9I,OAAD,CAAlB;;;;;;;;"}