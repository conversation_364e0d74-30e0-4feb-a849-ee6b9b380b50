<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../../../dist/css/bootstrap.min.css" rel="stylesheet">
    <title>Carousel</title>
    <style>
      .carousel-item {
        transition: transform 2s ease, opacity .5s ease;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>Carousel <small>Bootstrap Visual Test</small></h1>

      <p>The transition duration should be around 2s. Also, the carousel shouldn't slide when its window/tab is hidden. Check the console log.</p>

      <div id="carousel-example-generic" class="carousel slide" data-bs-ride="carousel">
        <div class="carousel-indicators">
          <button type="button" data-bs-target="#carousel-example-generic" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
          <button type="button" data-bs-target="#carousel-example-generic" data-bs-slide-to="1" aria-label="Slide 2"></button>
          <button type="button" data-bs-target="#carousel-example-generic" data-bs-slide-to="2" aria-label="Slide 3"></button>
        </div>
        <div class="carousel-inner">
          <div class="carousel-item active">
            <img src="https://i.imgur.com/iEZgY7Y.jpg" alt="First slide">
          </div>
          <div class="carousel-item">
            <img src="https://i.imgur.com/eNWn1Xs.jpg" alt="Second slide">
          </div>
          <div class="carousel-item">
            <img src="https://i.imgur.com/Nm7xoti.jpg" alt="Third slide">
          </div>
        </div>
        <button class="carousel-control-prev" data-bs-target="#carousel-example-generic" type="button" data-bs-slide="prev">
          <span class="carousel-control-prev-icon" aria-hidden="true"></span>
          <span class="visually-hidden">Previous</span>
        </button>
        <button class="carousel-control-next" data-bs-target="#carousel-example-generic" type="button" data-bs-slide="next">
          <span class="carousel-control-next-icon" aria-hidden="true"></span>
          <span class="visually-hidden">Next</span>
        </button>
      </div>
    </div>

    <script src="../../../dist/js/bootstrap.bundle.js"></script>
    <script>
      let t0
      let t1
      const carousel = document.getElementById('carousel-example-generic')

      // Test to show that the carousel doesn't slide when the current tab isn't visible
      // Test to show that transition-duration can be changed with css
      carousel.addEventListener('slid.bs.carousel', event => {
        t1 = performance.now()
        console.log('transition-duration took ' + (t1 - t0) + 'ms, slid at ' + event.timeStamp)
      })
      carousel.addEventListener('slide.bs.carousel', () => {
        t0 = performance.now()
      })
    </script>
  </body>
</html>
