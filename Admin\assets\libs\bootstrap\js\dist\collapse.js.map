{"version": 3, "file": "collapse.js", "sources": ["../src/collapse.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getElementFromSelector,\n  getSelectorFromElement,\n  reflow\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\nconst CLASS_NAME_DEEPER_CHILDREN = `:scope .${CLASS_NAME_COLLAPSE} .${CLASS_NAME_COLLAPSE}`\nconst CLASS_NAME_HORIZONTAL = 'collapse-horizontal'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.collapse.show, .collapse.collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\nconst Default = {\n  parent: null,\n  toggle: true\n}\n\nconst DefaultType = {\n  parent: '(null|element)',\n  toggle: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isTransitioning = false\n    this._triggerArray = []\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (const elem of toggleList) {\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElement => foundElement === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._initializeChildren()\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._triggerArray, this._isShown())\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    if (this._isShown()) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._isShown()) {\n      return\n    }\n\n    let activeChildren = []\n\n    // find active children\n    if (this._config.parent) {\n      activeChildren = this._getFirstLevelChildren(SELECTOR_ACTIVES)\n        .filter(element => element !== this._element)\n        .map(element => Collapse.getOrCreateInstance(element, { toggle: false }))\n    }\n\n    if (activeChildren.length && activeChildren[0]._isTransitioning) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    for (const activeInstance of activeChildren) {\n      activeInstance.hide()\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    this._addAriaAndCollapsedClass(this._triggerArray, true)\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n\n    this._queueCallback(complete, this._element, true)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._isShown()) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    for (const trigger of this._triggerArray) {\n      const element = getElementFromSelector(trigger)\n\n      if (element && !this._isShown(element)) {\n        this._addAriaAndCollapsedClass([trigger], false)\n      }\n    }\n\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n\n    this._queueCallback(complete, this._element, true)\n  }\n\n  _isShown(element = this._element) {\n    return element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    config.parent = getElement(config.parent)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(CLASS_NAME_HORIZONTAL) ? WIDTH : HEIGHT\n  }\n\n  _initializeChildren() {\n    if (!this._config.parent) {\n      return\n    }\n\n    const children = this._getFirstLevelChildren(SELECTOR_DATA_TOGGLE)\n\n    for (const element of children) {\n      const selected = getElementFromSelector(element)\n\n      if (selected) {\n        this._addAriaAndCollapsedClass([element], this._isShown(selected))\n      }\n    }\n  }\n\n  _getFirstLevelChildren(selector) {\n    const children = SelectorEngine.find(CLASS_NAME_DEEPER_CHILDREN, this._config.parent)\n    // remove children if greater depth\n    return SelectorEngine.find(selector, this._config.parent).filter(element => !children.includes(element))\n  }\n\n  _addAriaAndCollapsedClass(triggerArray, isOpen) {\n    if (!triggerArray.length) {\n      return\n    }\n\n    for (const element of triggerArray) {\n      element.classList.toggle(CLASS_NAME_COLLAPSED, !isOpen)\n      element.setAttribute('aria-expanded', isOpen)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    const _config = {}\n    if (typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    return this.each(function () {\n      const data = Collapse.getOrCreateInstance(this, _config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  for (const element of selectorElements) {\n    Collapse.getOrCreateInstance(element, { toggle: false }).toggle()\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Collapse)\n\nexport default Collapse\n"], "names": ["NAME", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_CLICK_DATA_API", "CLASS_NAME_SHOW", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_COLLAPSED", "CLASS_NAME_DEEPER_CHILDREN", "CLASS_NAME_HORIZONTAL", "WIDTH", "HEIGHT", "SELECTOR_ACTIVES", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON><PERSON>", "parent", "toggle", "DefaultType", "Collapse", "BaseComponent", "constructor", "element", "config", "_isTransitioning", "_triggerArray", "toggleList", "SelectorEngine", "find", "elem", "selector", "getSelectorFromElement", "filterElement", "filter", "foundElement", "_element", "length", "push", "_initializeC<PERSON><PERSON>n", "_config", "_addAriaAndCollapsedClass", "_isShown", "hide", "show", "activeC<PERSON><PERSON>n", "_getFirstLevelChildren", "map", "getOrCreateInstance", "startEvent", "EventHandler", "trigger", "defaultPrevented", "activeInstance", "dimension", "_getDimension", "classList", "remove", "add", "style", "complete", "capitalizedDimension", "toUpperCase", "slice", "scrollSize", "_queueCallback", "getBoundingClientRect", "reflow", "getElementFromSelector", "contains", "_configAfterMerge", "Boolean", "getElement", "children", "selected", "includes", "trigger<PERSON><PERSON>y", "isOpen", "setAttribute", "jQueryInterface", "test", "each", "data", "TypeError", "on", "document", "event", "target", "tagName", "<PERSON><PERSON><PERSON><PERSON>", "preventDefault", "selectorElements", "defineJQueryPlugin"], "mappings": ";;;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAaA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,UAAb,CAAA;EACA,MAAMC,QAAQ,GAAG,aAAjB,CAAA;EACA,MAAMC,SAAS,GAAI,CAAGD,CAAAA,EAAAA,QAAS,CAA/B,CAAA,CAAA;EACA,MAAME,YAAY,GAAG,WAArB,CAAA;EAEA,MAAMC,UAAU,GAAI,CAAMF,IAAAA,EAAAA,SAAU,CAApC,CAAA,CAAA;EACA,MAAMG,WAAW,GAAI,CAAOH,KAAAA,EAAAA,SAAU,CAAtC,CAAA,CAAA;EACA,MAAMI,UAAU,GAAI,CAAMJ,IAAAA,EAAAA,SAAU,CAApC,CAAA,CAAA;EACA,MAAMK,YAAY,GAAI,CAAQL,MAAAA,EAAAA,SAAU,CAAxC,CAAA,CAAA;EACA,MAAMM,oBAAoB,GAAI,CAAA,KAAA,EAAON,SAAU,CAAA,EAAEC,YAAa,CAA9D,CAAA,CAAA;EAEA,MAAMM,eAAe,GAAG,MAAxB,CAAA;EACA,MAAMC,mBAAmB,GAAG,UAA5B,CAAA;EACA,MAAMC,qBAAqB,GAAG,YAA9B,CAAA;EACA,MAAMC,oBAAoB,GAAG,WAA7B,CAAA;EACA,MAAMC,0BAA0B,GAAI,CAAA,QAAA,EAAUH,mBAAoB,CAAA,EAAA,EAAIA,mBAAoB,CAA1F,CAAA,CAAA;EACA,MAAMI,qBAAqB,GAAG,qBAA9B,CAAA;EAEA,MAAMC,KAAK,GAAG,OAAd,CAAA;EACA,MAAMC,MAAM,GAAG,QAAf,CAAA;EAEA,MAAMC,gBAAgB,GAAG,sCAAzB,CAAA;EACA,MAAMC,oBAAoB,GAAG,6BAA7B,CAAA;EAEA,MAAMC,OAAO,GAAG;EACdC,EAAAA,MAAM,EAAE,IADM;EAEdC,EAAAA,MAAM,EAAE,IAAA;EAFM,CAAhB,CAAA;EAKA,MAAMC,WAAW,GAAG;EAClBF,EAAAA,MAAM,EAAE,gBADU;EAElBC,EAAAA,MAAM,EAAE,SAAA;EAFU,CAApB,CAAA;EAKA;EACA;EACA;;EAEA,MAAME,QAAN,SAAuBC,8BAAvB,CAAqC;EACnCC,EAAAA,WAAW,CAACC,OAAD,EAAUC,MAAV,EAAkB;MAC3B,KAAMD,CAAAA,OAAN,EAAeC,MAAf,CAAA,CAAA;MAEA,IAAKC,CAAAA,gBAAL,GAAwB,KAAxB,CAAA;MACA,IAAKC,CAAAA,aAAL,GAAqB,EAArB,CAAA;EAEA,IAAA,MAAMC,UAAU,GAAGC,+BAAc,CAACC,IAAf,CAAoBd,oBAApB,CAAnB,CAAA;;EAEA,IAAA,KAAK,MAAMe,IAAX,IAAmBH,UAAnB,EAA+B;EAC7B,MAAA,MAAMI,QAAQ,GAAGC,4BAAsB,CAACF,IAAD,CAAvC,CAAA;EACA,MAAA,MAAMG,aAAa,GAAGL,+BAAc,CAACC,IAAf,CAAoBE,QAApB,CAAA,CACnBG,MADmB,CACZC,YAAY,IAAIA,YAAY,KAAK,IAAA,CAAKC,QAD1B,CAAtB,CAAA;;EAGA,MAAA,IAAIL,QAAQ,KAAK,IAAb,IAAqBE,aAAa,CAACI,MAAvC,EAA+C;EAC7C,QAAA,IAAA,CAAKX,aAAL,CAAmBY,IAAnB,CAAwBR,IAAxB,CAAA,CAAA;EACD,OAAA;EACF,KAAA;;EAED,IAAA,IAAA,CAAKS,mBAAL,EAAA,CAAA;;EAEA,IAAA,IAAI,CAAC,IAAA,CAAKC,OAAL,CAAavB,MAAlB,EAA0B;EACxB,MAAA,IAAA,CAAKwB,yBAAL,CAA+B,IAAA,CAAKf,aAApC,EAAmD,IAAA,CAAKgB,QAAL,EAAnD,CAAA,CAAA;EACD,KAAA;;EAED,IAAA,IAAI,IAAKF,CAAAA,OAAL,CAAatB,MAAjB,EAAyB;EACvB,MAAA,IAAA,CAAKA,MAAL,EAAA,CAAA;EACD,KAAA;EACF,GA5BkC;;;EA+BjB,EAAA,WAAPF,OAAO,GAAG;EACnB,IAAA,OAAOA,OAAP,CAAA;EACD,GAAA;;EAEqB,EAAA,WAAXG,WAAW,GAAG;EACvB,IAAA,OAAOA,WAAP,CAAA;EACD,GAAA;;EAEc,EAAA,WAAJtB,IAAI,GAAG;EAChB,IAAA,OAAOA,IAAP,CAAA;EACD,GAzCkC;;;EA4CnCqB,EAAAA,MAAM,GAAG;MACP,IAAI,IAAA,CAAKwB,QAAL,EAAJ,EAAqB;EACnB,MAAA,IAAA,CAAKC,IAAL,EAAA,CAAA;EACD,KAFD,MAEO;EACL,MAAA,IAAA,CAAKC,IAAL,EAAA,CAAA;EACD,KAAA;EACF,GAAA;;EAEDA,EAAAA,IAAI,GAAG;EACL,IAAA,IAAI,KAAKnB,gBAAL,IAAyB,IAAKiB,CAAAA,QAAL,EAA7B,EAA8C;EAC5C,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAIG,cAAc,GAAG,EAArB,CALK;;EAQL,IAAA,IAAI,IAAKL,CAAAA,OAAL,CAAavB,MAAjB,EAAyB;QACvB4B,cAAc,GAAG,IAAKC,CAAAA,sBAAL,CAA4BhC,gBAA5B,EACdoB,MADc,CACPX,OAAO,IAAIA,OAAO,KAAK,KAAKa,QADrB,CAAA,CAEdW,GAFc,CAEVxB,OAAO,IAAIH,QAAQ,CAAC4B,mBAAT,CAA6BzB,OAA7B,EAAsC;EAAEL,QAAAA,MAAM,EAAE,KAAA;EAAV,OAAtC,CAFD,CAAjB,CAAA;EAGD,KAAA;;MAED,IAAI2B,cAAc,CAACR,MAAf,IAAyBQ,cAAc,CAAC,CAAD,CAAd,CAAkBpB,gBAA/C,EAAiE;EAC/D,MAAA,OAAA;EACD,KAAA;;MAED,MAAMwB,UAAU,GAAGC,6BAAY,CAACC,OAAb,CAAqB,IAAKf,CAAAA,QAA1B,EAAoCnC,UAApC,CAAnB,CAAA;;MACA,IAAIgD,UAAU,CAACG,gBAAf,EAAiC;EAC/B,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,KAAK,MAAMC,cAAX,IAA6BR,cAA7B,EAA6C;EAC3CQ,MAAAA,cAAc,CAACV,IAAf,EAAA,CAAA;EACD,KAAA;;EAED,IAAA,MAAMW,SAAS,GAAG,IAAKC,CAAAA,aAAL,EAAlB,CAAA;;EAEA,IAAA,IAAA,CAAKnB,QAAL,CAAcoB,SAAd,CAAwBC,MAAxB,CAA+BlD,mBAA/B,CAAA,CAAA;;EACA,IAAA,IAAA,CAAK6B,QAAL,CAAcoB,SAAd,CAAwBE,GAAxB,CAA4BlD,qBAA5B,CAAA,CAAA;;EAEA,IAAA,IAAA,CAAK4B,QAAL,CAAcuB,KAAd,CAAoBL,SAApB,IAAiC,CAAjC,CAAA;;EAEA,IAAA,IAAA,CAAKb,yBAAL,CAA+B,IAAKf,CAAAA,aAApC,EAAmD,IAAnD,CAAA,CAAA;;MACA,IAAKD,CAAAA,gBAAL,GAAwB,IAAxB,CAAA;;MAEA,MAAMmC,QAAQ,GAAG,MAAM;QACrB,IAAKnC,CAAAA,gBAAL,GAAwB,KAAxB,CAAA;;EAEA,MAAA,IAAA,CAAKW,QAAL,CAAcoB,SAAd,CAAwBC,MAAxB,CAA+BjD,qBAA/B,CAAA,CAAA;;QACA,IAAK4B,CAAAA,QAAL,CAAcoB,SAAd,CAAwBE,GAAxB,CAA4BnD,mBAA5B,EAAiDD,eAAjD,CAAA,CAAA;;EAEA,MAAA,IAAA,CAAK8B,QAAL,CAAcuB,KAAd,CAAoBL,SAApB,IAAiC,EAAjC,CAAA;EAEAJ,MAAAA,6BAAY,CAACC,OAAb,CAAqB,IAAKf,CAAAA,QAA1B,EAAoClC,WAApC,CAAA,CAAA;OARF,CAAA;;EAWA,IAAA,MAAM2D,oBAAoB,GAAGP,SAAS,CAAC,CAAD,CAAT,CAAaQ,WAAb,EAAA,GAA6BR,SAAS,CAACS,KAAV,CAAgB,CAAhB,CAA1D,CAAA;EACA,IAAA,MAAMC,UAAU,GAAI,CAAQH,MAAAA,EAAAA,oBAAqB,CAAjD,CAAA,CAAA;;EAEA,IAAA,IAAA,CAAKI,cAAL,CAAoBL,QAApB,EAA8B,IAAKxB,CAAAA,QAAnC,EAA6C,IAA7C,CAAA,CAAA;;EACA,IAAA,IAAA,CAAKA,QAAL,CAAcuB,KAAd,CAAoBL,SAApB,CAAA,GAAkC,CAAE,EAAA,IAAA,CAAKlB,QAAL,CAAc4B,UAAd,CAA0B,CAA9D,EAAA,CAAA,CAAA;EACD,GAAA;;EAEDrB,EAAAA,IAAI,GAAG;EACL,IAAA,IAAI,KAAKlB,gBAAL,IAAyB,CAAC,IAAKiB,CAAAA,QAAL,EAA9B,EAA+C;EAC7C,MAAA,OAAA;EACD,KAAA;;MAED,MAAMO,UAAU,GAAGC,6BAAY,CAACC,OAAb,CAAqB,IAAKf,CAAAA,QAA1B,EAAoCjC,UAApC,CAAnB,CAAA;;MACA,IAAI8C,UAAU,CAACG,gBAAf,EAAiC;EAC/B,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,MAAME,SAAS,GAAG,IAAKC,CAAAA,aAAL,EAAlB,CAAA;;EAEA,IAAA,IAAA,CAAKnB,QAAL,CAAcuB,KAAd,CAAoBL,SAApB,CAAkC,GAAA,CAAA,EAAE,IAAKlB,CAAAA,QAAL,CAAc8B,qBAAd,EAAsCZ,CAAAA,SAAtC,CAAiD,CAArF,EAAA,CAAA,CAAA;MAEAa,YAAM,CAAC,IAAK/B,CAAAA,QAAN,CAAN,CAAA;;EAEA,IAAA,IAAA,CAAKA,QAAL,CAAcoB,SAAd,CAAwBE,GAAxB,CAA4BlD,qBAA5B,CAAA,CAAA;;MACA,IAAK4B,CAAAA,QAAL,CAAcoB,SAAd,CAAwBC,MAAxB,CAA+BlD,mBAA/B,EAAoDD,eAApD,CAAA,CAAA;;EAEA,IAAA,KAAK,MAAM6C,OAAX,IAAsB,IAAA,CAAKzB,aAA3B,EAA0C;EACxC,MAAA,MAAMH,OAAO,GAAG6C,4BAAsB,CAACjB,OAAD,CAAtC,CAAA;;QAEA,IAAI5B,OAAO,IAAI,CAAC,IAAA,CAAKmB,QAAL,CAAcnB,OAAd,CAAhB,EAAwC;EACtC,QAAA,IAAA,CAAKkB,yBAAL,CAA+B,CAACU,OAAD,CAA/B,EAA0C,KAA1C,CAAA,CAAA;EACD,OAAA;EACF,KAAA;;MAED,IAAK1B,CAAAA,gBAAL,GAAwB,IAAxB,CAAA;;MAEA,MAAMmC,QAAQ,GAAG,MAAM;QACrB,IAAKnC,CAAAA,gBAAL,GAAwB,KAAxB,CAAA;;EACA,MAAA,IAAA,CAAKW,QAAL,CAAcoB,SAAd,CAAwBC,MAAxB,CAA+BjD,qBAA/B,CAAA,CAAA;;EACA,MAAA,IAAA,CAAK4B,QAAL,CAAcoB,SAAd,CAAwBE,GAAxB,CAA4BnD,mBAA5B,CAAA,CAAA;;EACA2C,MAAAA,6BAAY,CAACC,OAAb,CAAqB,IAAKf,CAAAA,QAA1B,EAAoChC,YAApC,CAAA,CAAA;OAJF,CAAA;;EAOA,IAAA,IAAA,CAAKgC,QAAL,CAAcuB,KAAd,CAAoBL,SAApB,IAAiC,EAAjC,CAAA;;EAEA,IAAA,IAAA,CAAKW,cAAL,CAAoBL,QAApB,EAA8B,IAAKxB,CAAAA,QAAnC,EAA6C,IAA7C,CAAA,CAAA;EACD,GAAA;;EAEDM,EAAAA,QAAQ,CAACnB,OAAO,GAAG,IAAA,CAAKa,QAAhB,EAA0B;EAChC,IAAA,OAAOb,OAAO,CAACiC,SAAR,CAAkBa,QAAlB,CAA2B/D,eAA3B,CAAP,CAAA;EACD,GAtJkC;;;IAyJnCgE,iBAAiB,CAAC9C,MAAD,EAAS;MACxBA,MAAM,CAACN,MAAP,GAAgBqD,OAAO,CAAC/C,MAAM,CAACN,MAAR,CAAvB,CADwB;;MAExBM,MAAM,CAACP,MAAP,GAAgBuD,gBAAU,CAAChD,MAAM,CAACP,MAAR,CAA1B,CAAA;EACA,IAAA,OAAOO,MAAP,CAAA;EACD,GAAA;;EAED+B,EAAAA,aAAa,GAAG;MACd,OAAO,IAAA,CAAKnB,QAAL,CAAcoB,SAAd,CAAwBa,QAAxB,CAAiC1D,qBAAjC,CAAA,GAA0DC,KAA1D,GAAkEC,MAAzE,CAAA;EACD,GAAA;;EAED0B,EAAAA,mBAAmB,GAAG;EACpB,IAAA,IAAI,CAAC,IAAA,CAAKC,OAAL,CAAavB,MAAlB,EAA0B;EACxB,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,MAAMwD,QAAQ,GAAG,IAAA,CAAK3B,sBAAL,CAA4B/B,oBAA5B,CAAjB,CAAA;;EAEA,IAAA,KAAK,MAAMQ,OAAX,IAAsBkD,QAAtB,EAAgC;EAC9B,MAAA,MAAMC,QAAQ,GAAGN,4BAAsB,CAAC7C,OAAD,CAAvC,CAAA;;EAEA,MAAA,IAAImD,QAAJ,EAAc;UACZ,IAAKjC,CAAAA,yBAAL,CAA+B,CAAClB,OAAD,CAA/B,EAA0C,IAAKmB,CAAAA,QAAL,CAAcgC,QAAd,CAA1C,CAAA,CAAA;EACD,OAAA;EACF,KAAA;EACF,GAAA;;IAED5B,sBAAsB,CAACf,QAAD,EAAW;EAC/B,IAAA,MAAM0C,QAAQ,GAAG7C,+BAAc,CAACC,IAAf,CAAoBnB,0BAApB,EAAgD,IAAA,CAAK8B,OAAL,CAAavB,MAA7D,CAAjB,CAD+B;;MAG/B,OAAOW,+BAAc,CAACC,IAAf,CAAoBE,QAApB,EAA8B,IAAA,CAAKS,OAAL,CAAavB,MAA3C,CAAA,CAAmDiB,MAAnD,CAA0DX,OAAO,IAAI,CAACkD,QAAQ,CAACE,QAAT,CAAkBpD,OAAlB,CAAtE,CAAP,CAAA;EACD,GAAA;;EAEDkB,EAAAA,yBAAyB,CAACmC,YAAD,EAAeC,MAAf,EAAuB;EAC9C,IAAA,IAAI,CAACD,YAAY,CAACvC,MAAlB,EAA0B;EACxB,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,KAAK,MAAMd,OAAX,IAAsBqD,YAAtB,EAAoC;QAClCrD,OAAO,CAACiC,SAAR,CAAkBtC,MAAlB,CAAyBT,oBAAzB,EAA+C,CAACoE,MAAhD,CAAA,CAAA;EACAtD,MAAAA,OAAO,CAACuD,YAAR,CAAqB,eAArB,EAAsCD,MAAtC,CAAA,CAAA;EACD,KAAA;EACF,GAlMkC;;;IAqMb,OAAfE,eAAe,CAACvD,MAAD,EAAS;MAC7B,MAAMgB,OAAO,GAAG,EAAhB,CAAA;;MACA,IAAI,OAAOhB,MAAP,KAAkB,QAAlB,IAA8B,YAAYwD,IAAZ,CAAiBxD,MAAjB,CAAlC,EAA4D;QAC1DgB,OAAO,CAACtB,MAAR,GAAiB,KAAjB,CAAA;EACD,KAAA;;MAED,OAAO,IAAA,CAAK+D,IAAL,CAAU,YAAY;QAC3B,MAAMC,IAAI,GAAG9D,QAAQ,CAAC4B,mBAAT,CAA6B,IAA7B,EAAmCR,OAAnC,CAAb,CAAA;;EAEA,MAAA,IAAI,OAAOhB,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,QAAA,IAAI,OAAO0D,IAAI,CAAC1D,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,UAAA,MAAM,IAAI2D,SAAJ,CAAe,CAAmB3D,iBAAAA,EAAAA,MAAO,GAAzC,CAAN,CAAA;EACD,SAAA;;UAED0D,IAAI,CAAC1D,MAAD,CAAJ,EAAA,CAAA;EACD,OAAA;EACF,KAVM,CAAP,CAAA;EAWD,GAAA;;EAtNkC,CAAA;EAyNrC;EACA;EACA;;;AAEA0B,+BAAY,CAACkC,EAAb,CAAgBC,QAAhB,EAA0BhF,oBAA1B,EAAgDU,oBAAhD,EAAsE,UAAUuE,KAAV,EAAiB;EACrF;EACA,EAAA,IAAIA,KAAK,CAACC,MAAN,CAAaC,OAAb,KAAyB,GAAzB,IAAiCF,KAAK,CAACG,cAAN,IAAwBH,KAAK,CAACG,cAAN,CAAqBD,OAArB,KAAiC,GAA9F,EAAoG;EAClGF,IAAAA,KAAK,CAACI,cAAN,EAAA,CAAA;EACD,GAAA;;EAED,EAAA,MAAM3D,QAAQ,GAAGC,4BAAsB,CAAC,IAAD,CAAvC,CAAA;EACA,EAAA,MAAM2D,gBAAgB,GAAG/D,+BAAc,CAACC,IAAf,CAAoBE,QAApB,CAAzB,CAAA;;EAEA,EAAA,KAAK,MAAMR,OAAX,IAAsBoE,gBAAtB,EAAwC;EACtCvE,IAAAA,QAAQ,CAAC4B,mBAAT,CAA6BzB,OAA7B,EAAsC;EAAEL,MAAAA,MAAM,EAAE,KAAA;EAAV,KAAtC,EAAyDA,MAAzD,EAAA,CAAA;EACD,GAAA;EACF,CAZD,CAAA,CAAA;EAcA;EACA;EACA;;AAEA0E,0BAAkB,CAACxE,QAAD,CAAlB;;;;;;;;"}