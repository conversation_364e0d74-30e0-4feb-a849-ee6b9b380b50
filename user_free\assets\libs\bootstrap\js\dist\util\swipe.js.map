{"version": 3, "file": "swipe.js", "sources": ["../../src/util/swipe.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): util/swipe.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Config from './config'\nimport EventHandler from '../dom/event-handler'\nimport { execute } from './index'\n\n/**\n * Constants\n */\n\nconst NAME = 'swipe'\nconst EVENT_KEY = '.bs.swipe'\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  endCallback: null,\n  leftCallback: null,\n  rightCallback: null\n}\n\nconst DefaultType = {\n  endCallback: '(function|null)',\n  leftCallback: '(function|null)',\n  rightCallback: '(function|null)'\n}\n\n/**\n * Class definition\n */\n\nclass Swipe extends Config {\n  constructor(element, config) {\n    super()\n    this._element = element\n\n    if (!element || !Swipe.isSupported()) {\n      return\n    }\n\n    this._config = this._getConfig(config)\n    this._deltaX = 0\n    this._supportPointerEvents = Boolean(window.PointerEvent)\n    this._initEvents()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n  }\n\n  // Private\n  _start(event) {\n    if (!this._supportPointerEvents) {\n      this._deltaX = event.touches[0].clientX\n\n      return\n    }\n\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX\n    }\n  }\n\n  _end(event) {\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX - this._deltaX\n    }\n\n    this._handleSwipe()\n    execute(this._config.endCallback)\n  }\n\n  _move(event) {\n    this._deltaX = event.touches && event.touches.length > 1 ?\n      0 :\n      event.touches[0].clientX - this._deltaX\n  }\n\n  _handleSwipe() {\n    const absDeltaX = Math.abs(this._deltaX)\n\n    if (absDeltaX <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltaX / this._deltaX\n\n    this._deltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    execute(direction > 0 ? this._config.rightCallback : this._config.leftCallback)\n  }\n\n  _initEvents() {\n    if (this._supportPointerEvents) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => this._start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => this._end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => this._start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => this._move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => this._end(event))\n    }\n  }\n\n  _eventIsPointerPenTouch(event) {\n    return this._supportPointerEvents && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)\n  }\n\n  // Static\n  static isSupported() {\n    return 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n  }\n}\n\nexport default Swipe\n"], "names": ["NAME", "EVENT_KEY", "EVENT_TOUCHSTART", "EVENT_TOUCHMOVE", "EVENT_TOUCHEND", "EVENT_POINTERDOWN", "EVENT_POINTERUP", "POINTER_TYPE_TOUCH", "POINTER_TYPE_PEN", "CLASS_NAME_POINTER_EVENT", "SWIPE_THRESHOLD", "<PERSON><PERSON><PERSON>", "endCallback", "leftCallback", "<PERSON><PERSON><PERSON><PERSON>", "DefaultType", "Swipe", "Config", "constructor", "element", "config", "_element", "isSupported", "_config", "_getConfig", "_deltaX", "_supportPointerEvents", "Boolean", "window", "PointerEvent", "_initEvents", "dispose", "EventHandler", "off", "_start", "event", "touches", "clientX", "_eventIsPointerPenTouch", "_end", "_handleSwipe", "execute", "_move", "length", "absDeltaX", "Math", "abs", "direction", "on", "classList", "add", "pointerType", "document", "documentElement", "navigator", "maxTouchPoints"], "mappings": ";;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAMA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,OAAb,CAAA;EACA,MAAMC,SAAS,GAAG,WAAlB,CAAA;EACA,MAAMC,gBAAgB,GAAI,CAAYD,UAAAA,EAAAA,SAAU,CAAhD,CAAA,CAAA;EACA,MAAME,eAAe,GAAI,CAAWF,SAAAA,EAAAA,SAAU,CAA9C,CAAA,CAAA;EACA,MAAMG,cAAc,GAAI,CAAUH,QAAAA,EAAAA,SAAU,CAA5C,CAAA,CAAA;EACA,MAAMI,iBAAiB,GAAI,CAAaJ,WAAAA,EAAAA,SAAU,CAAlD,CAAA,CAAA;EACA,MAAMK,eAAe,GAAI,CAAWL,SAAAA,EAAAA,SAAU,CAA9C,CAAA,CAAA;EACA,MAAMM,kBAAkB,GAAG,OAA3B,CAAA;EACA,MAAMC,gBAAgB,GAAG,KAAzB,CAAA;EACA,MAAMC,wBAAwB,GAAG,eAAjC,CAAA;EACA,MAAMC,eAAe,GAAG,EAAxB,CAAA;EAEA,MAAMC,OAAO,GAAG;EACdC,EAAAA,WAAW,EAAE,IADC;EAEdC,EAAAA,YAAY,EAAE,IAFA;EAGdC,EAAAA,aAAa,EAAE,IAAA;EAHD,CAAhB,CAAA;EAMA,MAAMC,WAAW,GAAG;EAClBH,EAAAA,WAAW,EAAE,iBADK;EAElBC,EAAAA,YAAY,EAAE,iBAFI;EAGlBC,EAAAA,aAAa,EAAE,iBAAA;EAHG,CAApB,CAAA;EAMA;EACA;EACA;;EAEA,MAAME,KAAN,SAAoBC,uBAApB,CAA2B;EACzBC,EAAAA,WAAW,CAACC,OAAD,EAAUC,MAAV,EAAkB;EAC3B,IAAA,KAAA,EAAA,CAAA;MACA,IAAKC,CAAAA,QAAL,GAAgBF,OAAhB,CAAA;;MAEA,IAAI,CAACA,OAAD,IAAY,CAACH,KAAK,CAACM,WAAN,EAAjB,EAAsC;EACpC,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKC,OAAL,GAAe,IAAA,CAAKC,UAAL,CAAgBJ,MAAhB,CAAf,CAAA;MACA,IAAKK,CAAAA,OAAL,GAAe,CAAf,CAAA;EACA,IAAA,IAAA,CAAKC,qBAAL,GAA6BC,OAAO,CAACC,MAAM,CAACC,YAAR,CAApC,CAAA;;EACA,IAAA,IAAA,CAAKC,WAAL,EAAA,CAAA;EACD,GAbwB;;;EAgBP,EAAA,WAAPnB,OAAO,GAAG;EACnB,IAAA,OAAOA,OAAP,CAAA;EACD,GAAA;;EAEqB,EAAA,WAAXI,WAAW,GAAG;EACvB,IAAA,OAAOA,WAAP,CAAA;EACD,GAAA;;EAEc,EAAA,WAAJf,IAAI,GAAG;EAChB,IAAA,OAAOA,IAAP,CAAA;EACD,GA1BwB;;;EA6BzB+B,EAAAA,OAAO,GAAG;EACRC,IAAAA,6BAAY,CAACC,GAAb,CAAiB,IAAKZ,CAAAA,QAAtB,EAAgCpB,SAAhC,CAAA,CAAA;EACD,GA/BwB;;;IAkCzBiC,MAAM,CAACC,KAAD,EAAQ;MACZ,IAAI,CAAC,IAAKT,CAAAA,qBAAV,EAAiC;QAC/B,IAAKD,CAAAA,OAAL,GAAeU,KAAK,CAACC,OAAN,CAAc,CAAd,EAAiBC,OAAhC,CAAA;EAEA,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAI,IAAKC,CAAAA,uBAAL,CAA6BH,KAA7B,CAAJ,EAAyC;EACvC,MAAA,IAAA,CAAKV,OAAL,GAAeU,KAAK,CAACE,OAArB,CAAA;EACD,KAAA;EACF,GAAA;;IAEDE,IAAI,CAACJ,KAAD,EAAQ;EACV,IAAA,IAAI,IAAKG,CAAAA,uBAAL,CAA6BH,KAA7B,CAAJ,EAAyC;EACvC,MAAA,IAAA,CAAKV,OAAL,GAAeU,KAAK,CAACE,OAAN,GAAgB,KAAKZ,OAApC,CAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKe,YAAL,EAAA,CAAA;;EACAC,IAAAA,aAAO,CAAC,IAAA,CAAKlB,OAAL,CAAaX,WAAd,CAAP,CAAA;EACD,GAAA;;IAED8B,KAAK,CAACP,KAAD,EAAQ;MACX,IAAKV,CAAAA,OAAL,GAAeU,KAAK,CAACC,OAAN,IAAiBD,KAAK,CAACC,OAAN,CAAcO,MAAd,GAAuB,CAAxC,GACb,CADa,GAEbR,KAAK,CAACC,OAAN,CAAc,CAAd,CAAiBC,CAAAA,OAAjB,GAA2B,IAAA,CAAKZ,OAFlC,CAAA;EAGD,GAAA;;EAEDe,EAAAA,YAAY,GAAG;MACb,MAAMI,SAAS,GAAGC,IAAI,CAACC,GAAL,CAAS,IAAA,CAAKrB,OAAd,CAAlB,CAAA;;MAEA,IAAImB,SAAS,IAAIlC,eAAjB,EAAkC;EAChC,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,MAAMqC,SAAS,GAAGH,SAAS,GAAG,KAAKnB,OAAnC,CAAA;MAEA,IAAKA,CAAAA,OAAL,GAAe,CAAf,CAAA;;MAEA,IAAI,CAACsB,SAAL,EAAgB;EACd,MAAA,OAAA;EACD,KAAA;;EAEDN,IAAAA,aAAO,CAACM,SAAS,GAAG,CAAZ,GAAgB,IAAKxB,CAAAA,OAAL,CAAaT,aAA7B,GAA6C,IAAA,CAAKS,OAAL,CAAaV,YAA3D,CAAP,CAAA;EACD,GAAA;;EAEDiB,EAAAA,WAAW,GAAG;MACZ,IAAI,IAAA,CAAKJ,qBAAT,EAAgC;EAC9BM,MAAAA,6BAAY,CAACgB,EAAb,CAAgB,IAAA,CAAK3B,QAArB,EAA+BhB,iBAA/B,EAAkD8B,KAAK,IAAI,IAAA,CAAKD,MAAL,CAAYC,KAAZ,CAA3D,CAAA,CAAA;EACAH,MAAAA,6BAAY,CAACgB,EAAb,CAAgB,IAAA,CAAK3B,QAArB,EAA+Bf,eAA/B,EAAgD6B,KAAK,IAAI,IAAA,CAAKI,IAAL,CAAUJ,KAAV,CAAzD,CAAA,CAAA;;EAEA,MAAA,IAAA,CAAKd,QAAL,CAAc4B,SAAd,CAAwBC,GAAxB,CAA4BzC,wBAA5B,CAAA,CAAA;EACD,KALD,MAKO;EACLuB,MAAAA,6BAAY,CAACgB,EAAb,CAAgB,IAAA,CAAK3B,QAArB,EAA+BnB,gBAA/B,EAAiDiC,KAAK,IAAI,IAAA,CAAKD,MAAL,CAAYC,KAAZ,CAA1D,CAAA,CAAA;EACAH,MAAAA,6BAAY,CAACgB,EAAb,CAAgB,IAAA,CAAK3B,QAArB,EAA+BlB,eAA/B,EAAgDgC,KAAK,IAAI,IAAA,CAAKO,KAAL,CAAWP,KAAX,CAAzD,CAAA,CAAA;EACAH,MAAAA,6BAAY,CAACgB,EAAb,CAAgB,IAAA,CAAK3B,QAArB,EAA+BjB,cAA/B,EAA+C+B,KAAK,IAAI,IAAA,CAAKI,IAAL,CAAUJ,KAAV,CAAxD,CAAA,CAAA;EACD,KAAA;EACF,GAAA;;IAEDG,uBAAuB,CAACH,KAAD,EAAQ;EAC7B,IAAA,OAAO,IAAKT,CAAAA,qBAAL,KAA+BS,KAAK,CAACgB,WAAN,KAAsB3C,gBAAtB,IAA0C2B,KAAK,CAACgB,WAAN,KAAsB5C,kBAA/F,CAAP,CAAA;EACD,GA9FwB;;;EAiGP,EAAA,OAAXe,WAAW,GAAG;MACnB,OAAO,cAAA,IAAkB8B,QAAQ,CAACC,eAA3B,IAA8CC,SAAS,CAACC,cAAV,GAA2B,CAAhF,CAAA;EACD,GAAA;;EAnGwB;;;;;;;;"}