{"version": 3, "file": "manipulator.js", "sources": ["../../src/dom/manipulator.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * <PERSON>trap (v5.2.3): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(value) {\n  if (value === 'true') {\n    return true\n  }\n\n  if (value === 'false') {\n    return false\n  }\n\n  if (value === Number(value).toString()) {\n    return Number(value)\n  }\n\n  if (value === '' || value === 'null') {\n    return null\n  }\n\n  if (typeof value !== 'string') {\n    return value\n  }\n\n  try {\n    return JSON.parse(decodeURIComponent(value))\n  } catch {\n    return value\n  }\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.toLowerCase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n    const bsKeys = Object.keys(element.dataset).filter(key => key.startsWith('bs') && !key.startsWith('bsConfig'))\n\n    for (const key of bsKeys) {\n      let pureKey = key.replace(/^bs/, '')\n      pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length)\n      attributes[pureKey] = normalizeData(element.dataset[key])\n    }\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  }\n}\n\nexport default Manipulator\n"], "names": ["normalizeData", "value", "Number", "toString", "JSON", "parse", "decodeURIComponent", "normalizeDataKey", "key", "replace", "chr", "toLowerCase", "Manipulator", "setDataAttribute", "element", "setAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "bs<PERSON><PERSON>s", "Object", "keys", "dataset", "filter", "startsWith", "pureKey", "char<PERSON>t", "slice", "length", "getDataAttribute", "getAttribute"], "mappings": ";;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAEA,SAASA,aAAT,CAAuBC,KAAvB,EAA8B;IAC5B,IAAIA,KAAK,KAAK,MAAd,EAAsB;EACpB,IAAA,OAAO,IAAP,CAAA;EACD,GAAA;;IAED,IAAIA,KAAK,KAAK,OAAd,EAAuB;EACrB,IAAA,OAAO,KAAP,CAAA;EACD,GAAA;;IAED,IAAIA,KAAK,KAAKC,MAAM,CAACD,KAAD,CAAN,CAAcE,QAAd,EAAd,EAAwC;MACtC,OAAOD,MAAM,CAACD,KAAD,CAAb,CAAA;EACD,GAAA;;EAED,EAAA,IAAIA,KAAK,KAAK,EAAV,IAAgBA,KAAK,KAAK,MAA9B,EAAsC;EACpC,IAAA,OAAO,IAAP,CAAA;EACD,GAAA;;EAED,EAAA,IAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;EAC7B,IAAA,OAAOA,KAAP,CAAA;EACD,GAAA;;IAED,IAAI;MACF,OAAOG,IAAI,CAACC,KAAL,CAAWC,kBAAkB,CAACL,KAAD,CAA7B,CAAP,CAAA;EACD,GAFD,CAEE,OAAM,OAAA,EAAA;EACN,IAAA,OAAOA,KAAP,CAAA;EACD,GAAA;EACF,CAAA;;EAED,SAASM,gBAAT,CAA0BC,GAA1B,EAA+B;EAC7B,EAAA,OAAOA,GAAG,CAACC,OAAJ,CAAY,QAAZ,EAAsBC,GAAG,IAAK,CAAA,CAAA,EAAGA,GAAG,CAACC,WAAJ,EAAkB,EAAnD,CAAP,CAAA;EACD,CAAA;;AAED,QAAMC,WAAW,GAAG;EAClBC,EAAAA,gBAAgB,CAACC,OAAD,EAAUN,GAAV,EAAeP,KAAf,EAAsB;MACpCa,OAAO,CAACC,YAAR,CAAsB,CAAUR,QAAAA,EAAAA,gBAAgB,CAACC,GAAD,CAAM,CAAtD,CAAA,EAAyDP,KAAzD,CAAA,CAAA;KAFgB;;EAKlBe,EAAAA,mBAAmB,CAACF,OAAD,EAAUN,GAAV,EAAe;MAChCM,OAAO,CAACG,eAAR,CAAyB,CAAA,QAAA,EAAUV,gBAAgB,CAACC,GAAD,CAAM,CAAzD,CAAA,CAAA,CAAA;KANgB;;IASlBU,iBAAiB,CAACJ,OAAD,EAAU;MACzB,IAAI,CAACA,OAAL,EAAc;EACZ,MAAA,OAAO,EAAP,CAAA;EACD,KAAA;;MAED,MAAMK,UAAU,GAAG,EAAnB,CAAA;MACA,MAAMC,MAAM,GAAGC,MAAM,CAACC,IAAP,CAAYR,OAAO,CAACS,OAApB,CAA6BC,CAAAA,MAA7B,CAAoChB,GAAG,IAAIA,GAAG,CAACiB,UAAJ,CAAe,IAAf,CAAwB,IAAA,CAACjB,GAAG,CAACiB,UAAJ,CAAe,UAAf,CAApE,CAAf,CAAA;;EAEA,IAAA,KAAK,MAAMjB,GAAX,IAAkBY,MAAlB,EAA0B;QACxB,IAAIM,OAAO,GAAGlB,GAAG,CAACC,OAAJ,CAAY,KAAZ,EAAmB,EAAnB,CAAd,CAAA;EACAiB,MAAAA,OAAO,GAAGA,OAAO,CAACC,MAAR,CAAe,CAAf,EAAkBhB,WAAlB,EAAA,GAAkCe,OAAO,CAACE,KAAR,CAAc,CAAd,EAAiBF,OAAO,CAACG,MAAzB,CAA5C,CAAA;EACAV,MAAAA,UAAU,CAACO,OAAD,CAAV,GAAsB1B,aAAa,CAACc,OAAO,CAACS,OAAR,CAAgBf,GAAhB,CAAD,CAAnC,CAAA;EACD,KAAA;;EAED,IAAA,OAAOW,UAAP,CAAA;KAvBgB;;EA0BlBW,EAAAA,gBAAgB,CAAChB,OAAD,EAAUN,GAAV,EAAe;EAC7B,IAAA,OAAOR,aAAa,CAACc,OAAO,CAACiB,YAAR,CAAsB,CAAUxB,QAAAA,EAAAA,gBAAgB,CAACC,GAAD,CAAM,CAAA,CAAtD,CAAD,CAApB,CAAA;EACD,GAAA;;EA5BiB;;;;;;;;"}