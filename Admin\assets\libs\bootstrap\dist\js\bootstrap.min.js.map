{"version": 3, "names": ["TRANSITION_END", "parseSelector", "selector", "window", "CSS", "escape", "replace", "match", "id", "triggerTransitionEnd", "element", "dispatchEvent", "Event", "isElement", "object", "j<PERSON>y", "nodeType", "getElement", "length", "document", "querySelector", "isVisible", "getClientRects", "elementIsVisible", "getComputedStyle", "getPropertyValue", "closedDetails", "closest", "summary", "parentNode", "isDisabled", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "getAttribute", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "DOMContentLoadedCallbacks", "isRTL", "dir", "defineJQueryPlugin", "plugin", "callback", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "readyState", "addEventListener", "push", "execute", "<PERSON><PERSON><PERSON><PERSON>", "args", "defaultValue", "executeAfterTransition", "transitionElement", "waitForTransition", "emulatedDuration", "transitionDuration", "transitionDelay", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "split", "getTransitionDurationFromElement", "called", "handler", "target", "removeEventListener", "setTimeout", "getNextActiveElement", "list", "activeElement", "shouldGetNext", "isCycleAllowed", "listLength", "index", "indexOf", "Math", "max", "min", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "Set", "makeEventUid", "uid", "getElementEvents", "<PERSON><PERSON><PERSON><PERSON>", "events", "callable", "delegationSelector", "Object", "values", "find", "event", "normalizeParameters", "originalTypeEvent", "delegationFunction", "isDelegated", "typeEvent", "getTypeEvent", "has", "add<PERSON><PERSON><PERSON>", "oneOff", "wrapFunction", "relatedTarget", "<PERSON><PERSON><PERSON><PERSON>", "call", "this", "handlers", "previousFunction", "dom<PERSON><PERSON>s", "querySelectorAll", "dom<PERSON>lement", "hydrateObj", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "bootstrapHandler", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "entries", "includes", "on", "one", "inNamespace", "isNamespace", "startsWith", "elementEvent", "keys", "slice", "keyHandlers", "trigger", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "evt", "cancelable", "preventDefault", "obj", "meta", "key", "value", "_unused", "defineProperty", "configurable", "get", "elementMap", "Map", "Data", "set", "instance", "instanceMap", "size", "console", "error", "Array", "from", "remove", "delete", "normalizeData", "toString", "JSON", "parse", "decodeURIComponent", "normalizeDataKey", "chr", "toLowerCase", "Manipulator", "setDataAttribute", "setAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "bs<PERSON><PERSON>s", "dataset", "filter", "pureKey", "char<PERSON>t", "getDataAttribute", "Config", "<PERSON><PERSON><PERSON>", "DefaultType", "Error", "_getConfig", "config", "_mergeConfigObj", "_configAfterMerge", "_typeCheckConfig", "jsonConfig", "constructor", "configTypes", "property", "expectedTypes", "valueType", "prototype", "RegExp", "test", "TypeError", "toUpperCase", "BaseComponent", "super", "_element", "_config", "DATA_KEY", "dispose", "EVENT_KEY", "propertyName", "getOwnPropertyNames", "_queueCallback", "isAnimated", "static", "getInstance", "VERSION", "getSelector", "hrefAttribute", "trim", "SelectorEngine", "concat", "Element", "findOne", "children", "child", "matches", "parents", "ancestor", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "focusableC<PERSON><PERSON>n", "focusables", "map", "join", "el", "getSelectorFromElement", "getElementFromSelector", "getMultipleElementsFromSelector", "enableDismissTrigger", "component", "method", "clickEvent", "tagName", "getOrCreateInstance", "<PERSON><PERSON>", "close", "_destroyElement", "each", "data", "undefined", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON>", "toggle", "button", "endCallback", "leftCallback", "<PERSON><PERSON><PERSON><PERSON>", "Swipe", "isSupported", "_deltaX", "_supportPointerEvents", "PointerEvent", "_initEvents", "_start", "_eventIsPointerPenTouch", "clientX", "touches", "_end", "_handleSwipe", "_move", "absDeltaX", "abs", "direction", "add", "pointerType", "navigator", "maxTouchPoints", "ORDER_NEXT", "ORDER_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLID", "CLASS_NAME_CAROUSEL", "CLASS_NAME_ACTIVE", "KEY_TO_DIRECTION", "ArrowLeft", "ArrowRight", "interval", "keyboard", "pause", "ride", "touch", "wrap", "Carousel", "_interval", "_activeElement", "_isSliding", "touchTimeout", "_swipe<PERSON><PERSON>per", "_indicatorsElement", "_addEventListeners", "cycle", "_slide", "nextWhenVisible", "hidden", "_clearInterval", "_updateInterval", "setInterval", "_maybeEnableCycle", "to", "items", "_getItems", "activeIndex", "_getItemIndex", "_getActive", "order", "defaultInterval", "_keydown", "_addTouchEventListeners", "img", "swipeConfig", "_directionToOrder", "clearTimeout", "_setActiveIndicatorElement", "activeIndicator", "newActiveIndicator", "elementInterval", "parseInt", "isNext", "nextElement", "nextElementIndex", "triggerEvent", "eventName", "_orderToDirection", "isCycling", "directionalClassName", "orderClassName", "_isAnimated", "SELECTOR_ACTIVE", "clearInterval", "carousel", "slideIndex", "carousels", "CLASS_NAME_SHOW", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "parent", "Collapse", "_isTransitioning", "_triggerArray", "toggleList", "elem", "filterElement", "foundElement", "_initializeC<PERSON><PERSON>n", "_addAriaAndCollapsedClass", "_isShown", "hide", "show", "activeC<PERSON><PERSON>n", "_getFirstLevelChildren", "activeInstance", "dimension", "_getDimension", "style", "scrollSize", "getBoundingClientRect", "selected", "trigger<PERSON><PERSON>y", "isOpen", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "EVENT_CLICK_DATA_API", "EVENT_KEYDOWN_DATA_API", "SELECTOR_DATA_TOGGLE_SHOWN", "SELECTOR_MENU", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "autoClose", "boundary", "display", "offset", "popperConfig", "reference", "Dropdown", "_popper", "_parent", "_menu", "_inNavbar", "_detectNavbar", "_createPopper", "focus", "_completeHide", "destroy", "update", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "createPopper", "_getPlacement", "parentDropdown", "isEnd", "_getOffset", "popperData", "defaultBsPopperConfig", "placement", "modifiers", "options", "enabled", "_selectMenuItem", "openToggles", "context", "<PERSON><PERSON><PERSON>", "isMenuTarget", "isInput", "isEscapeEvent", "isUpOrDownEvent", "getToggleButton", "stopPropagation", "dataApiKeydownHandler", "clearMenus", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "PROPERTY_PADDING", "PROPERTY_MARGIN", "ScrollBarHelper", "getWidth", "documentWidth", "clientWidth", "innerWidth", "width", "_disableOver<PERSON>low", "_setElementAttributes", "calculatedValue", "reset", "_resetElementAttributes", "isOverflowing", "_saveInitialAttribute", "overflow", "styleProperty", "scrollbarWidth", "_applyManipulationCallback", "setProperty", "actualValue", "removeProperty", "callBack", "sel", "EVENT_MOUSEDOWN", "className", "clickCallback", "rootElement", "Backdrop", "_isAppended", "_append", "_getElement", "_emulateAnimation", "backdrop", "createElement", "append", "TAB_NAV_BACKWARD", "autofocus", "trapElement", "FocusTrap", "_isActive", "_lastTabNavDirection", "activate", "_handleFocusin", "_handleKeydown", "deactivate", "elements", "shift<PERSON>ey", "EVENT_HIDDEN", "EVENT_SHOW", "CLASS_NAME_OPEN", "CLASS_NAME_STATIC", "Modal", "_dialog", "_backdrop", "_initializeBackDrop", "_focustrap", "_initializeFocusTrap", "_scrollBar", "_adjustDialog", "_showElement", "_hideModal", "htmlElement", "handleUpdate", "scrollTop", "modalBody", "_triggerBackdropTransition", "event2", "_resetAdjustments", "isModalOverflowing", "scrollHeight", "clientHeight", "initialOverflowY", "overflowY", "isBodyOverflowing", "paddingLeft", "paddingRight", "showEvent", "alreadyOpen", "CLASS_NAME_SHOWING", "CLASS_NAME_HIDING", "OPEN_SELECTOR", "EVENT_HIDE_PREVENTED", "scroll", "<PERSON><PERSON><PERSON>", "blur", "position", "uriAttributes", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attribute", "allowedAttributeList", "attributeName", "nodeName", "nodeValue", "attributeRegex", "some", "regex", "DefaultAllowlist", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "i", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "allowList", "content", "extraClass", "html", "sanitize", "sanitizeFn", "template", "DefaultContentType", "entry", "TemplateFactory", "get<PERSON>ontent", "_resolvePossibleFunction", "<PERSON><PERSON><PERSON><PERSON>", "changeContent", "_checkContent", "toHtml", "templateWrapper", "innerHTML", "_maybeSanitize", "text", "_setContent", "arg", "templateElement", "_putElementInTemplate", "textContent", "unsafeHtml", "sanitizeFunction", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "elementName", "attributeList", "allowedAttributes", "sanitizeHtml", "DISALLOWED_ATTRIBUTES", "CLASS_NAME_FADE", "SELECTOR_MODAL", "EVENT_MODAL_HIDE", "TRIGGER_HOVER", "TRIGGER_FOCUS", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "animation", "container", "customClass", "delay", "fallbackPlacements", "title", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_isHovered", "_activeTrigger", "_templateFactory", "_newContent", "tip", "_setListeners", "_fixTitle", "enable", "disable", "toggle<PERSON>nabled", "click", "_leave", "_enter", "_hideModalHandler", "_disposePopper", "_isWithContent", "isInTheDom", "ownerDocument", "_getTipElement", "_isWithActiveTrigger", "_getTitle", "_createTipElement", "_getContentForTemplate", "_getTemplateFactory", "tipId", "prefix", "floor", "random", "getElementById", "getUID", "<PERSON><PERSON><PERSON><PERSON>", "_initializeOnDelegatedTarget", "_getDelegateConfig", "attachment", "phase", "state", "triggers", "eventIn", "eventOut", "_setTimeout", "timeout", "dataAttributes", "dataAttribute", "Popover", "_getContent", "EVENT_CLICK", "SELECTOR_TARGET_LINKS", "rootMargin", "smoothScroll", "threshold", "ScrollSpy", "_targetLinks", "_observableSections", "_rootElement", "_activeTarget", "_observer", "_previousScrollData", "visibleEntryTop", "parentScrollTop", "refresh", "_initializeTargetsAndObservables", "_maybeEnableSmoothScroll", "disconnect", "_getNewObserver", "section", "observe", "observableSection", "hash", "height", "offsetTop", "scrollTo", "top", "behavior", "IntersectionObserver", "_<PERSON><PERSON><PERSON><PERSON>", "targetElement", "_process", "userScrollsDown", "isIntersecting", "_clearActiveClass", "entryIsLowerThanPrevious", "targetLinks", "anchor", "_activateParents", "listGroup", "item", "activeNodes", "node", "spy", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "SELECTOR_INNER_ELEM", "Tab", "_setInitialAttributes", "_get<PERSON><PERSON><PERSON>n", "innerElem", "_elemIsActive", "active", "_getActiveElem", "hideEvent", "_deactivate", "_activate", "relatedElem", "_toggleDropDown", "nextActiveElement", "preventScroll", "_setAttributeIfNotExists", "_setInitialAttributesOnChild", "_getInnerElement", "isActive", "outerElem", "_getOuterElement", "_setInitialAttributesOnTargetPanel", "open", "CLASS_NAME_HIDE", "autohide", "Toast", "_hasMouseInteraction", "_hasKeyboardInteraction", "_clearTimeout", "_maybeScheduleHide", "isShown", "_onInteraction", "isInteracting"], "sources": ["../../js/src/util/index.js", "../../js/src/dom/event-handler.js", "../../js/src/dom/data.js", "../../js/src/dom/manipulator.js", "../../js/src/util/config.js", "../../js/src/base-component.js", "../../js/src/dom/selector-engine.js", "../../js/src/util/component-functions.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/util/swipe.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../js/src/dropdown.js", "../../js/src/util/scrollbar.js", "../../js/src/util/backdrop.js", "../../js/src/util/focustrap.js", "../../js/src/modal.js", "../../js/src/offcanvas.js", "../../js/src/util/sanitizer.js", "../../js/src/util/template-factory.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../js/index.umd.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1_000_000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n/**\n * Properly escape IDs selectors to handle weird IDs\n * @param {string} selector\n * @returns {string}\n */\nconst parseSelector = selector => {\n  if (selector && window.CSS && window.CSS.escape) {\n    // document.querySelector needs escaping to handle IDs (html5+) containing for instance /\n    selector = selector.replace(/#([^\\s\"#']+)/g, (match, id) => `#${CSS.escape(id)}`)\n  }\n\n  return selector\n}\n\n// Shout-out Angus Croll (https://goo.gl/pxwQGp)\nconst toType = object => {\n  if (object === null || object === undefined) {\n    return `${object}`\n  }\n\n  return Object.prototype.toString.call(object).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * Public Util API\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = object => {\n  if (!object || typeof object !== 'object') {\n    return false\n  }\n\n  if (typeof object.jquery !== 'undefined') {\n    object = object[0]\n  }\n\n  return typeof object.nodeType !== 'undefined'\n}\n\nconst getElement = object => {\n  // it's a jQuery object or a node element\n  if (isElement(object)) {\n    return object.jquery ? object[0] : object\n  }\n\n  if (typeof object === 'string' && object.length > 0) {\n    return document.querySelector(parseSelector(object))\n  }\n\n  return null\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  const elementIsVisible = getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n  // Handle `details` element as its content may falsie appear visible when it is closed\n  const closedDetails = element.closest('details:not([open])')\n\n  if (!closedDetails) {\n    return elementIsVisible\n  }\n\n  if (closedDetails !== element) {\n    const summary = element.closest('summary')\n    if (summary && summary.parentNode !== closedDetails) {\n      return false\n    }\n\n    if (summary === null) {\n      return false\n    }\n  }\n\n  return elementIsVisible\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\n/**\n * Trick to restart an element's animation\n *\n * @param {HTMLElement} element\n * @return void\n *\n * @see https://www.charistheo.io/blog/2021/02/restart-a-css-animation-with-javascript/#restarting-a-css-animation\n */\nconst reflow = element => {\n  element.offsetHeight // eslint-disable-line no-unused-expressions\n}\n\nconst getjQuery = () => {\n  if (window.jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return window.jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        for (const callback of DOMContentLoadedCallbacks) {\n          callback()\n        }\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = (possibleCallback, args = [], defaultValue = possibleCallback) => {\n  return typeof possibleCallback === 'function' ? possibleCallback(...args) : defaultValue\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  const listLength = list.length\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element\n  // depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return !shouldGetNext && isCycleAllowed ? list[listLength - 1] : list[0]\n  }\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition,\n  findShadowRoot,\n  getElement,\n  getjQuery,\n  getNextActiveElement,\n  getTransitionDurationFromElement,\n  getUID,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop,\n  onDOMContentLoaded,\n  parseSelector,\n  reflow,\n  triggerTransitionEnd,\n  toType\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index.js'\n\n/**\n * Constants\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\n\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * Private methods\n */\n\nfunction makeEventUid(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getElementEvents(element) {\n  const uid = makeEventUid(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    hydrateObj(event, { delegateTarget: element })\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (const domElement of domElements) {\n        if (domElement !== target) {\n          continue\n        }\n\n        hydrateObj(event, { delegateTarget: target })\n\n        if (handler.oneOff) {\n          EventHandler.off(element, event.type, selector, fn)\n        }\n\n        return fn.apply(target, [event])\n      }\n    }\n  }\n}\n\nfunction findHandler(events, callable, delegationSelector = null) {\n  return Object.values(events)\n    .find(event => event.callable === callable && event.delegationSelector === delegationSelector)\n}\n\nfunction normalizeParameters(originalTypeEvent, handler, delegationFunction) {\n  const isDelegated = typeof handler === 'string'\n  // todo: tooltip passes `false` instead of selector, so we need to check\n  const callable = isDelegated ? delegationFunction : (handler || delegationFunction)\n  let typeEvent = getTypeEvent(originalTypeEvent)\n\n  if (!nativeEvents.has(typeEvent)) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [isDelegated, callable, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFunction, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  let [isDelegated, callable, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n\n  // in case of mouseenter or mouseleave wrap the handler within a function that checks for its DOM position\n  // this prevents the handler from being dispatched the same way as mouseover or mouseout does\n  if (originalTypeEvent in customEvents) {\n    const wrapFunction = fn => {\n      return function (event) {\n        if (!event.relatedTarget || (event.relatedTarget !== event.delegateTarget && !event.delegateTarget.contains(event.relatedTarget))) {\n          return fn.call(this, event)\n        }\n      }\n    }\n\n    callable = wrapFunction(callable)\n  }\n\n  const events = getElementEvents(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFunction = findHandler(handlers, callable, isDelegated ? handler : null)\n\n  if (previousFunction) {\n    previousFunction.oneOff = previousFunction.oneOff && oneOff\n\n    return\n  }\n\n  const uid = makeEventUid(callable, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = isDelegated ?\n    bootstrapDelegationHandler(element, handler, callable) :\n    bootstrapHandler(element, callable)\n\n  fn.delegationSelector = isDelegated ? handler : null\n  fn.callable = callable\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, isDelegated)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  for (const [handlerKey, event] of Object.entries(storeElementEvent)) {\n    if (handlerKey.includes(namespace)) {\n      removeHandler(element, events, typeEvent, event.callable, event.delegationSelector)\n    }\n  }\n}\n\nfunction getTypeEvent(event) {\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  event = event.replace(stripNameRegex, '')\n  return customEvents[event] || event\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, false)\n  },\n\n  one(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFunction) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [isDelegated, callable, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getElementEvents(element)\n    const storeElementEvent = events[typeEvent] || {}\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof callable !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!Object.keys(storeElementEvent).length) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, callable, isDelegated ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      for (const elementEvent of Object.keys(events)) {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      }\n    }\n\n    for (const [keyHandlers, event] of Object.entries(storeElementEvent)) {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        removeHandler(element, events, typeEvent, event.callable, event.delegationSelector)\n      }\n    }\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = getTypeEvent(event)\n    const inNamespace = event !== typeEvent\n\n    let jQueryEvent = null\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    let evt = new Event(event, { bubbles, cancelable: true })\n    evt = hydrateObj(evt, args)\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && jQueryEvent) {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nfunction hydrateObj(obj, meta = {}) {\n  for (const [key, value] of Object.entries(meta)) {\n    try {\n      obj[key] = value\n    } catch {\n      Object.defineProperty(obj, key, {\n        configurable: true,\n        get() {\n          return value\n        }\n      })\n    }\n  }\n\n  return obj\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * Constants\n */\n\nconst elementMap = new Map()\n\nexport default {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map())\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`)\n      return\n    }\n\n    instanceMap.set(key, instance)\n  },\n\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null\n    }\n\n    return null\n  },\n\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    instanceMap.delete(key)\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element)\n    }\n  }\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(value) {\n  if (value === 'true') {\n    return true\n  }\n\n  if (value === 'false') {\n    return false\n  }\n\n  if (value === Number(value).toString()) {\n    return Number(value)\n  }\n\n  if (value === '' || value === 'null') {\n    return null\n  }\n\n  if (typeof value !== 'string') {\n    return value\n  }\n\n  try {\n    return JSON.parse(decodeURIComponent(value))\n  } catch {\n    return value\n  }\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.toLowerCase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n    const bsKeys = Object.keys(element.dataset).filter(key => key.startsWith('bs') && !key.startsWith('bsConfig'))\n\n    for (const key of bsKeys) {\n      let pureKey = key.replace(/^bs/, '')\n      pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length)\n      attributes[pureKey] = normalizeData(element.dataset[key])\n    }\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): util/config.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { isElement, toType } from './index.js'\nimport Manipulator from '../dom/manipulator.js'\n\n/**\n * Class definition\n */\n\nclass Config {\n  // Getters\n  static get Default() {\n    return {}\n  }\n\n  static get DefaultType() {\n    return {}\n  }\n\n  static get NAME() {\n    throw new Error('You have to implement the static method \"NAME\", for each component!')\n  }\n\n  _getConfig(config) {\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    return config\n  }\n\n  _mergeConfigObj(config, element) {\n    const jsonConfig = isElement(element) ? Manipulator.getDataAttribute(element, 'config') : {} // try to parse\n\n    return {\n      ...this.constructor.Default,\n      ...(typeof jsonConfig === 'object' ? jsonConfig : {}),\n      ...(isElement(element) ? Manipulator.getDataAttributes(element) : {}),\n      ...(typeof config === 'object' ? config : {})\n    }\n  }\n\n  _typeCheckConfig(config, configTypes = this.constructor.DefaultType) {\n    for (const [property, expectedTypes] of Object.entries(configTypes)) {\n      const value = config[property]\n      const valueType = isElement(value) ? 'element' : toType(value)\n\n      if (!new RegExp(expectedTypes).test(valueType)) {\n        throw new TypeError(\n          `${this.constructor.NAME.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n        )\n      }\n    }\n  }\n}\n\nexport default Config\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data.js'\nimport { executeAfterTransition, getElement } from './util/index.js'\nimport EventHandler from './dom/event-handler.js'\nimport Config from './util/config.js'\n\n/**\n * Constants\n */\n\nconst VERSION = '5.3.0-alpha1'\n\n/**\n * Class definition\n */\n\nclass BaseComponent extends Config {\n  constructor(element, config) {\n    super()\n\n    element = getElement(element)\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    this._config = this._getConfig(config)\n\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  // Public\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n\n    for (const propertyName of Object.getOwnPropertyNames(this)) {\n      this[propertyName] = null\n    }\n  }\n\n  _queueCallback(callback, element, isAnimated = true) {\n    executeAfterTransition(callback, element, isAnimated)\n  }\n\n  _getConfig(config) {\n    config = this._mergeConfigObj(config, this._element)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  // Static\n  static getInstance(element) {\n    return Data.get(getElement(element), this.DATA_KEY)\n  }\n\n  static getOrCreateInstance(element, config = {}) {\n    return this.getInstance(element) || new this(element, typeof config === 'object' ? config : null)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DATA_KEY() {\n    return `bs.${this.NAME}`\n  }\n\n  static get EVENT_KEY() {\n    return `.${this.DATA_KEY}`\n  }\n\n  static eventName(name) {\n    return `${name}${this.EVENT_KEY}`\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { isDisabled, isVisible, parseSelector } from '../util/index.js'\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttribute = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttribute || (!hrefAttribute.includes('#') && !hrefAttribute.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttribute.includes('#') && !hrefAttribute.startsWith('#')) {\n      hrefAttribute = `#${hrefAttribute.split('#')[1]}`\n    }\n\n    selector = hrefAttribute && hrefAttribute !== '#' ? hrefAttribute.trim() : null\n  }\n\n  return parseSelector(selector)\n}\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children).filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n    let ancestor = element.parentNode.closest(selector)\n\n    while (ancestor) {\n      parents.push(ancestor)\n      ancestor = ancestor.parentNode.closest(selector)\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n  // TODO: this is now unused; remove later along with prev()\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  },\n\n  focusableChildren(element) {\n    const focusables = [\n      'a',\n      'button',\n      'input',\n      'textarea',\n      'select',\n      'details',\n      '[tabindex]',\n      '[contenteditable=\"true\"]'\n    ].map(selector => `${selector}:not([tabindex^=\"-\"])`).join(',')\n\n    return this.find(focusables, element).filter(el => !isDisabled(el) && isVisible(el))\n  },\n\n  getSelectorFromElement(element) {\n    const selector = getSelector(element)\n\n    if (selector) {\n      return SelectorEngine.findOne(selector) ? selector : null\n    }\n\n    return null\n  },\n\n  getElementFromSelector(element) {\n    const selector = getSelector(element)\n\n    return selector ? SelectorEngine.findOne(selector) : null\n  },\n\n  getMultipleElementsFromSelector(element) {\n    const selector = getSelector(element)\n\n    return selector ? SelectorEngine.find(selector) : []\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): util/component-functions.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport { isDisabled } from './index.js'\nimport SelectorEngine from '../dom/selector-engine.js'\n\nconst enableDismissTrigger = (component, method = 'hide') => {\n  const clickEvent = `click.dismiss${component.EVENT_KEY}`\n  const name = component.NAME\n\n  EventHandler.on(document, clickEvent, `[data-bs-dismiss=\"${name}\"]`, function (event) {\n    if (['A', 'AREA'].includes(this.tagName)) {\n      event.preventDefault()\n    }\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const target = SelectorEngine.getElementFromSelector(this) || this.closest(`.${name}`)\n    const instance = component.getOrCreateInstance(target)\n\n    // Method argument is left, for Alert and only, as it doesn't implement the 'hide' method\n    instance[method]()\n  })\n}\n\nexport {\n  enableDismissTrigger\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index.js'\nimport EventHandler from './dom/event-handler.js'\nimport BaseComponent from './base-component.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * Class definition\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  close() {\n    const closeEvent = EventHandler.trigger(this._element, EVENT_CLOSE)\n\n    if (closeEvent.defaultPrevented) {\n      return\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    const isAnimated = this._element.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(() => this._destroyElement(), this._element, isAnimated)\n  }\n\n  // Private\n  _destroyElement() {\n    this._element.remove()\n    EventHandler.trigger(this._element, EVENT_CLOSED)\n    this.dispose()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Alert.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Alert, 'close')\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Alert)\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index.js'\nimport EventHandler from './dom/event-handler.js'\nimport BaseComponent from './base-component.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * Class definition\n */\n\nclass Button extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Button.getOrCreateInstance(this)\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n  const data = Button.getOrCreateInstance(button)\n\n  data.toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Button)\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): util/swipe.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Config from './config.js'\nimport EventHandler from '../dom/event-handler.js'\nimport { execute } from './index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'swipe'\nconst EVENT_KEY = '.bs.swipe'\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  endCallback: null,\n  leftCallback: null,\n  rightCallback: null\n}\n\nconst DefaultType = {\n  endCallback: '(function|null)',\n  leftCallback: '(function|null)',\n  rightCallback: '(function|null)'\n}\n\n/**\n * Class definition\n */\n\nclass Swipe extends Config {\n  constructor(element, config) {\n    super()\n    this._element = element\n\n    if (!element || !Swipe.isSupported()) {\n      return\n    }\n\n    this._config = this._getConfig(config)\n    this._deltaX = 0\n    this._supportPointerEvents = Boolean(window.PointerEvent)\n    this._initEvents()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n  }\n\n  // Private\n  _start(event) {\n    if (!this._supportPointerEvents) {\n      this._deltaX = event.touches[0].clientX\n\n      return\n    }\n\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX\n    }\n  }\n\n  _end(event) {\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX - this._deltaX\n    }\n\n    this._handleSwipe()\n    execute(this._config.endCallback)\n  }\n\n  _move(event) {\n    this._deltaX = event.touches && event.touches.length > 1 ?\n      0 :\n      event.touches[0].clientX - this._deltaX\n  }\n\n  _handleSwipe() {\n    const absDeltaX = Math.abs(this._deltaX)\n\n    if (absDeltaX <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltaX / this._deltaX\n\n    this._deltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    execute(direction > 0 ? this._config.rightCallback : this._config.leftCallback)\n  }\n\n  _initEvents() {\n    if (this._supportPointerEvents) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => this._start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => this._end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => this._start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => this._move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => this._end(event))\n    }\n  }\n\n  _eventIsPointerPenTouch(event) {\n    return this._supportPointerEvents && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)\n  }\n\n  // Static\n  static isSupported() {\n    return 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n  }\n}\n\nexport default Swipe\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getNextActiveElement,\n  isRTL,\n  isVisible,\n  reflow,\n  triggerTransitionEnd\n} from './util/index.js'\nimport EventHandler from './dom/event-handler.js'\nimport Manipulator from './dom/manipulator.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport Swipe from './util/swipe.js'\nimport BaseComponent from './base-component.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ACTIVE_ITEM = SELECTOR_ACTIVE + SELECTOR_ITEM\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst KEY_TO_DIRECTION = {\n  [ARROW_LEFT_KEY]: DIRECTION_RIGHT,\n  [ARROW_RIGHT_KEY]: DIRECTION_LEFT\n}\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  pause: 'hover',\n  ride: false,\n  touch: true,\n  wrap: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)', // TODO:v6 remove boolean support\n  keyboard: 'boolean',\n  pause: '(string|boolean)',\n  ride: '(boolean|string)',\n  touch: 'boolean',\n  wrap: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._interval = null\n    this._activeElement = null\n    this._isSliding = false\n    this.touchTimeout = null\n    this._swipeHelper = null\n\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._addEventListeners()\n\n    if (this._config.ride === CLASS_NAME_CAROUSEL) {\n      this.cycle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  next() {\n    this._slide(ORDER_NEXT)\n  }\n\n  nextWhenVisible() {\n    // FIXME TODO use `document.visibilityState`\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    this._slide(ORDER_PREV)\n  }\n\n  pause() {\n    if (this._isSliding) {\n      triggerTransitionEnd(this._element)\n    }\n\n    this._clearInterval()\n  }\n\n  cycle() {\n    this._clearInterval()\n    this._updateInterval()\n\n    this._interval = setInterval(() => this.nextWhenVisible(), this._config.interval)\n  }\n\n  _maybeEnableCycle() {\n    if (!this._config.ride) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.cycle())\n      return\n    }\n\n    this.cycle()\n  }\n\n  to(index) {\n    const items = this._getItems()\n    if (index > items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    const activeIndex = this._getItemIndex(this._getActive())\n    if (activeIndex === index) {\n      return\n    }\n\n    const order = index > activeIndex ? ORDER_NEXT : ORDER_PREV\n\n    this._slide(order, items[index])\n  }\n\n  dispose() {\n    if (this._swipeHelper) {\n      this._swipeHelper.dispose()\n    }\n\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.defaultInterval = config.interval\n    return config\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, () => this.pause())\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, () => this._maybeEnableCycle())\n    }\n\n    if (this._config.touch && Swipe.isSupported()) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    for (const img of SelectorEngine.find(SELECTOR_ITEM_IMG, this._element)) {\n      EventHandler.on(img, EVENT_DRAG_START, event => event.preventDefault())\n    }\n\n    const endCallBack = () => {\n      if (this._config.pause !== 'hover') {\n        return\n      }\n\n      // If it's a touch-enabled device, mouseenter/leave are fired as\n      // part of the mouse compatibility events on first tap - the carousel\n      // would stop cycling until user tapped out of it;\n      // here, we listen for touchend, explicitly pause the carousel\n      // (as if it's the second time we tap on it, mouseenter compat event\n      // is NOT fired) and after a timeout (to allow for mouse compatibility\n      // events to fire) we explicitly restart cycling\n\n      this.pause()\n      if (this.touchTimeout) {\n        clearTimeout(this.touchTimeout)\n      }\n\n      this.touchTimeout = setTimeout(() => this._maybeEnableCycle(), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n    }\n\n    const swipeConfig = {\n      leftCallback: () => this._slide(this._directionToOrder(DIRECTION_LEFT)),\n      rightCallback: () => this._slide(this._directionToOrder(DIRECTION_RIGHT)),\n      endCallback: endCallBack\n    }\n\n    this._swipeHelper = new Swipe(this._element, swipeConfig)\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    const direction = KEY_TO_DIRECTION[event.key]\n    if (direction) {\n      event.preventDefault()\n      this._slide(this._directionToOrder(direction))\n    }\n  }\n\n  _getItemIndex(element) {\n    return this._getItems().indexOf(element)\n  }\n\n  _setActiveIndicatorElement(index) {\n    if (!this._indicatorsElement) {\n      return\n    }\n\n    const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n    activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n    activeIndicator.removeAttribute('aria-current')\n\n    const newActiveIndicator = SelectorEngine.findOne(`[data-bs-slide-to=\"${index}\"]`, this._indicatorsElement)\n\n    if (newActiveIndicator) {\n      newActiveIndicator.classList.add(CLASS_NAME_ACTIVE)\n      newActiveIndicator.setAttribute('aria-current', 'true')\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || this._getActive()\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    this._config.interval = elementInterval || this._config.defaultInterval\n  }\n\n  _slide(order, element = null) {\n    if (this._isSliding) {\n      return\n    }\n\n    const activeElement = this._getActive()\n    const isNext = order === ORDER_NEXT\n    const nextElement = element || getNextActiveElement(this._getItems(), activeElement, isNext, this._config.wrap)\n\n    if (nextElement === activeElement) {\n      return\n    }\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n\n    const triggerEvent = eventName => {\n      return EventHandler.trigger(this._element, eventName, {\n        relatedTarget: nextElement,\n        direction: this._orderToDirection(order),\n        from: this._getItemIndex(activeElement),\n        to: nextElementIndex\n      })\n    }\n\n    const slideEvent = triggerEvent(EVENT_SLIDE)\n\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      // todo: change tests that use empty divs to avoid this check\n      return\n    }\n\n    const isCycling = Boolean(this._interval)\n    this.pause()\n\n    this._isSliding = true\n\n    this._setActiveIndicatorElement(nextElementIndex)\n    this._activeElement = nextElement\n\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n\n    nextElement.classList.add(orderClassName)\n\n    reflow(nextElement)\n\n    activeElement.classList.add(directionalClassName)\n    nextElement.classList.add(directionalClassName)\n\n    const completeCallBack = () => {\n      nextElement.classList.remove(directionalClassName, orderClassName)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n      this._isSliding = false\n\n      triggerEvent(EVENT_SLID)\n    }\n\n    this._queueCallback(completeCallBack, activeElement, this._isAnimated())\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_SLIDE)\n  }\n\n  _getActive() {\n    return SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n  }\n\n  _getItems() {\n    return SelectorEngine.find(SELECTOR_ITEM, this._element)\n  }\n\n  _clearInterval() {\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (isRTL()) {\n      return direction === DIRECTION_LEFT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_LEFT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (isRTL()) {\n      return order === ORDER_PREV ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_PREV ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Carousel.getOrCreateInstance(this, config)\n\n      if (typeof config === 'number') {\n        data.to(config)\n        return\n      }\n\n      if (typeof config === 'string') {\n        if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n    return\n  }\n\n  event.preventDefault()\n\n  const carousel = Carousel.getOrCreateInstance(target)\n  const slideIndex = this.getAttribute('data-bs-slide-to')\n\n  if (slideIndex) {\n    carousel.to(slideIndex)\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  if (Manipulator.getDataAttribute(this, 'slide') === 'next') {\n    carousel.next()\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  carousel.prev()\n  carousel._maybeEnableCycle()\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (const carousel of carousels) {\n    Carousel.getOrCreateInstance(carousel)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Carousel)\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  reflow\n} from './util/index.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport BaseComponent from './base-component.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\nconst CLASS_NAME_DEEPER_CHILDREN = `:scope .${CLASS_NAME_COLLAPSE} .${CLASS_NAME_COLLAPSE}`\nconst CLASS_NAME_HORIZONTAL = 'collapse-horizontal'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.collapse.show, .collapse.collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\nconst Default = {\n  parent: null,\n  toggle: true\n}\n\nconst DefaultType = {\n  parent: '(null|element)',\n  toggle: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isTransitioning = false\n    this._triggerArray = []\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (const elem of toggleList) {\n      const selector = SelectorEngine.getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElement => foundElement === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._initializeChildren()\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._triggerArray, this._isShown())\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    if (this._isShown()) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._isShown()) {\n      return\n    }\n\n    let activeChildren = []\n\n    // find active children\n    if (this._config.parent) {\n      activeChildren = this._getFirstLevelChildren(SELECTOR_ACTIVES)\n        .filter(element => element !== this._element)\n        .map(element => Collapse.getOrCreateInstance(element, { toggle: false }))\n    }\n\n    if (activeChildren.length && activeChildren[0]._isTransitioning) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    for (const activeInstance of activeChildren) {\n      activeInstance.hide()\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    this._addAriaAndCollapsedClass(this._triggerArray, true)\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n\n    this._queueCallback(complete, this._element, true)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._isShown()) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    for (const trigger of this._triggerArray) {\n      const element = SelectorEngine.getElementFromSelector(trigger)\n\n      if (element && !this._isShown(element)) {\n        this._addAriaAndCollapsedClass([trigger], false)\n      }\n    }\n\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n\n    this._queueCallback(complete, this._element, true)\n  }\n\n  _isShown(element = this._element) {\n    return element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    config.parent = getElement(config.parent)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(CLASS_NAME_HORIZONTAL) ? WIDTH : HEIGHT\n  }\n\n  _initializeChildren() {\n    if (!this._config.parent) {\n      return\n    }\n\n    const children = this._getFirstLevelChildren(SELECTOR_DATA_TOGGLE)\n\n    for (const element of children) {\n      const selected = SelectorEngine.getElementFromSelector(element)\n\n      if (selected) {\n        this._addAriaAndCollapsedClass([element], this._isShown(selected))\n      }\n    }\n  }\n\n  _getFirstLevelChildren(selector) {\n    const children = SelectorEngine.find(CLASS_NAME_DEEPER_CHILDREN, this._config.parent)\n    // remove children if greater depth\n    return SelectorEngine.find(selector, this._config.parent).filter(element => !children.includes(element))\n  }\n\n  _addAriaAndCollapsedClass(triggerArray, isOpen) {\n    if (!triggerArray.length) {\n      return\n    }\n\n    for (const element of triggerArray) {\n      element.classList.toggle(CLASS_NAME_COLLAPSED, !isOpen)\n      element.setAttribute('aria-expanded', isOpen)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    const _config = {}\n    if (typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    return this.each(function () {\n      const data = Collapse.getOrCreateInstance(this, _config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  for (const element of SelectorEngine.getMultipleElementsFromSelector(this)) {\n    Collapse.getOrCreateInstance(element, { toggle: false }).toggle()\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Collapse)\n\nexport default Collapse\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport {\n  defineJQueryPlugin,\n  execute,\n  getElement,\n  getNextActiveElement,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop\n} from './util/index.js'\nimport EventHandler from './dom/event-handler.js'\nimport Manipulator from './dom/manipulator.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport BaseComponent from './base-component.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_DROPUP_CENTER = 'dropup-center'\nconst CLASS_NAME_DROPDOWN_CENTER = 'dropdown-center'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]:not(.disabled):not(:disabled)'\nconst SELECTOR_DATA_TOGGLE_SHOWN = `${SELECTOR_DATA_TOGGLE}.${CLASS_NAME_SHOW}`\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR = '.navbar'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\nconst PLACEMENT_TOPCENTER = 'top'\nconst PLACEMENT_BOTTOMCENTER = 'bottom'\n\nconst Default = {\n  autoClose: true,\n  boundary: 'clippingParents',\n  display: 'dynamic',\n  offset: [0, 2],\n  popperConfig: null,\n  reference: 'toggle'\n}\n\nconst DefaultType = {\n  autoClose: '(boolean|string)',\n  boundary: '(string|element)',\n  display: 'string',\n  offset: '(array|string|function)',\n  popperConfig: '(null|object|function)',\n  reference: '(string|element|object)'\n}\n\n/**\n * Class definition\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._popper = null\n    this._parent = this._element.parentNode // dropdown wrapper\n    // todo: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.3/forms/input-group/\n    this._menu = SelectorEngine.next(this._element, SELECTOR_MENU)[0] ||\n      SelectorEngine.prev(this._element, SELECTOR_MENU)[0] ||\n      SelectorEngine.findOne(SELECTOR_MENU, this._parent)\n    this._inNavbar = this._detectNavbar()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    return this._isShown() ? this.hide() : this.show()\n  }\n\n  show() {\n    if (isDisabled(this._element) || this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._createPopper()\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement && !this._parent.closest(SELECTOR_NAVBAR_NAV)) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.add(CLASS_NAME_SHOW)\n    this._element.classList.add(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (isDisabled(this._element) || !this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    this._completeHide(relatedTarget)\n  }\n\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.remove(CLASS_NAME_SHOW)\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._element.setAttribute('aria-expanded', 'false')\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  _getConfig(config) {\n    config = super._getConfig(config)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _createPopper() {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n    }\n\n    let referenceElement = this._element\n\n    if (this._config.reference === 'parent') {\n      referenceElement = this._parent\n    } else if (isElement(this._config.reference)) {\n      referenceElement = getElement(this._config.reference)\n    } else if (typeof this._config.reference === 'object') {\n      referenceElement = this._config.reference\n    }\n\n    const popperConfig = this._getPopperConfig()\n    this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n  }\n\n  _isShown() {\n    return this._menu.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._parent\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP_CENTER)) {\n      return PLACEMENT_TOPCENTER\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPDOWN_CENTER)) {\n      return PLACEMENT_BOTTOMCENTER\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(SELECTOR_NAVBAR) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display or Dropdown is in Navbar\n    if (this._inNavbar || this._config.display === 'static') {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'static') // todo:v6 remove\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...execute(this._config.popperConfig, [defaultBsPopperConfig])\n    }\n  }\n\n  _selectMenuItem({ key, target }) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(element => isVisible(element))\n\n    if (!items.length) {\n      return\n    }\n\n    // if target isn't included in items (e.g. when expanding the dropdown)\n    // allow cycling to get the last item in case key equals ARROW_UP_KEY\n    getNextActiveElement(items, target, key === ARROW_DOWN_KEY, !items.includes(target)).focus()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Dropdown.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n\n  static clearMenus(event) {\n    if (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY)) {\n      return\n    }\n\n    const openToggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE_SHOWN)\n\n    for (const toggle of openToggles) {\n      const context = Dropdown.getInstance(toggle)\n      if (!context || context._config.autoClose === false) {\n        continue\n      }\n\n      const composedPath = event.composedPath()\n      const isMenuTarget = composedPath.includes(context._menu)\n      if (\n        composedPath.includes(context._element) ||\n        (context._config.autoClose === 'inside' && !isMenuTarget) ||\n        (context._config.autoClose === 'outside' && isMenuTarget)\n      ) {\n        continue\n      }\n\n      // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n      if (context._menu.contains(event.target) && ((event.type === 'keyup' && event.key === TAB_KEY) || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n        continue\n      }\n\n      const relatedTarget = { relatedTarget: context._element }\n\n      if (event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      context._completeHide(relatedTarget)\n    }\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not an UP | DOWN | ESCAPE key => not a dropdown command\n    // If input/textarea && if key is other than ESCAPE => not a dropdown command\n\n    const isInput = /input|textarea/i.test(event.target.tagName)\n    const isEscapeEvent = event.key === ESCAPE_KEY\n    const isUpOrDownEvent = [ARROW_UP_KEY, ARROW_DOWN_KEY].includes(event.key)\n\n    if (!isUpOrDownEvent && !isEscapeEvent) {\n      return\n    }\n\n    if (isInput && !isEscapeEvent) {\n      return\n    }\n\n    event.preventDefault()\n\n    // todo: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.3/forms/input-group/\n    const getToggleButton = this.matches(SELECTOR_DATA_TOGGLE) ?\n      this :\n      (SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0] ||\n        SelectorEngine.next(this, SELECTOR_DATA_TOGGLE)[0] ||\n        SelectorEngine.findOne(SELECTOR_DATA_TOGGLE, event.delegateTarget.parentNode))\n\n    const instance = Dropdown.getOrCreateInstance(getToggleButton)\n\n    if (isUpOrDownEvent) {\n      event.stopPropagation()\n      instance.show()\n      instance._selectMenuItem(event)\n      return\n    }\n\n    if (instance._isShown()) { // else is escape and we check if it is shown\n      event.stopPropagation()\n      instance.hide()\n      getToggleButton.focus()\n    }\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.getOrCreateInstance(this).toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Dropdown)\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine.js'\nimport Manipulator from '../dom/manipulator.js'\nimport { isElement } from './index.js'\n\n/**\n * Constants\n */\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\nconst PROPERTY_PADDING = 'padding-right'\nconst PROPERTY_MARGIN = 'margin-right'\n\n/**\n * Class definition\n */\n\nclass ScrollBarHelper {\n  constructor() {\n    this._element = document.body\n  }\n\n  // Public\n  getWidth() {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n    const documentWidth = document.documentElement.clientWidth\n    return Math.abs(window.innerWidth - documentWidth)\n  }\n\n  hide() {\n    const width = this.getWidth()\n    this._disableOverFlow()\n    // give padding to element to balance the hidden scrollbar width\n    this._setElementAttributes(this._element, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements to keep showing fullwidth\n    this._setElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    this._setElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN, calculatedValue => calculatedValue - width)\n  }\n\n  reset() {\n    this._resetElementAttributes(this._element, 'overflow')\n    this._resetElementAttributes(this._element, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN)\n  }\n\n  isOverflowing() {\n    return this.getWidth() > 0\n  }\n\n  // Private\n  _disableOverFlow() {\n    this._saveInitialAttribute(this._element, 'overflow')\n    this._element.style.overflow = 'hidden'\n  }\n\n  _setElementAttributes(selector, styleProperty, callback) {\n    const scrollbarWidth = this.getWidth()\n    const manipulationCallBack = element => {\n      if (element !== this._element && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      this._saveInitialAttribute(element, styleProperty)\n      const calculatedValue = window.getComputedStyle(element).getPropertyValue(styleProperty)\n      element.style.setProperty(styleProperty, `${callback(Number.parseFloat(calculatedValue))}px`)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _saveInitialAttribute(element, styleProperty) {\n    const actualValue = element.style.getPropertyValue(styleProperty)\n    if (actualValue) {\n      Manipulator.setDataAttribute(element, styleProperty, actualValue)\n    }\n  }\n\n  _resetElementAttributes(selector, styleProperty) {\n    const manipulationCallBack = element => {\n      const value = Manipulator.getDataAttribute(element, styleProperty)\n      // We only want to remove the property if the value is `null`; the value can also be zero\n      if (value === null) {\n        element.style.removeProperty(styleProperty)\n        return\n      }\n\n      Manipulator.removeDataAttribute(element, styleProperty)\n      element.style.setProperty(styleProperty, value)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _applyManipulationCallback(selector, callBack) {\n    if (isElement(selector)) {\n      callBack(selector)\n      return\n    }\n\n    for (const sel of SelectorEngine.find(selector, this._element)) {\n      callBack(sel)\n    }\n  }\n}\n\nexport default ScrollBarHelper\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport { execute, executeAfterTransition, getElement, reflow } from './index.js'\nimport Config from './config.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nconst Default = {\n  className: 'modal-backdrop',\n  clickCallback: null,\n  isAnimated: false,\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  rootElement: 'body' // give the choice to place backdrop under different elements\n}\n\nconst DefaultType = {\n  className: 'string',\n  clickCallback: '(function|null)',\n  isAnimated: 'boolean',\n  isVisible: 'boolean',\n  rootElement: '(element|string)'\n}\n\n/**\n * Class definition\n */\n\nclass Backdrop extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    const element = this._getElement()\n    if (this._config.isAnimated) {\n      reflow(element)\n    }\n\n    element.classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._element.remove()\n    this._isAppended = false\n  }\n\n  // Private\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = this._config.className\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _configAfterMerge(config) {\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    const element = this._getElement()\n    this._config.rootElement.append(element)\n\n    EventHandler.on(element, EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated)\n  }\n}\n\nexport default Backdrop\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): util/focustrap.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport SelectorEngine from '../dom/selector-engine.js'\nimport Config from './config.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'focustrap'\nconst DATA_KEY = 'bs.focustrap'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_KEYDOWN_TAB = `keydown.tab${EVENT_KEY}`\n\nconst TAB_KEY = 'Tab'\nconst TAB_NAV_FORWARD = 'forward'\nconst TAB_NAV_BACKWARD = 'backward'\n\nconst Default = {\n  autofocus: true,\n  trapElement: null // The element to trap focus inside of\n}\n\nconst DefaultType = {\n  autofocus: 'boolean',\n  trapElement: 'element'\n}\n\n/**\n * Class definition\n */\n\nclass FocusTrap extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isActive = false\n    this._lastTabNavDirection = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  activate() {\n    if (this._isActive) {\n      return\n    }\n\n    if (this._config.autofocus) {\n      this._config.trapElement.focus()\n    }\n\n    EventHandler.off(document, EVENT_KEY) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => this._handleFocusin(event))\n    EventHandler.on(document, EVENT_KEYDOWN_TAB, event => this._handleKeydown(event))\n\n    this._isActive = true\n  }\n\n  deactivate() {\n    if (!this._isActive) {\n      return\n    }\n\n    this._isActive = false\n    EventHandler.off(document, EVENT_KEY)\n  }\n\n  // Private\n  _handleFocusin(event) {\n    const { trapElement } = this._config\n\n    if (event.target === document || event.target === trapElement || trapElement.contains(event.target)) {\n      return\n    }\n\n    const elements = SelectorEngine.focusableChildren(trapElement)\n\n    if (elements.length === 0) {\n      trapElement.focus()\n    } else if (this._lastTabNavDirection === TAB_NAV_BACKWARD) {\n      elements[elements.length - 1].focus()\n    } else {\n      elements[0].focus()\n    }\n  }\n\n  _handleKeydown(event) {\n    if (event.key !== TAB_KEY) {\n      return\n    }\n\n    this._lastTabNavDirection = event.shiftKey ? TAB_NAV_BACKWARD : TAB_NAV_FORWARD\n  }\n}\n\nexport default FocusTrap\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, isRTL, isVisible, reflow } from './util/index.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport ScrollBarHelper from './util/scrollbar.js'\nimport BaseComponent from './base-component.js'\nimport Backdrop from './util/backdrop.js'\nimport FocusTrap from './util/focustrap.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst OPEN_SELECTOR = '.modal.show'\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\n\nconst Default = {\n  backdrop: true,\n  focus: true,\n  keyboard: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  focus: 'boolean',\n  keyboard: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._isShown = false\n    this._isTransitioning = false\n    this._scrollBar = new ScrollBarHelper()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._isTransitioning = true\n\n    this._scrollBar.hide()\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n\n    this._adjustDialog()\n\n    this._backdrop.show(() => this._showElement(relatedTarget))\n  }\n\n  hide() {\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    this._isTransitioning = true\n    this._focustrap.deactivate()\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    this._queueCallback(() => this._hideModal(), this._element, this._isAnimated())\n  }\n\n  dispose() {\n    for (const htmlElement of [window, this._dialog]) {\n      EventHandler.off(htmlElement, EVENT_KEY)\n    }\n\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: Boolean(this._config.backdrop), // 'static' option will be translated to true, and booleans will keep their value,\n      isAnimated: this._isAnimated()\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _showElement(relatedTarget) {\n    // try to append dynamic modal\n    if (!document.body.contains(this._element)) {\n      document.body.append(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._focustrap.activate()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    this._queueCallback(transitionComplete, this._dialog, this._isAnimated())\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (this._config.keyboard) {\n        event.preventDefault()\n        this.hide()\n        return\n      }\n\n      this._triggerBackdropTransition()\n    })\n\n    EventHandler.on(window, EVENT_RESIZE, () => {\n      if (this._isShown && !this._isTransitioning) {\n        this._adjustDialog()\n      }\n    })\n\n    EventHandler.on(this._element, EVENT_MOUSEDOWN_DISMISS, event => {\n      // a bad trick to segregate clicks that may start inside dialog but end outside, and avoid listen to scrollbar clicks\n      EventHandler.one(this._element, EVENT_CLICK_DISMISS, event2 => {\n        if (this._element !== event.target || this._element !== event2.target) {\n          return\n        }\n\n        if (this._config.backdrop === 'static') {\n          this._triggerBackdropTransition()\n          return\n        }\n\n        if (this._config.backdrop) {\n          this.hide()\n        }\n      })\n    })\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n\n    this._backdrop.hide(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._scrollBar.reset()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const initialOverflowY = this._element.style.overflowY\n    // return if the following background transition hasn't yet completed\n    if (initialOverflowY === 'hidden' || this._element.classList.contains(CLASS_NAME_STATIC)) {\n      return\n    }\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n    this._queueCallback(() => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      this._queueCallback(() => {\n        this._element.style.overflowY = initialOverflowY\n      }, this._dialog)\n    }, this._dialog)\n\n    this._element.focus()\n  }\n\n  /**\n   * The following methods are used to handle overflowing modals\n   */\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const scrollbarWidth = this._scrollBar.getWidth()\n    const isBodyOverflowing = scrollbarWidth > 0\n\n    if (isBodyOverflowing && !isModalOverflowing) {\n      const property = isRTL() ? 'paddingLeft' : 'paddingRight'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n\n    if (!isBodyOverflowing && isModalOverflowing) {\n      const property = isRTL() ? 'paddingRight' : 'paddingLeft'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  // Static\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      const data = Modal.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](relatedTarget)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  // avoid conflict when clicking modal toggler while another one is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen) {\n    Modal.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Modal.getOrCreateInstance(target)\n\n  data.toggle(this)\n})\n\nenableDismissTrigger(Modal)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Modal)\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  isDisabled,\n  isVisible\n} from './util/index.js'\nimport ScrollBarHelper from './util/scrollbar.js'\nimport EventHandler from './dom/event-handler.js'\nimport BaseComponent from './base-component.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport Backdrop from './util/backdrop.js'\nimport FocusTrap from './util/focustrap.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\nconst CLASS_NAME_HIDING = 'hiding'\nconst CLASS_NAME_BACKDROP = 'offcanvas-backdrop'\nconst OPEN_SELECTOR = '.offcanvas.show'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isShown = false\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._backdrop.show()\n\n    if (!this._config.scroll) {\n      new ScrollBarHelper().hide()\n    }\n\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOWING)\n\n    const completeCallBack = () => {\n      if (!this._config.scroll || this._config.backdrop) {\n        this._focustrap.activate()\n      }\n\n      this._element.classList.add(CLASS_NAME_SHOW)\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n    }\n\n    this._queueCallback(completeCallBack, this._element, true)\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._focustrap.deactivate()\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.add(CLASS_NAME_HIDING)\n    this._backdrop.hide()\n\n    const completeCallback = () => {\n      this._element.classList.remove(CLASS_NAME_SHOW, CLASS_NAME_HIDING)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n\n      if (!this._config.scroll) {\n        new ScrollBarHelper().reset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._queueCallback(completeCallback, this._element, true)\n  }\n\n  dispose() {\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    const clickCallback = () => {\n      if (this._config.backdrop === 'static') {\n        EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n        return\n      }\n\n      this.hide()\n    }\n\n    // 'static' option will be translated to true, and booleans will keep their value\n    const isVisible = Boolean(this._config.backdrop)\n\n    return new Backdrop({\n      className: CLASS_NAME_BACKDROP,\n      isVisible,\n      isAnimated: true,\n      rootElement: this._element.parentNode,\n      clickCallback: isVisible ? clickCallback : null\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (!this._config.keyboard) {\n        EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n        return\n      }\n\n      this.hide()\n    })\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Offcanvas.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen && alreadyOpen !== target) {\n    Offcanvas.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Offcanvas.getOrCreateInstance(target)\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const selector of SelectorEngine.find(OPEN_SELECTOR)) {\n    Offcanvas.getOrCreateInstance(selector).show()\n  }\n})\n\nEventHandler.on(window, EVENT_RESIZE, () => {\n  for (const element of SelectorEngine.find('[aria-modal][class*=show][class*=offcanvas-]')) {\n    if (getComputedStyle(element).position !== 'fixed') {\n      Offcanvas.getOrCreateInstance(element).hide()\n    }\n  }\n})\n\nenableDismissTrigger(Offcanvas)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Offcanvas)\n\nexport default Offcanvas\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttributes = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shout-out to Angular https://github.com/angular/angular/blob/12.2.x/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file|sms):|[^#&/:?]*(?:[#/?]|$))/i\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shout-out to Angular https://github.com/angular/angular/blob/12.2.x/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attribute, allowedAttributeList) => {\n  const attributeName = attribute.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attributeName)) {\n    if (uriAttributes.has(attributeName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attribute.nodeValue) || DATA_URL_PATTERN.test(attribute.nodeValue))\n    }\n\n    return true\n  }\n\n  // Check if a regular expression validates the attribute.\n  return allowedAttributeList.filter(attributeRegex => attributeRegex instanceof RegExp)\n    .some(regex => regex.test(attributeName))\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFunction) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFunction && typeof sanitizeFunction === 'function') {\n    return sanitizeFunction(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (const element of elements) {\n    const elementName = element.nodeName.toLowerCase()\n\n    if (!Object.keys(allowList).includes(elementName)) {\n      element.remove()\n\n      continue\n    }\n\n    const attributeList = [].concat(...element.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elementName] || [])\n\n    for (const attribute of attributeList) {\n      if (!allowedAttribute(attribute, allowedAttributes)) {\n        element.removeAttribute(attribute.nodeName)\n      }\n    }\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): util/template-factory.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { DefaultAllowlist, sanitizeHtml } from './sanitizer.js'\nimport { execute, getElement, isElement } from './index.js'\nimport SelectorEngine from '../dom/selector-engine.js'\nimport Config from './config.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'TemplateFactory'\n\nconst Default = {\n  allowList: DefaultAllowlist,\n  content: {}, // { selector : text ,  selector2 : text2 , }\n  extraClass: '',\n  html: false,\n  sanitize: true,\n  sanitizeFn: null,\n  template: '<div></div>'\n}\n\nconst DefaultType = {\n  allowList: 'object',\n  content: 'object',\n  extraClass: '(string|function)',\n  html: 'boolean',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  template: 'string'\n}\n\nconst DefaultContentType = {\n  entry: '(string|element|function|null)',\n  selector: '(string|element)'\n}\n\n/**\n * Class definition\n */\n\nclass TemplateFactory extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  getContent() {\n    return Object.values(this._config.content)\n      .map(config => this._resolvePossibleFunction(config))\n      .filter(Boolean)\n  }\n\n  hasContent() {\n    return this.getContent().length > 0\n  }\n\n  changeContent(content) {\n    this._checkContent(content)\n    this._config.content = { ...this._config.content, ...content }\n    return this\n  }\n\n  toHtml() {\n    const templateWrapper = document.createElement('div')\n    templateWrapper.innerHTML = this._maybeSanitize(this._config.template)\n\n    for (const [selector, text] of Object.entries(this._config.content)) {\n      this._setContent(templateWrapper, text, selector)\n    }\n\n    const template = templateWrapper.children[0]\n    const extraClass = this._resolvePossibleFunction(this._config.extraClass)\n\n    if (extraClass) {\n      template.classList.add(...extraClass.split(' '))\n    }\n\n    return template\n  }\n\n  // Private\n  _typeCheckConfig(config) {\n    super._typeCheckConfig(config)\n    this._checkContent(config.content)\n  }\n\n  _checkContent(arg) {\n    for (const [selector, content] of Object.entries(arg)) {\n      super._typeCheckConfig({ selector, entry: content }, DefaultContentType)\n    }\n  }\n\n  _setContent(template, content, selector) {\n    const templateElement = SelectorEngine.findOne(selector, template)\n\n    if (!templateElement) {\n      return\n    }\n\n    content = this._resolvePossibleFunction(content)\n\n    if (!content) {\n      templateElement.remove()\n      return\n    }\n\n    if (isElement(content)) {\n      this._putElementInTemplate(getElement(content), templateElement)\n      return\n    }\n\n    if (this._config.html) {\n      templateElement.innerHTML = this._maybeSanitize(content)\n      return\n    }\n\n    templateElement.textContent = content\n  }\n\n  _maybeSanitize(arg) {\n    return this._config.sanitize ? sanitizeHtml(arg, this._config.allowList, this._config.sanitizeFn) : arg\n  }\n\n  _resolvePossibleFunction(arg) {\n    return execute(arg, [this])\n  }\n\n  _putElementInTemplate(element, templateElement) {\n    if (this._config.html) {\n      templateElement.innerHTML = ''\n      templateElement.append(element)\n      return\n    }\n\n    templateElement.textContent = element.textContent\n  }\n}\n\nexport default TemplateFactory\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport { defineJQueryPlugin, execute, findShadowRoot, getElement, getUID, isRTL, noop } from './util/index.js'\nimport { DefaultAllowlist } from './util/sanitizer.js'\nimport EventHandler from './dom/event-handler.js'\nimport Manipulator from './dom/manipulator.js'\nimport BaseComponent from './base-component.js'\nimport TemplateFactory from './util/template-factory.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'tooltip'\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\nconst SELECTOR_MODAL = `.${CLASS_NAME_MODAL}`\n\nconst EVENT_MODAL_HIDE = 'hide.bs.modal'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\nconst EVENT_HIDE = 'hide'\nconst EVENT_HIDDEN = 'hidden'\nconst EVENT_SHOW = 'show'\nconst EVENT_SHOWN = 'shown'\nconst EVENT_INSERTED = 'inserted'\nconst EVENT_CLICK = 'click'\nconst EVENT_FOCUSIN = 'focusin'\nconst EVENT_FOCUSOUT = 'focusout'\nconst EVENT_MOUSEENTER = 'mouseenter'\nconst EVENT_MOUSELEAVE = 'mouseleave'\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  allowList: DefaultAllowlist,\n  animation: true,\n  boundary: 'clippingParents',\n  container: false,\n  customClass: '',\n  delay: 0,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  html: false,\n  offset: [0, 0],\n  placement: 'top',\n  popperConfig: null,\n  sanitize: true,\n  sanitizeFn: null,\n  selector: false,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n            '<div class=\"tooltip-arrow\"></div>' +\n            '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  title: '',\n  trigger: 'hover focus'\n}\n\nconst DefaultType = {\n  allowList: 'object',\n  animation: 'boolean',\n  boundary: '(string|element)',\n  container: '(string|element|boolean)',\n  customClass: '(string|function)',\n  delay: '(number|object)',\n  fallbackPlacements: 'array',\n  html: 'boolean',\n  offset: '(array|string|function)',\n  placement: '(string|function)',\n  popperConfig: '(null|object|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  selector: '(string|boolean)',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string'\n}\n\n/**\n * Class definition\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element, config)\n\n    // Private\n    this._isEnabled = true\n    this._timeout = 0\n    this._isHovered = null\n    this._activeTrigger = {}\n    this._popper = null\n    this._templateFactory = null\n    this._newContent = null\n\n    // Protected\n    this.tip = null\n\n    this._setListeners()\n\n    if (!this._config.selector) {\n      this._fixTitle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle() {\n    if (!this._isEnabled) {\n      return\n    }\n\n    this._activeTrigger.click = !this._activeTrigger.click\n    if (this._isShown()) {\n      this._leave()\n      return\n    }\n\n    this._enter()\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n\n    if (this._element.getAttribute('data-bs-original-title')) {\n      this._element.setAttribute('title', this._element.getAttribute('data-bs-original-title'))\n    }\n\n    this._disposePopper()\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this._isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOW))\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = (shadowRoot || this._element.ownerDocument.documentElement).contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    // todo v6 remove this OR make it optional\n    this._disposePopper()\n\n    const tip = this._getTipElement()\n\n    this._element.setAttribute('aria-describedby', tip.getAttribute('id'))\n\n    const { container } = this._config\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.append(tip)\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_INSERTED))\n    }\n\n    this._popper = this._createPopper(tip)\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    const complete = () => {\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOWN))\n\n      if (this._isHovered === false) {\n        this._leave()\n      }\n\n      this._isHovered = false\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  hide() {\n    if (!this._isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDE))\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const tip = this._getTipElement()\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n    this._isHovered = null // it is a trick to support manual triggering\n\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (!this._isHovered) {\n        this._disposePopper()\n      }\n\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDDEN))\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  update() {\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n  _isWithContent() {\n    return Boolean(this._getTitle())\n  }\n\n  _getTipElement() {\n    if (!this.tip) {\n      this.tip = this._createTipElement(this._newContent || this._getContentForTemplate())\n    }\n\n    return this.tip\n  }\n\n  _createTipElement(content) {\n    const tip = this._getTemplateFactory(content).toHtml()\n\n    // todo: remove this check on v6\n    if (!tip) {\n      return null\n    }\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n    // todo: on v6 the following can be achieved with CSS only\n    tip.classList.add(`bs-${this.constructor.NAME}-auto`)\n\n    const tipId = getUID(this.constructor.NAME).toString()\n\n    tip.setAttribute('id', tipId)\n\n    if (this._isAnimated()) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    return tip\n  }\n\n  setContent(content) {\n    this._newContent = content\n    if (this._isShown()) {\n      this._disposePopper()\n      this.show()\n    }\n  }\n\n  _getTemplateFactory(content) {\n    if (this._templateFactory) {\n      this._templateFactory.changeContent(content)\n    } else {\n      this._templateFactory = new TemplateFactory({\n        ...this._config,\n        // the `content` var has to be after `this._config`\n        // to override config.content in case of popover\n        content,\n        extraClass: this._resolvePossibleFunction(this._config.customClass)\n      })\n    }\n\n    return this._templateFactory\n  }\n\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TOOLTIP_INNER]: this._getTitle()\n    }\n  }\n\n  _getTitle() {\n    return this._resolvePossibleFunction(this._config.title) || this._element.getAttribute('data-bs-original-title')\n  }\n\n  // Private\n  _initializeOnDelegatedTarget(event) {\n    return this.constructor.getOrCreateInstance(event.delegateTarget, this._getDelegateConfig())\n  }\n\n  _isAnimated() {\n    return this._config.animation || (this.tip && this.tip.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _isShown() {\n    return this.tip && this.tip.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _createPopper(tip) {\n    const placement = execute(this._config.placement, [this, tip, this._element])\n    const attachment = AttachmentMap[placement.toUpperCase()]\n    return Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _resolvePossibleFunction(arg) {\n    return execute(arg, [this._element])\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            fallbackPlacements: this._config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this._config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'preSetPlacement',\n          enabled: true,\n          phase: 'beforeMain',\n          fn: data => {\n            // Pre-set Popper's placement attribute in order to read the arrow sizes properly.\n            // Otherwise, Popper mixes up the width and height dimensions since the initial arrow style is for top placement\n            this._getTipElement().setAttribute('data-popper-placement', data.state.placement)\n          }\n        }\n      ]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...execute(this._config.popperConfig, [defaultBsPopperConfig])\n    }\n  }\n\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ')\n\n    for (const trigger of triggers) {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.eventName(EVENT_CLICK), this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context.toggle()\n        })\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSEENTER) :\n          this.constructor.eventName(EVENT_FOCUSIN)\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSELEAVE) :\n          this.constructor.eventName(EVENT_FOCUSOUT)\n\n        EventHandler.on(this._element, eventIn, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER] = true\n          context._enter()\n        })\n        EventHandler.on(this._element, eventOut, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER] =\n            context._element.contains(event.relatedTarget)\n\n          context._leave()\n        })\n      }\n    }\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n\n    if (!title) {\n      return\n    }\n\n    if (!this._element.getAttribute('aria-label') && !this._element.textContent.trim()) {\n      this._element.setAttribute('aria-label', title)\n    }\n\n    this._element.setAttribute('data-bs-original-title', title) // DO NOT USE IT. Is only for backwards compatibility\n    this._element.removeAttribute('title')\n  }\n\n  _enter() {\n    if (this._isShown() || this._isHovered) {\n      this._isHovered = true\n      return\n    }\n\n    this._isHovered = true\n\n    this._setTimeout(() => {\n      if (this._isHovered) {\n        this.show()\n      }\n    }, this._config.delay.show)\n  }\n\n  _leave() {\n    if (this._isWithActiveTrigger()) {\n      return\n    }\n\n    this._isHovered = false\n\n    this._setTimeout(() => {\n      if (!this._isHovered) {\n        this.hide()\n      }\n    }, this._config.delay.hide)\n  }\n\n  _setTimeout(handler, timeout) {\n    clearTimeout(this._timeout)\n    this._timeout = setTimeout(handler, timeout)\n  }\n\n  _isWithActiveTrigger() {\n    return Object.values(this._activeTrigger).includes(true)\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    for (const dataAttribute of Object.keys(dataAttributes)) {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttribute)) {\n        delete dataAttributes[dataAttribute]\n      }\n    }\n\n    config = {\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    config.container = config.container === false ? document.body : getElement(config.container)\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    for (const [key, value] of Object.entries(this._config)) {\n      if (this.constructor.Default[key] !== value) {\n        config[key] = value\n      }\n    }\n\n    config.selector = false\n    config.trigger = 'manual'\n\n    // In the future can be replaced with:\n    // const keysWithDifferentValues = Object.entries(this._config).filter(entry => this.constructor.Default[entry[0]] !== this._config[entry[0]])\n    // `Object.fromEntries(keysWithDifferentValues)`\n    return config\n  }\n\n  _disposePopper() {\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n\n    if (this.tip) {\n      this.tip.remove()\n      this.tip = null\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tooltip.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tooltip)\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index.js'\nimport Tooltip from './tooltip.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'popover'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\nconst Default = {\n  ...Tooltip.Default,\n  content: '',\n  offset: [0, 8],\n  placement: 'right',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n    '<div class=\"popover-arrow\"></div>' +\n    '<h3 class=\"popover-header\"></h3>' +\n    '<div class=\"popover-body\"></div>' +\n    '</div>',\n  trigger: 'click'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(null|string|element|function)'\n}\n\n/**\n * Class definition\n */\n\nclass Popover extends Tooltip {\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Overrides\n  _isWithContent() {\n    return this._getTitle() || this._getContent()\n  }\n\n  // Private\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TITLE]: this._getTitle(),\n      [SELECTOR_CONTENT]: this._getContent()\n    }\n  }\n\n  _getContent() {\n    return this._resolvePossibleFunction(this._config.content)\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Popover.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Popover)\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, getElement, isDisabled, isVisible } from './util/index.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport BaseComponent from './base-component.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_TARGET_LINKS = '[href]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_LINK_ITEMS = `${SELECTOR_NAV_LINKS}, ${SELECTOR_NAV_ITEMS} > ${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst Default = {\n  offset: null, // TODO: v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: '0px 0px -25%',\n  smoothScroll: false,\n  target: null,\n  threshold: [0.1, 0.5, 1]\n}\n\nconst DefaultType = {\n  offset: '(number|null)', // TODO v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: 'string',\n  smoothScroll: 'boolean',\n  target: 'element',\n  threshold: 'array'\n}\n\n/**\n * Class definition\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    // this._element is the observablesContainer and config.target the menu links wrapper\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n    this._rootElement = getComputedStyle(this._element).overflowY === 'visible' ? null : this._element\n    this._activeTarget = null\n    this._observer = null\n    this._previousScrollData = {\n      visibleEntryTop: 0,\n      parentScrollTop: 0\n    }\n    this.refresh() // initialize\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  refresh() {\n    this._initializeTargetsAndObservables()\n    this._maybeEnableSmoothScroll()\n\n    if (this._observer) {\n      this._observer.disconnect()\n    } else {\n      this._observer = this._getNewObserver()\n    }\n\n    for (const section of this._observableSections.values()) {\n      this._observer.observe(section)\n    }\n  }\n\n  dispose() {\n    this._observer.disconnect()\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    // TODO: on v6 target should be given explicitly & remove the {target: 'ss-target'} case\n    config.target = getElement(config.target) || document.body\n\n    // TODO: v6 Only for backwards compatibility reasons. Use rootMargin only\n    config.rootMargin = config.offset ? `${config.offset}px 0px -30%` : config.rootMargin\n\n    if (typeof config.threshold === 'string') {\n      config.threshold = config.threshold.split(',').map(value => Number.parseFloat(value))\n    }\n\n    return config\n  }\n\n  _maybeEnableSmoothScroll() {\n    if (!this._config.smoothScroll) {\n      return\n    }\n\n    // unregister any previous listeners\n    EventHandler.off(this._config.target, EVENT_CLICK)\n\n    EventHandler.on(this._config.target, EVENT_CLICK, SELECTOR_TARGET_LINKS, event => {\n      const observableSection = this._observableSections.get(event.target.hash)\n      if (observableSection) {\n        event.preventDefault()\n        const root = this._rootElement || window\n        const height = observableSection.offsetTop - this._element.offsetTop\n        if (root.scrollTo) {\n          root.scrollTo({ top: height, behavior: 'smooth' })\n          return\n        }\n\n        // Chrome 60 doesn't support `scrollTo`\n        root.scrollTop = height\n      }\n    })\n  }\n\n  _getNewObserver() {\n    const options = {\n      root: this._rootElement,\n      threshold: this._config.threshold,\n      rootMargin: this._config.rootMargin\n    }\n\n    return new IntersectionObserver(entries => this._observerCallback(entries), options)\n  }\n\n  // The logic of selection\n  _observerCallback(entries) {\n    const targetElement = entry => this._targetLinks.get(`#${entry.target.id}`)\n    const activate = entry => {\n      this._previousScrollData.visibleEntryTop = entry.target.offsetTop\n      this._process(targetElement(entry))\n    }\n\n    const parentScrollTop = (this._rootElement || document.documentElement).scrollTop\n    const userScrollsDown = parentScrollTop >= this._previousScrollData.parentScrollTop\n    this._previousScrollData.parentScrollTop = parentScrollTop\n\n    for (const entry of entries) {\n      if (!entry.isIntersecting) {\n        this._activeTarget = null\n        this._clearActiveClass(targetElement(entry))\n\n        continue\n      }\n\n      const entryIsLowerThanPrevious = entry.target.offsetTop >= this._previousScrollData.visibleEntryTop\n      // if we are scrolling down, pick the bigger offsetTop\n      if (userScrollsDown && entryIsLowerThanPrevious) {\n        activate(entry)\n        // if parent isn't scrolled, let's keep the first visible item, breaking the iteration\n        if (!parentScrollTop) {\n          return\n        }\n\n        continue\n      }\n\n      // if we are scrolling up, pick the smallest offsetTop\n      if (!userScrollsDown && !entryIsLowerThanPrevious) {\n        activate(entry)\n      }\n    }\n  }\n\n  _initializeTargetsAndObservables() {\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n\n    const targetLinks = SelectorEngine.find(SELECTOR_TARGET_LINKS, this._config.target)\n\n    for (const anchor of targetLinks) {\n      // ensure that the anchor has an id and is not disabled\n      if (!anchor.hash || isDisabled(anchor)) {\n        continue\n      }\n\n      const observableSection = SelectorEngine.findOne(anchor.hash, this._element)\n\n      // ensure that the observableSection exists & is visible\n      if (isVisible(observableSection)) {\n        this._targetLinks.set(anchor.hash, anchor)\n        this._observableSections.set(anchor.hash, observableSection)\n      }\n    }\n  }\n\n  _process(target) {\n    if (this._activeTarget === target) {\n      return\n    }\n\n    this._clearActiveClass(this._config.target)\n    this._activeTarget = target\n    target.classList.add(CLASS_NAME_ACTIVE)\n    this._activateParents(target)\n\n    EventHandler.trigger(this._element, EVENT_ACTIVATE, { relatedTarget: target })\n  }\n\n  _activateParents(target) {\n    // Activate dropdown parents\n    if (target.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, target.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n      return\n    }\n\n    for (const listGroup of SelectorEngine.parents(target, SELECTOR_NAV_LIST_GROUP)) {\n      // Set triggered links parents as active\n      // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n      for (const item of SelectorEngine.prev(listGroup, SELECTOR_LINK_ITEMS)) {\n        item.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _clearActiveClass(parent) {\n    parent.classList.remove(CLASS_NAME_ACTIVE)\n\n    const activeNodes = SelectorEngine.find(`${SELECTOR_TARGET_LINKS}.${CLASS_NAME_ACTIVE}`, parent)\n    for (const node of activeNodes) {\n      node.classList.remove(CLASS_NAME_ACTIVE)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = ScrollSpy.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const spy of SelectorEngine.find(SELECTOR_DATA_SPY)) {\n    ScrollSpy.getOrCreateInstance(spy)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(ScrollSpy)\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, getNextActiveElement, isDisabled } from './util/index.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport BaseComponent from './base-component.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}`\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_DROPDOWN = 'dropdown'\n\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_MENU = '.dropdown-menu'\nconst NOT_SELECTOR_DROPDOWN_TOGGLE = ':not(.dropdown-toggle)'\n\nconst SELECTOR_TAB_PANEL = '.list-group, .nav, [role=\"tablist\"]'\nconst SELECTOR_OUTER = '.nav-item, .list-group-item'\nconst SELECTOR_INNER = `.nav-link${NOT_SELECTOR_DROPDOWN_TOGGLE}, .list-group-item${NOT_SELECTOR_DROPDOWN_TOGGLE}, [role=\"tab\"]${NOT_SELECTOR_DROPDOWN_TOGGLE}`\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]' // todo:v6: could be only `tab`\nconst SELECTOR_INNER_ELEM = `${SELECTOR_INNER}, ${SELECTOR_DATA_TOGGLE}`\n\nconst SELECTOR_DATA_TOGGLE_ACTIVE = `.${CLASS_NAME_ACTIVE}[data-bs-toggle=\"tab\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"pill\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"list\"]`\n\n/**\n * Class definition\n */\n\nclass Tab extends BaseComponent {\n  constructor(element) {\n    super(element)\n    this._parent = this._element.closest(SELECTOR_TAB_PANEL)\n\n    if (!this._parent) {\n      return\n      // todo: should Throw exception on v6\n      // throw new TypeError(`${element.outerHTML} has not a valid parent ${SELECTOR_INNER_ELEM}`)\n    }\n\n    // Set up initial aria attributes\n    this._setInitialAttributes(this._parent, this._getChildren())\n\n    EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n  }\n\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() { // Shows this elem and deactivate the active sibling if exists\n    const innerElem = this._element\n    if (this._elemIsActive(innerElem)) {\n      return\n    }\n\n    // Search for active tab on same parent to deactivate it\n    const active = this._getActiveElem()\n\n    const hideEvent = active ?\n      EventHandler.trigger(active, EVENT_HIDE, { relatedTarget: innerElem }) :\n      null\n\n    const showEvent = EventHandler.trigger(innerElem, EVENT_SHOW, { relatedTarget: active })\n\n    if (showEvent.defaultPrevented || (hideEvent && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._deactivate(active, innerElem)\n    this._activate(innerElem, active)\n  }\n\n  // Private\n  _activate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n\n    this._activate(SelectorEngine.getElementFromSelector(element)) // Search and activate/show the proper section\n\n    const complete = () => {\n      if (element.getAttribute('role') !== 'tab') {\n        element.classList.add(CLASS_NAME_SHOW)\n        return\n      }\n\n      element.removeAttribute('tabindex')\n      element.setAttribute('aria-selected', true)\n      this._toggleDropDown(element, true)\n      EventHandler.trigger(element, EVENT_SHOWN, {\n        relatedTarget: relatedElem\n      })\n    }\n\n    this._queueCallback(complete, element, element.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _deactivate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.remove(CLASS_NAME_ACTIVE)\n    element.blur()\n\n    this._deactivate(SelectorEngine.getElementFromSelector(element)) // Search and deactivate the shown section too\n\n    const complete = () => {\n      if (element.getAttribute('role') !== 'tab') {\n        element.classList.remove(CLASS_NAME_SHOW)\n        return\n      }\n\n      element.setAttribute('aria-selected', false)\n      element.setAttribute('tabindex', '-1')\n      this._toggleDropDown(element, false)\n      EventHandler.trigger(element, EVENT_HIDDEN, { relatedTarget: relatedElem })\n    }\n\n    this._queueCallback(complete, element, element.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _keydown(event) {\n    if (!([ARROW_LEFT_KEY, ARROW_RIGHT_KEY, ARROW_UP_KEY, ARROW_DOWN_KEY].includes(event.key))) {\n      return\n    }\n\n    event.stopPropagation()// stopPropagation/preventDefault both added to support up/down keys without scrolling the page\n    event.preventDefault()\n    const isNext = [ARROW_RIGHT_KEY, ARROW_DOWN_KEY].includes(event.key)\n    const nextActiveElement = getNextActiveElement(this._getChildren().filter(element => !isDisabled(element)), event.target, isNext, true)\n\n    if (nextActiveElement) {\n      nextActiveElement.focus({ preventScroll: true })\n      Tab.getOrCreateInstance(nextActiveElement).show()\n    }\n  }\n\n  _getChildren() { // collection of inner elements\n    return SelectorEngine.find(SELECTOR_INNER_ELEM, this._parent)\n  }\n\n  _getActiveElem() {\n    return this._getChildren().find(child => this._elemIsActive(child)) || null\n  }\n\n  _setInitialAttributes(parent, children) {\n    this._setAttributeIfNotExists(parent, 'role', 'tablist')\n\n    for (const child of children) {\n      this._setInitialAttributesOnChild(child)\n    }\n  }\n\n  _setInitialAttributesOnChild(child) {\n    child = this._getInnerElement(child)\n    const isActive = this._elemIsActive(child)\n    const outerElem = this._getOuterElement(child)\n    child.setAttribute('aria-selected', isActive)\n\n    if (outerElem !== child) {\n      this._setAttributeIfNotExists(outerElem, 'role', 'presentation')\n    }\n\n    if (!isActive) {\n      child.setAttribute('tabindex', '-1')\n    }\n\n    this._setAttributeIfNotExists(child, 'role', 'tab')\n\n    // set attributes to the related panel too\n    this._setInitialAttributesOnTargetPanel(child)\n  }\n\n  _setInitialAttributesOnTargetPanel(child) {\n    const target = SelectorEngine.getElementFromSelector(child)\n\n    if (!target) {\n      return\n    }\n\n    this._setAttributeIfNotExists(target, 'role', 'tabpanel')\n\n    if (child.id) {\n      this._setAttributeIfNotExists(target, 'aria-labelledby', `#${child.id}`)\n    }\n  }\n\n  _toggleDropDown(element, open) {\n    const outerElem = this._getOuterElement(element)\n    if (!outerElem.classList.contains(CLASS_DROPDOWN)) {\n      return\n    }\n\n    const toggle = (selector, className) => {\n      const element = SelectorEngine.findOne(selector, outerElem)\n      if (element) {\n        element.classList.toggle(className, open)\n      }\n    }\n\n    toggle(SELECTOR_DROPDOWN_TOGGLE, CLASS_NAME_ACTIVE)\n    toggle(SELECTOR_DROPDOWN_MENU, CLASS_NAME_SHOW)\n    outerElem.setAttribute('aria-expanded', open)\n  }\n\n  _setAttributeIfNotExists(element, attribute, value) {\n    if (!element.hasAttribute(attribute)) {\n      element.setAttribute(attribute, value)\n    }\n  }\n\n  _elemIsActive(elem) {\n    return elem.classList.contains(CLASS_NAME_ACTIVE)\n  }\n\n  // Try to get the inner element (usually the .nav-link)\n  _getInnerElement(elem) {\n    return elem.matches(SELECTOR_INNER_ELEM) ? elem : SelectorEngine.findOne(SELECTOR_INNER_ELEM, elem)\n  }\n\n  // Try to get the outer element (usually the .nav-item)\n  _getOuterElement(elem) {\n    return elem.closest(SELECTOR_OUTER) || elem\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tab.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  Tab.getOrCreateInstance(this).show()\n})\n\n/**\n * Initialize on focus\n */\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const element of SelectorEngine.find(SELECTOR_DATA_TOGGLE_ACTIVE)) {\n    Tab.getOrCreateInstance(element)\n  }\n})\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tab)\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, reflow } from './util/index.js'\nimport EventHandler from './dom/event-handler.js'\nimport BaseComponent from './base-component.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide' // @deprecated - kept here only for backwards compatibility\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\n/**\n * Class definition\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._timeout = null\n    this._hasMouseInteraction = false\n    this._hasKeyboardInteraction = false\n    this._setListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      this._maybeScheduleHide()\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE) // @deprecated\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOW, CLASS_NAME_SHOWING)\n\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  hide() {\n    if (!this.isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE) // @deprecated\n      this._element.classList.remove(CLASS_NAME_SHOWING, CLASS_NAME_SHOW)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this.isShown()) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    super.dispose()\n  }\n\n  isShown() {\n    return this._element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return\n    }\n\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return\n    }\n\n    this._timeout = setTimeout(() => {\n      this.hide()\n    }, this._config.delay)\n  }\n\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout': {\n        this._hasMouseInteraction = isInteracting\n        break\n      }\n\n      case 'focusin':\n      case 'focusout': {\n        this._hasKeyboardInteraction = isInteracting\n        break\n      }\n\n      default: {\n        break\n      }\n    }\n\n    if (isInteracting) {\n      this._clearTimeout()\n      return\n    }\n\n    const nextElement = event.relatedTarget\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return\n    }\n\n    this._maybeScheduleHide()\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false))\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false))\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Toast.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Toast)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Toast)\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Alert from './src/alert.js'\nimport Button from './src/button.js'\nimport Carousel from './src/carousel.js'\nimport Collapse from './src/collapse.js'\nimport Dropdown from './src/dropdown.js'\nimport Modal from './src/modal.js'\nimport Offcanvas from './src/offcanvas.js'\nimport Popover from './src/popover.js'\nimport ScrollSpy from './src/scrollspy.js'\nimport Tab from './src/tab.js'\nimport Toast from './src/toast.js'\nimport Tooltip from './src/tooltip.js'\n\nexport default {\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Offcanvas,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"], "mappings": ";;;;;ujBASMA,EAAiB,gBAOjBC,EAAgBC,IAChBA,GAAYC,OAAOC,KAAOD,OAAOC,IAAIC,SAEvCH,EAAWA,EAASI,QAAQ,iBAAiB,CAACC,EAAOC,IAAQ,IAAGJ,IAAIC,OAAOG,QAGtEN,GA+CHO,EAAuBC,IAC3BA,EAAQC,cAAc,IAAIC,MAAMZ,GAAgB,EAG5Ca,EAAYC,MACXA,GAA4B,iBAAXA,UAIO,IAAlBA,EAAOC,SAChBD,EAASA,EAAO,SAGgB,IAApBA,EAAOE,UAGjBC,EAAaH,GAEbD,EAAUC,GACLA,EAAOC,OAASD,EAAO,GAAKA,EAGf,iBAAXA,GAAuBA,EAAOI,OAAS,EACzCC,SAASC,cAAcnB,EAAca,IAGvC,KAGHO,EAAYX,IAChB,IAAKG,EAAUH,IAAgD,IAApCA,EAAQY,iBAAiBJ,OAClD,OAAO,EAGT,MAAMK,EAAgF,YAA7DC,iBAAiBd,GAASe,iBAAiB,cAE9DC,EAAgBhB,EAAQiB,QAAQ,uBAEtC,IAAKD,EACH,OAAOH,EAGT,GAAIG,IAAkBhB,EAAS,CAC7B,MAAMkB,EAAUlB,EAAQiB,QAAQ,WAChC,GAAIC,GAAWA,EAAQC,aAAeH,EACpC,OAAO,EAGT,GAAgB,OAAZE,EACF,OAAO,CAEX,CAEA,OAAOL,CAAgB,EAGnBO,EAAapB,IACZA,GAAWA,EAAQM,WAAae,KAAKC,gBAItCtB,EAAQuB,UAAUC,SAAS,mBAIC,IAArBxB,EAAQyB,SACVzB,EAAQyB,SAGVzB,EAAQ0B,aAAa,aAAoD,UAArC1B,EAAQ2B,aAAa,aAG5DC,EAAiB5B,IACrB,IAAKS,SAASoB,gBAAgBC,aAC5B,OAAO,KAIT,GAAmC,mBAAxB9B,EAAQ+B,YAA4B,CAC7C,MAAMC,EAAOhC,EAAQ+B,cACrB,OAAOC,aAAgBC,WAAaD,EAAO,IAC7C,CAEA,OAAIhC,aAAmBiC,WACdjC,EAIJA,EAAQmB,WAINS,EAAe5B,EAAQmB,YAHrB,IAGgC,EAGrCe,EAAO,OAUPC,EAASnC,IACbA,EAAQoC,YAAY,EAGhBC,EAAY,IACZ5C,OAAO6C,SAAW7B,SAAS8B,KAAKb,aAAa,qBACxCjC,OAAO6C,OAGT,KAGHE,EAA4B,GAmB5BC,EAAQ,IAAuC,QAAjChC,SAASoB,gBAAgBa,IAEvCC,EAAqBC,IAnBAC,QAoBN,KACjB,MAAMC,EAAIT,IAEV,GAAIS,EAAG,CACL,MAAMC,EAAOH,EAAOI,KACdC,EAAqBH,EAAEI,GAAGH,GAChCD,EAAEI,GAAGH,GAAQH,EAAOO,gBACpBL,EAAEI,GAAGH,GAAMK,YAAcR,EACzBE,EAAEI,GAAGH,GAAMM,WAAa,KACtBP,EAAEI,GAAGH,GAAQE,EACNL,EAAOO,gBAElB,GA/B0B,YAAxB1C,SAAS6C,YAENd,EAA0BhC,QAC7BC,SAAS8C,iBAAiB,oBAAoB,KAC5C,IAAK,MAAMV,KAAYL,EACrBK,GACF,IAIJL,EAA0BgB,KAAKX,IAE/BA,GAoBA,EAGEY,EAAU,CAACC,EAAkBC,EAAO,GAAIC,EAAeF,IACxB,mBAArBA,EAAkCA,KAAoBC,GAAQC,EAGxEC,EAAyB,CAAChB,EAAUiB,EAAmBC,GAAoB,KAC/E,IAAKA,EAEH,YADAN,EAAQZ,GAIV,MACMmB,EA7LiChE,KACvC,IAAKA,EACH,OAAO,EAIT,IAAIiE,mBAAEA,EAAkBC,gBAAEA,GAAoBzE,OAAOqB,iBAAiBd,GAEtE,MAAMmE,EAA0BC,OAAOC,WAAWJ,GAC5CK,EAAuBF,OAAOC,WAAWH,GAG/C,OAAKC,GAA4BG,GAKjCL,EAAqBA,EAAmBM,MAAM,KAAK,GACnDL,EAAkBA,EAAgBK,MAAM,KAAK,GAxDf,KA0DtBH,OAAOC,WAAWJ,GAAsBG,OAAOC,WAAWH,KAPzD,CAOoG,EAyKpFM,CAAiCV,GADlC,EAGxB,IAAIW,GAAS,EAEb,MAAMC,EAAU,EAAGC,aACbA,IAAWb,IAIfW,GAAS,EACTX,EAAkBc,oBAAoBtF,EAAgBoF,GACtDjB,EAAQZ,GAAS,EAGnBiB,EAAkBP,iBAAiBjE,EAAgBoF,GACnDG,YAAW,KACJJ,GACH1E,EAAqB+D,EACvB,GACCE,EAAiB,EAYhBc,EAAuB,CAACC,EAAMC,EAAeC,EAAeC,KAChE,MAAMC,EAAaJ,EAAKvE,OACxB,IAAI4E,EAAQL,EAAKM,QAAQL,GAIzB,OAAe,IAAXI,GACMH,GAAiBC,EAAiBH,EAAKI,EAAa,GAAKJ,EAAK,IAGxEK,GAASH,EAAgB,GAAK,EAE1BC,IACFE,GAASA,EAAQD,GAAcA,GAG1BJ,EAAKO,KAAKC,IAAI,EAAGD,KAAKE,IAAIJ,EAAOD,EAAa,KAAI,EC7QrDM,EAAiB,qBACjBC,EAAiB,OACjBC,EAAgB,SAChBC,EAAgB,GACtB,IAAIC,EAAW,EACf,MAAMC,EAAe,CACnBC,WAAY,YACZC,WAAY,YAGRC,EAAe,IAAIC,IAAI,CAC3B,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,WAOF,SAASC,EAAanG,EAASoG,GAC7B,OAAQA,GAAQ,GAAEA,MAAQP,OAAiB7F,EAAQ6F,UAAYA,GACjE,CAEA,SAASQ,EAAiBrG,GACxB,MAAMoG,EAAMD,EAAanG,GAKzB,OAHAA,EAAQ6F,SAAWO,EACnBR,EAAcQ,GAAOR,EAAcQ,IAAQ,GAEpCR,EAAcQ,EACvB,CAoCA,SAASE,EAAYC,EAAQC,EAAUC,EAAqB,MAC1D,OAAOC,OAAOC,OAAOJ,GAClBK,MAAKC,GAASA,EAAML,WAAaA,GAAYK,EAAMJ,qBAAuBA,GAC/E,CAEA,SAASK,EAAoBC,EAAmBrC,EAASsC,GACvD,MAAMC,EAAiC,iBAAZvC,EAErB8B,EAAWS,EAAcD,EAAsBtC,GAAWsC,EAChE,IAAIE,EAAYC,EAAaJ,GAM7B,OAJKd,EAAamB,IAAIF,KACpBA,EAAYH,GAGP,CAACE,EAAaT,EAAUU,EACjC,CAEA,SAASG,EAAWrH,EAAS+G,EAAmBrC,EAASsC,EAAoBM,GAC3E,GAAiC,iBAAtBP,IAAmC/G,EAC5C,OAGF,IAAKiH,EAAaT,EAAUU,GAAaJ,EAAoBC,EAAmBrC,EAASsC,GAIzF,GAAID,KAAqBjB,EAAc,CACrC,MAAMyB,EAAerE,GACZ,SAAU2D,GACf,IAAKA,EAAMW,eAAkBX,EAAMW,gBAAkBX,EAAMY,iBAAmBZ,EAAMY,eAAejG,SAASqF,EAAMW,eAChH,OAAOtE,EAAGwE,KAAKC,KAAMd,E,EAK3BL,EAAWe,EAAaf,EAC1B,CAEA,MAAMD,EAASF,EAAiBrG,GAC1B4H,EAAWrB,EAAOW,KAAeX,EAAOW,GAAa,IACrDW,EAAmBvB,EAAYsB,EAAUpB,EAAUS,EAAcvC,EAAU,MAEjF,GAAImD,EAGF,YAFAA,EAAiBP,OAASO,EAAiBP,QAAUA,GAKvD,MAAMlB,EAAMD,EAAaK,EAAUO,EAAkBnH,QAAQ6F,EAAgB,KACvEvC,EAAK+D,EAxEb,SAAoCjH,EAASR,EAAU0D,GACrD,OAAO,SAASwB,EAAQmC,GACtB,MAAMiB,EAAc9H,EAAQ+H,iBAAiBvI,GAE7C,IAAK,IAAImF,OAAEA,GAAWkC,EAAOlC,GAAUA,IAAWgD,KAAMhD,EAASA,EAAOxD,WACtE,IAAK,MAAM6G,KAAcF,EACvB,GAAIE,IAAerD,EAUnB,OANAsD,EAAWpB,EAAO,CAAEY,eAAgB9C,IAEhCD,EAAQ4C,QACVY,EAAaC,IAAInI,EAAS6G,EAAMuB,KAAM5I,EAAU0D,GAG3CA,EAAGmF,MAAM1D,EAAQ,CAACkC,G,CAIjC,CAqDIyB,CAA2BtI,EAAS0E,EAAS8B,GArFjD,SAA0BxG,EAASkD,GACjC,OAAO,SAASwB,EAAQmC,GAOtB,OANAoB,EAAWpB,EAAO,CAAEY,eAAgBzH,IAEhC0E,EAAQ4C,QACVY,EAAaC,IAAInI,EAAS6G,EAAMuB,KAAMlF,GAGjCA,EAAGmF,MAAMrI,EAAS,CAAC6G,G,CAE9B,CA4EI0B,CAAiBvI,EAASwG,GAE5BtD,EAAGuD,mBAAqBQ,EAAcvC,EAAU,KAChDxB,EAAGsD,SAAWA,EACdtD,EAAGoE,OAASA,EACZpE,EAAG2C,SAAWO,EACdwB,EAASxB,GAAOlD,EAEhBlD,EAAQuD,iBAAiB2D,EAAWhE,EAAI+D,EAC1C,CAEA,SAASuB,EAAcxI,EAASuG,EAAQW,EAAWxC,EAAS+B,GAC1D,MAAMvD,EAAKoD,EAAYC,EAAOW,GAAYxC,EAAS+B,GAE9CvD,IAILlD,EAAQ4E,oBAAoBsC,EAAWhE,EAAIuF,QAAQhC,WAC5CF,EAAOW,GAAWhE,EAAG2C,UAC9B,CAEA,SAAS6C,EAAyB1I,EAASuG,EAAQW,EAAWyB,GAC5D,MAAMC,EAAoBrC,EAAOW,IAAc,GAE/C,IAAK,MAAO2B,EAAYhC,KAAUH,OAAOoC,QAAQF,GAC3CC,EAAWE,SAASJ,IACtBH,EAAcxI,EAASuG,EAAQW,EAAWL,EAAML,SAAUK,EAAMJ,mBAGtE,CAEA,SAASU,EAAaN,GAGpB,OADAA,EAAQA,EAAMjH,QAAQ8F,EAAgB,IAC/BI,EAAae,IAAUA,CAChC,CAEA,MAAMqB,EAAe,CACnBc,GAAGhJ,EAAS6G,EAAOnC,EAASsC,GAC1BK,EAAWrH,EAAS6G,EAAOnC,EAASsC,GAAoB,E,EAG1DiC,IAAIjJ,EAAS6G,EAAOnC,EAASsC,GAC3BK,EAAWrH,EAAS6G,EAAOnC,EAASsC,GAAoB,E,EAG1DmB,IAAInI,EAAS+G,EAAmBrC,EAASsC,GACvC,GAAiC,iBAAtBD,IAAmC/G,EAC5C,OAGF,MAAOiH,EAAaT,EAAUU,GAAaJ,EAAoBC,EAAmBrC,EAASsC,GACrFkC,EAAchC,IAAcH,EAC5BR,EAASF,EAAiBrG,GAC1B4I,EAAoBrC,EAAOW,IAAc,GACzCiC,EAAcpC,EAAkBqC,WAAW,KAEjD,QAAwB,IAAb5C,EAAX,CAUA,GAAI2C,EACF,IAAK,MAAME,KAAgB3C,OAAO4C,KAAK/C,GACrCmC,EAAyB1I,EAASuG,EAAQ8C,EAActC,EAAkBwC,MAAM,IAIpF,IAAK,MAAOC,EAAa3C,KAAUH,OAAOoC,QAAQF,GAAoB,CACpE,MAAMC,EAAaW,EAAY5J,QAAQ+F,EAAe,IAEjDuD,IAAenC,EAAkBgC,SAASF,IAC7CL,EAAcxI,EAASuG,EAAQW,EAAWL,EAAML,SAAUK,EAAMJ,mBAEpE,CAdA,KARA,CAEE,IAAKC,OAAO4C,KAAKV,GAAmBpI,OAClC,OAGFgI,EAAcxI,EAASuG,EAAQW,EAAWV,EAAUS,EAAcvC,EAAU,KAE9E,C,EAiBF+E,QAAQzJ,EAAS6G,EAAOlD,GACtB,GAAqB,iBAAVkD,IAAuB7G,EAChC,OAAO,KAGT,MAAM8C,EAAIT,IAIV,IAAIqH,EAAc,KACdC,GAAU,EACVC,GAAiB,EACjBC,GAAmB,EALHhD,IADFM,EAAaN,IAQZ/D,IACjB4G,EAAc5G,EAAE5C,MAAM2G,EAAOlD,GAE7Bb,EAAE9C,GAASyJ,QAAQC,GACnBC,GAAWD,EAAYI,uBACvBF,GAAkBF,EAAYK,gCAC9BF,EAAmBH,EAAYM,sBAGjC,IAAIC,EAAM,IAAI/J,MAAM2G,EAAO,CAAE8C,UAASO,YAAY,IAelD,OAdAD,EAAMhC,EAAWgC,EAAKtG,GAElBkG,GACFI,EAAIE,iBAGFP,GACF5J,EAAQC,cAAcgK,GAGpBA,EAAIJ,kBAAoBH,GAC1BA,EAAYS,iBAGPF,CACT,GAGF,SAAShC,EAAWmC,EAAKC,EAAO,IAC9B,IAAK,MAAOC,EAAKC,KAAU7D,OAAOoC,QAAQuB,GACxC,IACED,EAAIE,GAAOC,CAQb,CAPE,MAAMC,GACN9D,OAAO+D,eAAeL,EAAKE,EAAK,CAC9BI,cAAc,EACdC,IAAG,IACMJ,GAGb,CAGF,OAAOH,CACT,CChTA,MAAMQ,EAAa,IAAIC,IAEvBC,EAAe,CACbC,IAAI/K,EAASsK,EAAKU,GACXJ,EAAWxD,IAAIpH,IAClB4K,EAAWG,IAAI/K,EAAS,IAAI6K,KAG9B,MAAMI,EAAcL,EAAWD,IAAI3K,GAI9BiL,EAAY7D,IAAIkD,IAA6B,IAArBW,EAAYC,KAMzCD,EAAYF,IAAIT,EAAKU,GAJnBG,QAAQC,MAAO,+EAA8EC,MAAMC,KAAKL,EAAY3B,QAAQ,M,EAOhIqB,IAAG,CAAC3K,EAASsK,IACPM,EAAWxD,IAAIpH,IACV4K,EAAWD,IAAI3K,GAAS2K,IAAIL,IAG9B,KAGTiB,OAAOvL,EAASsK,GACd,IAAKM,EAAWxD,IAAIpH,GAClB,OAGF,MAAMiL,EAAcL,EAAWD,IAAI3K,GAEnCiL,EAAYO,OAAOlB,GAGM,IAArBW,EAAYC,MACdN,EAAWY,OAAOxL,EAEtB,GC9CF,SAASyL,EAAclB,GACrB,GAAc,SAAVA,EACF,OAAO,EAGT,GAAc,UAAVA,EACF,OAAO,EAGT,GAAIA,IAAUnG,OAAOmG,GAAOmB,WAC1B,OAAOtH,OAAOmG,GAGhB,GAAc,KAAVA,GAA0B,SAAVA,EAClB,OAAO,KAGT,GAAqB,iBAAVA,EACT,OAAOA,EAGT,IACE,OAAOoB,KAAKC,MAAMC,mBAAmBtB,GAGvC,CAFE,MAAMC,GACN,OAAOD,CACT,CACF,CAEA,SAASuB,EAAiBxB,GACxB,OAAOA,EAAI1K,QAAQ,UAAUmM,GAAQ,IAAGA,EAAIC,iBAC9C,CAEA,MAAMC,EAAc,CAClBC,iBAAiBlM,EAASsK,EAAKC,GAC7BvK,EAAQmM,aAAc,WAAUL,EAAiBxB,KAAQC,E,EAG3D6B,oBAAoBpM,EAASsK,GAC3BtK,EAAQqM,gBAAiB,WAAUP,EAAiBxB,K,EAGtDgC,kBAAkBtM,GAChB,IAAKA,EACH,MAAO,GAGT,MAAMuM,EAAa,GACbC,EAAS9F,OAAO4C,KAAKtJ,EAAQyM,SAASC,QAAOpC,GAAOA,EAAIlB,WAAW,QAAUkB,EAAIlB,WAAW,cAElG,IAAK,MAAMkB,KAAOkC,EAAQ,CACxB,IAAIG,EAAUrC,EAAI1K,QAAQ,MAAO,IACjC+M,EAAUA,EAAQC,OAAO,GAAGZ,cAAgBW,EAAQpD,MAAM,EAAGoD,EAAQnM,QACrE+L,EAAWI,GAAWlB,EAAczL,EAAQyM,QAAQnC,GACtD,CAEA,OAAOiC,C,EAGTM,iBAAgB,CAAC7M,EAASsK,IACjBmB,EAAczL,EAAQ2B,aAAc,WAAUmK,EAAiBxB,QCpD1E,MAAMwC,EAEOC,qBACT,MAAO,EACT,CAEWC,yBACT,MAAO,EACT,CAEWhK,kBACT,MAAM,IAAIiK,MAAM,sEAClB,CAEAC,WAAWC,GAIT,OAHAA,EAASxF,KAAKyF,gBAAgBD,GAC9BA,EAASxF,KAAK0F,kBAAkBF,GAChCxF,KAAK2F,iBAAiBH,GACfA,CACT,CAEAE,kBAAkBF,GAChB,OAAOA,CACT,CAEAC,gBAAgBD,EAAQnN,GACtB,MAAMuN,EAAapN,EAAUH,GAAWiM,EAAYY,iBAAiB7M,EAAS,UAAY,GAE1F,MAAO,IACF2H,KAAK6F,YAAYT,WACM,iBAAfQ,EAA0BA,EAAa,MAC9CpN,EAAUH,GAAWiM,EAAYK,kBAAkBtM,GAAW,MAC5C,iBAAXmN,EAAsBA,EAAS,GAE9C,CAEAG,iBAAiBH,EAAQM,EAAc9F,KAAK6F,YAAYR,aACtD,IAAK,MAAOU,EAAUC,KAAkBjH,OAAOoC,QAAQ2E,GAAc,CACnE,MAAMlD,EAAQ4C,EAAOO,GACfE,EAAYzN,EAAUoK,GAAS,UJ1BrCnK,OADSA,EI2B+CmK,GJzBlD,GAAEnK,IAGLsG,OAAOmH,UAAUnC,SAAShE,KAAKtH,GAAQP,MAAM,eAAe,GAAGmM,cIwBlE,IAAK,IAAI8B,OAAOH,GAAeI,KAAKH,GAClC,MAAM,IAAII,UACP,GAAErG,KAAK6F,YAAYxK,KAAKiL,0BAA0BP,qBAA4BE,yBAAiCD,MAGtH,CJlCWvN,KImCb,ECvCF,MAAM8N,UAAsBpB,EAC1BU,YAAYxN,EAASmN,GACnBgB,SAEAnO,EAAUO,EAAWP,MAKrB2H,KAAKyG,SAAWpO,EAChB2H,KAAK0G,QAAU1G,KAAKuF,WAAWC,GAE/BrC,EAAKC,IAAIpD,KAAKyG,SAAUzG,KAAK6F,YAAYc,SAAU3G,MACrD,CAGA4G,UACEzD,EAAKS,OAAO5D,KAAKyG,SAAUzG,KAAK6F,YAAYc,UAC5CpG,EAAaC,IAAIR,KAAKyG,SAAUzG,KAAK6F,YAAYgB,WAEjD,IAAK,MAAMC,KAAgB/H,OAAOgI,oBAAoB/G,MACpDA,KAAK8G,GAAgB,IAEzB,CAEAE,eAAe9L,EAAU7C,EAAS4O,GAAa,GAC7C/K,EAAuBhB,EAAU7C,EAAS4O,EAC5C,CAEA1B,WAAWC,GAIT,OAHAA,EAASxF,KAAKyF,gBAAgBD,EAAQxF,KAAKyG,UAC3CjB,EAASxF,KAAK0F,kBAAkBF,GAChCxF,KAAK2F,iBAAiBH,GACfA,CACT,CAGA0B,mBAAmB7O,GACjB,OAAO8K,EAAKH,IAAIpK,EAAWP,GAAU2H,KAAK2G,SAC5C,CAEAO,2BAA2B7O,EAASmN,EAAS,IAC3C,OAAOxF,KAAKmH,YAAY9O,IAAY,IAAI2H,KAAK3H,EAA2B,iBAAXmN,EAAsBA,EAAS,KAC9F,CAEW4B,qBACT,MApDY,cAqDd,CAEWT,sBACT,MAAQ,MAAK3G,KAAK3E,MACpB,CAEWwL,uBACT,MAAQ,IAAG7G,KAAK2G,UAClB,CAEAO,iBAAiB9L,GACf,MAAQ,GAAEA,IAAO4E,KAAK6G,WACxB,ECxEF,MAAMQ,EAAchP,IAClB,IAAIR,EAAWQ,EAAQ2B,aAAa,kBAEpC,IAAKnC,GAAyB,MAAbA,EAAkB,CACjC,IAAIyP,EAAgBjP,EAAQ2B,aAAa,QAMzC,IAAKsN,IAAmBA,EAAclG,SAAS,OAASkG,EAAc7F,WAAW,KAC/E,OAAO,KAIL6F,EAAclG,SAAS,OAASkG,EAAc7F,WAAW,OAC3D6F,EAAiB,IAAGA,EAAc1K,MAAM,KAAK,MAG/C/E,EAAWyP,GAAmC,MAAlBA,EAAwBA,EAAcC,OAAS,IAC7E,CAEA,OAAO3P,EAAcC,EAAS,EAG1B2P,EAAiB,CACrBvI,KAAI,CAACpH,EAAUQ,EAAUS,SAASoB,kBACzB,GAAGuN,UAAUC,QAAQxB,UAAU9F,iBAAiBL,KAAK1H,EAASR,IAGvE8P,QAAO,CAAC9P,EAAUQ,EAAUS,SAASoB,kBAC5BwN,QAAQxB,UAAUnN,cAAcgH,KAAK1H,EAASR,GAGvD+P,SAAQ,CAACvP,EAASR,IACT,GAAG4P,UAAUpP,EAAQuP,UAAU7C,QAAO8C,GAASA,EAAMC,QAAQjQ,KAGtEkQ,QAAQ1P,EAASR,GACf,MAAMkQ,EAAU,GAChB,IAAIC,EAAW3P,EAAQmB,WAAWF,QAAQzB,GAE1C,KAAOmQ,GACLD,EAAQlM,KAAKmM,GACbA,EAAWA,EAASxO,WAAWF,QAAQzB,GAGzC,OAAOkQ,C,EAGTE,KAAK5P,EAASR,GACZ,IAAIqQ,EAAW7P,EAAQ8P,uBAEvB,KAAOD,GAAU,CACf,GAAIA,EAASJ,QAAQjQ,GACnB,MAAO,CAACqQ,GAGVA,EAAWA,EAASC,sBACtB,CAEA,MAAO,E,EAGTC,KAAK/P,EAASR,GACZ,IAAIuQ,EAAO/P,EAAQgQ,mBAEnB,KAAOD,GAAM,CACX,GAAIA,EAAKN,QAAQjQ,GACf,MAAO,CAACuQ,GAGVA,EAAOA,EAAKC,kBACd,CAEA,MAAO,E,EAGTC,kBAAkBjQ,GAChB,MAAMkQ,EAAa,CACjB,IACA,SACA,QACA,WACA,SACA,UACA,aACA,4BACAC,KAAI3Q,GAAa,GAAEA,2BAAiC4Q,KAAK,KAE3D,OAAOzI,KAAKf,KAAKsJ,EAAYlQ,GAAS0M,QAAO2D,IAAOjP,EAAWiP,IAAO1P,EAAU0P,I,EAGlFC,uBAAuBtQ,GACrB,MAAMR,EAAWwP,EAAYhP,GAE7B,OAAIR,GACK2P,EAAeG,QAAQ9P,GAAYA,EAGrC,I,EAGT+Q,uBAAuBvQ,GACrB,MAAMR,EAAWwP,EAAYhP,GAE7B,OAAOR,EAAW2P,EAAeG,QAAQ9P,GAAY,I,EAGvDgR,gCAAgCxQ,GAC9B,MAAMR,EAAWwP,EAAYhP,GAE7B,OAAOR,EAAW2P,EAAevI,KAAKpH,GAAY,EACpD,GC/GIiR,EAAuB,CAACC,EAAWC,EAAS,UAChD,MAAMC,EAAc,gBAAeF,EAAUlC,YACvCzL,EAAO2N,EAAU1N,KAEvBkF,EAAac,GAAGvI,SAAUmQ,EAAa,qBAAoB7N,OAAU,SAAU8D,GAK7E,GAJI,CAAC,IAAK,QAAQkC,SAASpB,KAAKkJ,UAC9BhK,EAAMsD,iBAGJ/I,EAAWuG,MACb,OAGF,MAAMhD,EAASwK,EAAeoB,uBAAuB5I,OAASA,KAAK1G,QAAS,IAAG8B,KAC9D2N,EAAUI,oBAAoBnM,GAGtCgM,IACX,GAAE,ECAJ,MAAMI,UAAc7C,EAEPlL,kBACT,MAhBS,OAiBX,CAGAgO,QAGE,GAFmB9I,EAAauB,QAAQ9B,KAAKyG,SAjB5B,kBAmBFvE,iBACb,OAGFlC,KAAKyG,SAAS7M,UAAUgK,OApBJ,QAsBpB,MAAMqD,EAAajH,KAAKyG,SAAS7M,UAAUC,SAvBvB,QAwBpBmG,KAAKgH,gBAAe,IAAMhH,KAAKsJ,mBAAmBtJ,KAAKyG,SAAUQ,EACnE,CAGAqC,kBACEtJ,KAAKyG,SAAS7C,SACdrD,EAAauB,QAAQ9B,KAAKyG,SA/BR,mBAgClBzG,KAAK4G,SACP,CAGAM,uBAAuB1B,GACrB,OAAOxF,KAAKuJ,MAAK,WACf,MAAMC,EAAOJ,EAAMD,oBAAoBnJ,MAEvC,GAAsB,iBAAXwF,EAAX,CAIA,QAAqBiE,IAAjBD,EAAKhE,IAAyBA,EAAO/D,WAAW,MAAmB,gBAAX+D,EAC1D,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,GAAQxF,KANb,CAOF,GACF,EAOF8I,EAAqBM,EAAO,SAM5BpO,EAAmBoO,GCrEnB,MAMMM,EAAuB,4BAO7B,MAAMC,UAAepD,EAERlL,kBACT,MAhBS,QAiBX,CAGAuO,SAEE5J,KAAKyG,SAASjC,aAAa,eAAgBxE,KAAKyG,SAAS7M,UAAUgQ,OAjB7C,UAkBxB,CAGA1C,uBAAuB1B,GACrB,OAAOxF,KAAKuJ,MAAK,WACf,MAAMC,EAAOG,EAAOR,oBAAoBnJ,MAEzB,WAAXwF,GACFgE,EAAKhE,IAET,GACF,EAOFjF,EAAac,GAAGvI,SAlCc,2BAkCkB4Q,GAAsBxK,IACpEA,EAAMsD,iBAEN,MAAMqH,EAAS3K,EAAMlC,OAAO1D,QAAQoQ,GACvBC,EAAOR,oBAAoBU,GAEnCD,QAAQ,IAOf5O,EAAmB2O,GCtDnB,MAYMvE,EAAU,CACd0E,YAAa,KACbC,aAAc,KACdC,cAAe,MAGX3E,EAAc,CAClByE,YAAa,kBACbC,aAAc,kBACdC,cAAe,mBAOjB,MAAMC,UAAc9E,EAClBU,YAAYxN,EAASmN,GACnBgB,QACAxG,KAAKyG,SAAWpO,EAEXA,GAAY4R,EAAMC,gBAIvBlK,KAAK0G,QAAU1G,KAAKuF,WAAWC,GAC/BxF,KAAKmK,QAAU,EACfnK,KAAKoK,sBAAwBtJ,QAAQhJ,OAAOuS,cAC5CrK,KAAKsK,cACP,CAGWlF,qBACT,OAAOA,CACT,CAEWC,yBACT,OAAOA,CACT,CAEWhK,kBACT,MArDS,OAsDX,CAGAuL,UACErG,EAAaC,IAAIR,KAAKyG,SAzDR,YA0DhB,CAGA8D,OAAOrL,GACAc,KAAKoK,sBAMNpK,KAAKwK,wBAAwBtL,KAC/Bc,KAAKmK,QAAUjL,EAAMuL,SANrBzK,KAAKmK,QAAUjL,EAAMwL,QAAQ,GAAGD,OAQpC,CAEAE,KAAKzL,GACCc,KAAKwK,wBAAwBtL,KAC/Bc,KAAKmK,QAAUjL,EAAMuL,QAAUzK,KAAKmK,SAGtCnK,KAAK4K,eACL9O,EAAQkE,KAAK0G,QAAQoD,YACvB,CAEAe,MAAM3L,GACJc,KAAKmK,QAAUjL,EAAMwL,SAAWxL,EAAMwL,QAAQ7R,OAAS,EACrD,EACAqG,EAAMwL,QAAQ,GAAGD,QAAUzK,KAAKmK,OACpC,CAEAS,eACE,MAAME,EAAYnN,KAAKoN,IAAI/K,KAAKmK,SAEhC,GAAIW,GAlFgB,GAmFlB,OAGF,MAAME,EAAYF,EAAY9K,KAAKmK,QAEnCnK,KAAKmK,QAAU,EAEVa,GAILlP,EAAQkP,EAAY,EAAIhL,KAAK0G,QAAQsD,cAAgBhK,KAAK0G,QAAQqD,aACpE,CAEAO,cACMtK,KAAKoK,uBACP7J,EAAac,GAAGrB,KAAKyG,SAxGA,wBAwG6BvH,GAASc,KAAKuK,OAAOrL,KACvEqB,EAAac,GAAGrB,KAAKyG,SAxGF,sBAwG6BvH,GAASc,KAAK2K,KAAKzL,KAEnEc,KAAKyG,SAAS7M,UAAUqR,IAvGG,mBAyG3B1K,EAAac,GAAGrB,KAAKyG,SAhHD,uBAgH6BvH,GAASc,KAAKuK,OAAOrL,KACtEqB,EAAac,GAAGrB,KAAKyG,SAhHF,sBAgH6BvH,GAASc,KAAK6K,MAAM3L,KACpEqB,EAAac,GAAGrB,KAAKyG,SAhHH,qBAgH6BvH,GAASc,KAAK2K,KAAKzL,KAEtE,CAEAsL,wBAAwBtL,GACtB,OAAOc,KAAKoK,wBAjHS,QAiHiBlL,EAAMgM,aAlHrB,UAkHyDhM,EAAMgM,YACxF,CAGAhE,qBACE,MAAO,iBAAkBpO,SAASoB,iBAAmBiR,UAAUC,eAAiB,CAClF,ECrHF,MASMC,EAAa,OACbC,GAAa,OACbC,GAAiB,OACjBC,GAAkB,QAGlBC,GAAc,mBAQdC,GAAsB,WACtBC,GAAoB,SAepBC,GAAmB,CACvBC,UAAkBL,GAClBM,WAAmBP,IAGfnG,GAAU,CACd2G,SAAU,IACVC,UAAU,EACVC,MAAO,QACPC,MAAM,EACNC,OAAO,EACPC,MAAM,GAGF/G,GAAc,CAClB0G,SAAU,mBACVC,SAAU,UACVC,MAAO,mBACPC,KAAM,mBACNC,MAAO,UACPC,KAAM,WAOR,MAAMC,WAAiB9F,EACrBV,YAAYxN,EAASmN,GACnBgB,MAAMnO,EAASmN,GAEfxF,KAAKsM,UAAY,KACjBtM,KAAKuM,eAAiB,KACtBvM,KAAKwM,YAAa,EAClBxM,KAAKyM,aAAe,KACpBzM,KAAK0M,aAAe,KAEpB1M,KAAK2M,mBAAqBnF,EAAeG,QAzCjB,uBAyC8C3H,KAAKyG,UAC3EzG,KAAK4M,qBAED5M,KAAK0G,QAAQwF,OAASR,IACxB1L,KAAK6M,OAET,CAGWzH,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEWhK,kBACT,MA9FS,UA+FX,CAGA+M,OACEpI,KAAK8M,OAAOzB,EACd,CAEA0B,mBAIOjU,SAASkU,QAAUhU,EAAUgH,KAAKyG,WACrCzG,KAAKoI,MAET,CAEAH,OACEjI,KAAK8M,OAAOxB,GACd,CAEAW,QACMjM,KAAKwM,YACPpU,EAAqB4H,KAAKyG,UAG5BzG,KAAKiN,gBACP,CAEAJ,QACE7M,KAAKiN,iBACLjN,KAAKkN,kBAELlN,KAAKsM,UAAYa,aAAY,IAAMnN,KAAK+M,mBAAmB/M,KAAK0G,QAAQqF,SAC1E,CAEAqB,oBACOpN,KAAK0G,QAAQwF,OAIdlM,KAAKwM,WACPjM,EAAae,IAAItB,KAAKyG,SAAUgF,IAAY,IAAMzL,KAAK6M,UAIzD7M,KAAK6M,QACP,CAEAQ,GAAG5P,GACD,MAAM6P,EAAQtN,KAAKuN,YACnB,GAAI9P,EAAQ6P,EAAMzU,OAAS,GAAK4E,EAAQ,EACtC,OAGF,GAAIuC,KAAKwM,WAEP,YADAjM,EAAae,IAAItB,KAAKyG,SAAUgF,IAAY,IAAMzL,KAAKqN,GAAG5P,KAI5D,MAAM+P,EAAcxN,KAAKyN,cAAczN,KAAK0N,cAC5C,GAAIF,IAAgB/P,EAClB,OAGF,MAAMkQ,EAAQlQ,EAAQ+P,EAAcnC,EAAaC,GAEjDtL,KAAK8M,OAAOa,EAAOL,EAAM7P,GAC3B,CAEAmJ,UACM5G,KAAK0M,cACP1M,KAAK0M,aAAa9F,UAGpBJ,MAAMI,SACR,CAGAlB,kBAAkBF,GAEhB,OADAA,EAAOoI,gBAAkBpI,EAAOuG,SACzBvG,CACT,CAEAoH,qBACM5M,KAAK0G,QAAQsF,UACfzL,EAAac,GAAGrB,KAAKyG,SApKJ,uBAoK6BvH,GAASc,KAAK6N,SAAS3O,KAG5C,UAAvBc,KAAK0G,QAAQuF,QACf1L,EAAac,GAAGrB,KAAKyG,SAvKD,0BAuK6B,IAAMzG,KAAKiM,UAC5D1L,EAAac,GAAGrB,KAAKyG,SAvKD,0BAuK6B,IAAMzG,KAAKoN,uBAG1DpN,KAAK0G,QAAQyF,OAASlC,EAAMC,eAC9BlK,KAAK8N,yBAET,CAEAA,0BACE,IAAK,MAAMC,KAAOvG,EAAevI,KAhKX,qBAgKmCe,KAAKyG,UAC5DlG,EAAac,GAAG0M,EAhLI,yBAgLmB7O,GAASA,EAAMsD,mBAGxD,MAqBMwL,EAAc,CAClBjE,aAAc,IAAM/J,KAAK8M,OAAO9M,KAAKiO,kBAAkB1C,KACvDvB,cAAe,IAAMhK,KAAK8M,OAAO9M,KAAKiO,kBAAkBzC,KACxD1B,YAxBkB,KACS,UAAvB9J,KAAK0G,QAAQuF,QAYjBjM,KAAKiM,QACDjM,KAAKyM,cACPyB,aAAalO,KAAKyM,cAGpBzM,KAAKyM,aAAevP,YAAW,IAAM8C,KAAKoN,qBAjNjB,IAiN+DpN,KAAK0G,QAAQqF,UAAS,GAShH/L,KAAK0M,aAAe,IAAIzC,EAAMjK,KAAKyG,SAAUuH,EAC/C,CAEAH,SAAS3O,GACP,GAAI,kBAAkBkH,KAAKlH,EAAMlC,OAAOkM,SACtC,OAGF,MAAM8B,EAAYY,GAAiB1M,EAAMyD,KACrCqI,IACF9L,EAAMsD,iBACNxC,KAAK8M,OAAO9M,KAAKiO,kBAAkBjD,IAEvC,CAEAyC,cAAcpV,GACZ,OAAO2H,KAAKuN,YAAY7P,QAAQrF,EAClC,CAEA8V,2BAA2B1Q,GACzB,IAAKuC,KAAK2M,mBACR,OAGF,MAAMyB,EAAkB5G,EAAeG,QA1NnB,UA0N4C3H,KAAK2M,oBAErEyB,EAAgBxU,UAAUgK,OAAO+H,IACjCyC,EAAgB1J,gBAAgB,gBAEhC,MAAM2J,EAAqB7G,EAAeG,QAAS,sBAAqBlK,MAAWuC,KAAK2M,oBAEpF0B,IACFA,EAAmBzU,UAAUqR,IAAIU,IACjC0C,EAAmB7J,aAAa,eAAgB,QAEpD,CAEA0I,kBACE,MAAM7U,EAAU2H,KAAKuM,gBAAkBvM,KAAK0N,aAE5C,IAAKrV,EACH,OAGF,MAAMiW,EAAkB7R,OAAO8R,SAASlW,EAAQ2B,aAAa,oBAAqB,IAElFgG,KAAK0G,QAAQqF,SAAWuC,GAAmBtO,KAAK0G,QAAQkH,eAC1D,CAEAd,OAAOa,EAAOtV,EAAU,MACtB,GAAI2H,KAAKwM,WACP,OAGF,MAAMnP,EAAgB2C,KAAK0N,aACrBc,EAASb,IAAUtC,EACnBoD,EAAcpW,GAAW8E,EAAqB6C,KAAKuN,YAAalQ,EAAemR,EAAQxO,KAAK0G,QAAQ0F,MAE1G,GAAIqC,IAAgBpR,EAClB,OAGF,MAAMqR,EAAmB1O,KAAKyN,cAAcgB,GAEtCE,EAAeC,GACZrO,EAAauB,QAAQ9B,KAAKyG,SAAUmI,EAAW,CACpD/O,cAAe4O,EACfzD,UAAWhL,KAAK6O,kBAAkBlB,GAClChK,KAAM3D,KAAKyN,cAAcpQ,GACzBgQ,GAAIqB,IAMR,GAFmBC,EA5RF,qBA8RFzM,iBACb,OAGF,IAAK7E,IAAkBoR,EAGrB,OAGF,MAAMK,EAAYhO,QAAQd,KAAKsM,WAC/BtM,KAAKiM,QAELjM,KAAKwM,YAAa,EAElBxM,KAAKmO,2BAA2BO,GAChC1O,KAAKuM,eAAiBkC,EAEtB,MAAMM,EAAuBP,EAnSR,sBADF,oBAqSbQ,EAAiBR,EAnSH,qBACA,qBAoSpBC,EAAY7U,UAAUqR,IAAI+D,GAE1BxU,EAAOiU,GAEPpR,EAAczD,UAAUqR,IAAI8D,GAC5BN,EAAY7U,UAAUqR,IAAI8D,GAa1B/O,KAAKgH,gBAXoB,KACvByH,EAAY7U,UAAUgK,OAAOmL,EAAsBC,GACnDP,EAAY7U,UAAUqR,IAAIU,IAE1BtO,EAAczD,UAAUgK,OAAO+H,GAAmBqD,EAAgBD,GAElE/O,KAAKwM,YAAa,EAElBmC,EAAalD,GAAW,GAGYpO,EAAe2C,KAAKiP,eAEtDH,GACF9O,KAAK6M,OAET,CAEAoC,cACE,OAAOjP,KAAKyG,SAAS7M,UAAUC,SAlUV,QAmUvB,CAEA6T,aACE,OAAOlG,EAAeG,QA9TGuH,wBA8T2BlP,KAAKyG,SAC3D,CAEA8G,YACE,OAAO/F,EAAevI,KAnUJ,iBAmUwBe,KAAKyG,SACjD,CAEAwG,iBACMjN,KAAKsM,YACP6C,cAAcnP,KAAKsM,WACnBtM,KAAKsM,UAAY,KAErB,CAEA2B,kBAAkBjD,GAChB,OAAIlQ,IACKkQ,IAAcO,GAAiBD,GAAaD,EAG9CL,IAAcO,GAAiBF,EAAaC,EACrD,CAEAuD,kBAAkBlB,GAChB,OAAI7S,IACK6S,IAAUrC,GAAaC,GAAiBC,GAG1CmC,IAAUrC,GAAaE,GAAkBD,EAClD,CAGArE,uBAAuB1B,GACrB,OAAOxF,KAAKuJ,MAAK,WACf,MAAMC,EAAO6C,GAASlD,oBAAoBnJ,KAAMwF,GAEhD,GAAsB,iBAAXA,GAKX,GAAsB,iBAAXA,EAAqB,CAC9B,QAAqBiE,IAAjBD,EAAKhE,IAAyBA,EAAO/D,WAAW,MAAmB,gBAAX+D,EAC1D,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,IACP,OAVEgE,EAAK6D,GAAG7H,EAWZ,GACF,EAOFjF,EAAac,GAAGvI,SAjYc,6BAeF,uCAkXyC,SAAUoG,GAC7E,MAAMlC,EAASwK,EAAeoB,uBAAuB5I,MAErD,IAAKhD,IAAWA,EAAOpD,UAAUC,SAAS6R,IACxC,OAGFxM,EAAMsD,iBAEN,MAAM4M,EAAW/C,GAASlD,oBAAoBnM,GACxCqS,EAAarP,KAAKhG,aAAa,oBAErC,OAAIqV,GACFD,EAAS/B,GAAGgC,QACZD,EAAShC,qBAIyC,SAAhD9I,EAAYY,iBAAiBlF,KAAM,UACrCoP,EAAShH,YACTgH,EAAShC,sBAIXgC,EAASnH,YACTmH,EAAShC,oBACX,IAEA7M,EAAac,GAAGvJ,OA9Za,6BA8ZgB,KAC3C,MAAMwX,EAAY9H,EAAevI,KA9YR,6BAgZzB,IAAK,MAAMmQ,KAAYE,EACrBjD,GAASlD,oBAAoBiG,EAC/B,IAOFpU,EAAmBqR,ICncnB,MAWMkD,GAAkB,OAClBC,GAAsB,WACtBC,GAAwB,aASxB/F,GAAuB,8BAEvBtE,GAAU,CACdsK,OAAQ,KACR9F,QAAQ,GAGJvE,GAAc,CAClBqK,OAAQ,iBACR9F,OAAQ,WAOV,MAAM+F,WAAiBpJ,EACrBV,YAAYxN,EAASmN,GACnBgB,MAAMnO,EAASmN,GAEfxF,KAAK4P,kBAAmB,EACxB5P,KAAK6P,cAAgB,GAErB,MAAMC,EAAatI,EAAevI,KAAKyK,IAEvC,IAAK,MAAMqG,KAAQD,EAAY,CAC7B,MAAMjY,EAAW2P,EAAemB,uBAAuBoH,GACjDC,EAAgBxI,EAAevI,KAAKpH,GACvCkN,QAAOkL,GAAgBA,IAAiBjQ,KAAKyG,WAE/B,OAAb5O,GAAqBmY,EAAcnX,QACrCmH,KAAK6P,cAAchU,KAAKkU,EAE5B,CAEA/P,KAAKkQ,sBAEAlQ,KAAK0G,QAAQgJ,QAChB1P,KAAKmQ,0BAA0BnQ,KAAK6P,cAAe7P,KAAKoQ,YAGtDpQ,KAAK0G,QAAQkD,QACf5J,KAAK4J,QAET,CAGWxE,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEWhK,kBACT,MA9ES,UA+EX,CAGAuO,SACM5J,KAAKoQ,WACPpQ,KAAKqQ,OAELrQ,KAAKsQ,MAET,CAEAA,OACE,GAAItQ,KAAK4P,kBAAoB5P,KAAKoQ,WAChC,OAGF,IAAIG,EAAiB,GASrB,GANIvQ,KAAK0G,QAAQgJ,SACfa,EAAiBvQ,KAAKwQ,uBA9EH,wCA+EhBzL,QAAO1M,GAAWA,IAAY2H,KAAKyG,WACnC+B,KAAInQ,GAAWsX,GAASxG,oBAAoB9Q,EAAS,CAAEuR,QAAQ,OAGhE2G,EAAe1X,QAAU0X,EAAe,GAAGX,iBAC7C,OAIF,GADmBrP,EAAauB,QAAQ9B,KAAKyG,SAvG7B,oBAwGDvE,iBACb,OAGF,IAAK,MAAMuO,KAAkBF,EAC3BE,EAAeJ,OAGjB,MAAMK,EAAY1Q,KAAK2Q,gBAEvB3Q,KAAKyG,SAAS7M,UAAUgK,OAAO4L,IAC/BxP,KAAKyG,SAAS7M,UAAUqR,IAAIwE,IAE5BzP,KAAKyG,SAASmK,MAAMF,GAAa,EAEjC1Q,KAAKmQ,0BAA0BnQ,KAAK6P,eAAe,GACnD7P,KAAK4P,kBAAmB,EAExB,MAYMiB,EAAc,SADSH,EAAU,GAAGpK,cAAgBoK,EAAU9O,MAAM,KAG1E5B,KAAKgH,gBAdY,KACfhH,KAAK4P,kBAAmB,EAExB5P,KAAKyG,SAAS7M,UAAUgK,OAAO6L,IAC/BzP,KAAKyG,SAAS7M,UAAUqR,IAAIuE,GAAqBD,IAEjDvP,KAAKyG,SAASmK,MAAMF,GAAa,GAEjCnQ,EAAauB,QAAQ9B,KAAKyG,SAjIX,oBAiIiC,GAMpBzG,KAAKyG,UAAU,GAC7CzG,KAAKyG,SAASmK,MAAMF,GAAc,GAAE1Q,KAAKyG,SAASoK,MACpD,CAEAR,OACE,GAAIrQ,KAAK4P,mBAAqB5P,KAAKoQ,WACjC,OAIF,GADmB7P,EAAauB,QAAQ9B,KAAKyG,SA/I7B,oBAgJDvE,iBACb,OAGF,MAAMwO,EAAY1Q,KAAK2Q,gBAEvB3Q,KAAKyG,SAASmK,MAAMF,GAAc,GAAE1Q,KAAKyG,SAASqK,wBAAwBJ,OAE1ElW,EAAOwF,KAAKyG,UAEZzG,KAAKyG,SAAS7M,UAAUqR,IAAIwE,IAC5BzP,KAAKyG,SAAS7M,UAAUgK,OAAO4L,GAAqBD,IAEpD,IAAK,MAAMzN,KAAW9B,KAAK6P,cAAe,CACxC,MAAMxX,EAAUmP,EAAeoB,uBAAuB9G,GAElDzJ,IAAY2H,KAAKoQ,SAAS/X,IAC5B2H,KAAKmQ,0BAA0B,CAACrO,IAAU,EAE9C,CAEA9B,KAAK4P,kBAAmB,EASxB5P,KAAKyG,SAASmK,MAAMF,GAAa,GAEjC1Q,KAAKgH,gBATY,KACfhH,KAAK4P,kBAAmB,EACxB5P,KAAKyG,SAAS7M,UAAUgK,OAAO6L,IAC/BzP,KAAKyG,SAAS7M,UAAUqR,IAAIuE,IAC5BjP,EAAauB,QAAQ9B,KAAKyG,SA1KV,qBA0KiC,GAKrBzG,KAAKyG,UAAU,EAC/C,CAEA2J,SAAS/X,EAAU2H,KAAKyG,UACtB,OAAOpO,EAAQuB,UAAUC,SAAS0V,GACpC,CAGA7J,kBAAkBF,GAGhB,OAFAA,EAAOoE,OAAS9I,QAAQ0E,EAAOoE,QAC/BpE,EAAOkK,OAAS9W,EAAW4M,EAAOkK,QAC3BlK,CACT,CAEAmL,gBACE,OAAO3Q,KAAKyG,SAAS7M,UAAUC,SAtLL,uBAEhB,QACC,QAoLb,CAEAqW,sBACE,IAAKlQ,KAAK0G,QAAQgJ,OAChB,OAGF,MAAM9H,EAAW5H,KAAKwQ,uBAAuB9G,IAE7C,IAAK,MAAMrR,KAAWuP,EAAU,CAC9B,MAAMmJ,EAAWvJ,EAAeoB,uBAAuBvQ,GAEnD0Y,GACF/Q,KAAKmQ,0BAA0B,CAAC9X,GAAU2H,KAAKoQ,SAASW,GAE5D,CACF,CAEAP,uBAAuB3Y,GACrB,MAAM+P,EAAWJ,EAAevI,KA3MA,6BA2MiCe,KAAK0G,QAAQgJ,QAE9E,OAAOlI,EAAevI,KAAKpH,EAAUmI,KAAK0G,QAAQgJ,QAAQ3K,QAAO1M,IAAYuP,EAASxG,SAAS/I,IACjG,CAEA8X,0BAA0Ba,EAAcC,GACtC,GAAKD,EAAanY,OAIlB,IAAK,MAAMR,KAAW2Y,EACpB3Y,EAAQuB,UAAUgQ,OAvNK,aAuNyBqH,GAChD5Y,EAAQmM,aAAa,gBAAiByM,EAE1C,CAGA/J,uBAAuB1B,GACrB,MAAMkB,EAAU,GAKhB,MAJsB,iBAAXlB,GAAuB,YAAYY,KAAKZ,KACjDkB,EAAQkD,QAAS,GAGZ5J,KAAKuJ,MAAK,WACf,MAAMC,EAAOmG,GAASxG,oBAAoBnJ,KAAM0G,GAEhD,GAAsB,iBAAXlB,EAAqB,CAC9B,QAA4B,IAAjBgE,EAAKhE,GACd,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,IACP,CACF,GACF,EAOFjF,EAAac,GAAGvI,SA1Pc,6BA0PkB4Q,IAAsB,SAAUxK,IAEjD,MAAzBA,EAAMlC,OAAOkM,SAAoBhK,EAAMY,gBAAmD,MAAjCZ,EAAMY,eAAeoJ,UAChFhK,EAAMsD,iBAGR,IAAK,MAAMnK,KAAWmP,EAAeqB,gCAAgC7I,MACnE2P,GAASxG,oBAAoB9Q,EAAS,CAAEuR,QAAQ,IAASA,QAE7D,IAMA5O,EAAmB2U,IC1QnB,MAAMtU,GAAO,WAOP6V,GAAe,UACfC,GAAiB,YAOjBC,GAAwB,6BACxBC,GAA0B,+BAG1B9B,GAAkB,OAOlB7F,GAAuB,4DACvB4H,GAA8B,GAAE5H,UAChC6H,GAAgB,iBAKhBC,GAAgB1W,IAAU,UAAY,YACtC2W,GAAmB3W,IAAU,YAAc,UAC3C4W,GAAmB5W,IAAU,aAAe,eAC5C6W,GAAsB7W,IAAU,eAAiB,aACjD8W,GAAkB9W,IAAU,aAAe,cAC3C+W,GAAiB/W,IAAU,cAAgB,aAI3CsK,GAAU,CACd0M,WAAW,EACXC,SAAU,kBACVC,QAAS,UACTC,OAAQ,CAAC,EAAG,GACZC,aAAc,KACdC,UAAW,UAGP9M,GAAc,CAClByM,UAAW,mBACXC,SAAU,mBACVC,QAAS,SACTC,OAAQ,0BACRC,aAAc,yBACdC,UAAW,2BAOb,MAAMC,WAAiB7L,EACrBV,YAAYxN,EAASmN,GACnBgB,MAAMnO,EAASmN,GAEfxF,KAAKqS,QAAU,KACfrS,KAAKsS,QAAUtS,KAAKyG,SAASjN,WAE7BwG,KAAKuS,MAAQ/K,EAAeY,KAAKpI,KAAKyG,SAAU8K,IAAe,IAC7D/J,EAAeS,KAAKjI,KAAKyG,SAAU8K,IAAe,IAClD/J,EAAeG,QAAQ4J,GAAevR,KAAKsS,SAC7CtS,KAAKwS,UAAYxS,KAAKyS,eACxB,CAGWrN,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEWhK,kBACT,OAAOA,EACT,CAGAuO,SACE,OAAO5J,KAAKoQ,WAAapQ,KAAKqQ,OAASrQ,KAAKsQ,MAC9C,CAEAA,OACE,GAAI7W,EAAWuG,KAAKyG,WAAazG,KAAKoQ,WACpC,OAGF,MAAMvQ,EAAgB,CACpBA,cAAeG,KAAKyG,UAKtB,IAFkBlG,EAAauB,QAAQ9B,KAAKyG,SA3F5B,mBA2FkD5G,GAEpDqC,iBAAd,CAUA,GANAlC,KAAK0S,gBAMD,iBAAkB5Z,SAASoB,kBAAoB8F,KAAKsS,QAAQhZ,QAtFxC,eAuFtB,IAAK,MAAMjB,IAAW,GAAGoP,UAAU3O,SAAS8B,KAAKgN,UAC/CrH,EAAac,GAAGhJ,EAAS,YAAakC,GAI1CyF,KAAKyG,SAASkM,QACd3S,KAAKyG,SAASjC,aAAa,iBAAiB,GAE5CxE,KAAKuS,MAAM3Y,UAAUqR,IAAIsE,IACzBvP,KAAKyG,SAAS7M,UAAUqR,IAAIsE,IAC5BhP,EAAauB,QAAQ9B,KAAKyG,SAjHT,oBAiHgC5G,EAnBjD,CAoBF,CAEAwQ,OACE,GAAI5W,EAAWuG,KAAKyG,YAAczG,KAAKoQ,WACrC,OAGF,MAAMvQ,EAAgB,CACpBA,cAAeG,KAAKyG,UAGtBzG,KAAK4S,cAAc/S,EACrB,CAEA+G,UACM5G,KAAKqS,SACPrS,KAAKqS,QAAQQ,UAGfrM,MAAMI,SACR,CAEAkM,SACE9S,KAAKwS,UAAYxS,KAAKyS,gBAClBzS,KAAKqS,SACPrS,KAAKqS,QAAQS,QAEjB,CAGAF,cAAc/S,GAEZ,IADkBU,EAAauB,QAAQ9B,KAAKyG,SApJ5B,mBAoJkD5G,GACpDqC,iBAAd,CAMA,GAAI,iBAAkBpJ,SAASoB,gBAC7B,IAAK,MAAM7B,IAAW,GAAGoP,UAAU3O,SAAS8B,KAAKgN,UAC/CrH,EAAaC,IAAInI,EAAS,YAAakC,GAIvCyF,KAAKqS,SACPrS,KAAKqS,QAAQQ,UAGf7S,KAAKuS,MAAM3Y,UAAUgK,OAAO2L,IAC5BvP,KAAKyG,SAAS7M,UAAUgK,OAAO2L,IAC/BvP,KAAKyG,SAASjC,aAAa,gBAAiB,SAC5CF,EAAYG,oBAAoBzE,KAAKuS,MAAO,UAC5ChS,EAAauB,QAAQ9B,KAAKyG,SAxKR,qBAwKgC5G,EAlBlD,CAmBF,CAEA0F,WAAWC,GAGT,GAAgC,iBAFhCA,EAASgB,MAAMjB,WAAWC,IAER2M,YAA2B3Z,EAAUgN,EAAO2M,YACV,mBAA3C3M,EAAO2M,UAAUrB,sBAGxB,MAAM,IAAIzK,UAAW,GAAEhL,GAAKiL,+GAG9B,OAAOd,CACT,CAEAkN,gBACE,QAAsB,IAAXK,EACT,MAAM,IAAI1M,UAAU,gEAGtB,IAAI2M,EAAmBhT,KAAKyG,SAEG,WAA3BzG,KAAK0G,QAAQyL,UACfa,EAAmBhT,KAAKsS,QACf9Z,EAAUwH,KAAK0G,QAAQyL,WAChCa,EAAmBpa,EAAWoH,KAAK0G,QAAQyL,WACA,iBAA3BnS,KAAK0G,QAAQyL,YAC7Ba,EAAmBhT,KAAK0G,QAAQyL,WAGlC,MAAMD,EAAelS,KAAKiT,mBAC1BjT,KAAKqS,QAAUU,EAAOG,aAAaF,EAAkBhT,KAAKuS,MAAOL,EACnE,CAEA9B,WACE,OAAOpQ,KAAKuS,MAAM3Y,UAAUC,SAAS0V,GACvC,CAEA4D,gBACE,MAAMC,EAAiBpT,KAAKsS,QAE5B,GAAIc,EAAexZ,UAAUC,SAzMN,WA0MrB,OAAO+X,GAGT,GAAIwB,EAAexZ,UAAUC,SA5MJ,aA6MvB,OAAOgY,GAGT,GAAIuB,EAAexZ,UAAUC,SA/MA,iBAgN3B,MAhMsB,MAmMxB,GAAIuZ,EAAexZ,UAAUC,SAlNE,mBAmN7B,MAnMyB,SAuM3B,MAAMwZ,EAAkF,QAA1Ela,iBAAiB6G,KAAKuS,OAAOnZ,iBAAiB,iBAAiBmO,OAE7E,OAAI6L,EAAexZ,UAAUC,SA7NP,UA8NbwZ,EAAQ5B,GAAmBD,GAG7B6B,EAAQ1B,GAAsBD,EACvC,CAEAe,gBACE,OAAkD,OAA3CzS,KAAKyG,SAASnN,QA5ND,UA6NtB,CAEAga,aACE,MAAMrB,OAAEA,GAAWjS,KAAK0G,QAExB,MAAsB,iBAAXuL,EACFA,EAAOrV,MAAM,KAAK4L,KAAI5F,GAASnG,OAAO8R,SAAS3L,EAAO,MAGzC,mBAAXqP,EACFsB,GAActB,EAAOsB,EAAYvT,KAAKyG,UAGxCwL,CACT,CAEAgB,mBACE,MAAMO,EAAwB,CAC5BC,UAAWzT,KAAKmT,gBAChBO,UAAW,CAAC,CACVtY,KAAM,kBACNuY,QAAS,CACP5B,SAAU/R,KAAK0G,QAAQqL,WAG3B,CACE3W,KAAM,SACNuY,QAAS,CACP1B,OAAQjS,KAAKsT,iBAcnB,OARItT,KAAKwS,WAAsC,WAAzBxS,KAAK0G,QAAQsL,WACjC1N,EAAYC,iBAAiBvE,KAAKuS,MAAO,SAAU,UACnDiB,EAAsBE,UAAY,CAAC,CACjCtY,KAAM,cACNwY,SAAS,KAIN,IACFJ,KACA1X,EAAQkE,KAAK0G,QAAQwL,aAAc,CAACsB,IAE3C,CAEAK,iBAAgBlR,IAAEA,EAAG3F,OAAEA,IACrB,MAAMsQ,EAAQ9F,EAAevI,KA5QF,8DA4Q+Be,KAAKuS,OAAOxN,QAAO1M,GAAWW,EAAUX,KAE7FiV,EAAMzU,QAMXsE,EAAqBmQ,EAAOtQ,EAAQ2F,IAAQwO,IAAiB7D,EAAMlM,SAASpE,IAAS2V,OACvF,CAGAzL,uBAAuB1B,GACrB,OAAOxF,KAAKuJ,MAAK,WACf,MAAMC,EAAO4I,GAASjJ,oBAAoBnJ,KAAMwF,GAEhD,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBgE,EAAKhE,GACd,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,IANL,CAOF,GACF,CAEA0B,kBAAkBhI,GAChB,GA/TuB,IA+TnBA,EAAM2K,QAAiD,UAAf3K,EAAMuB,MAlUtC,QAkU0DvB,EAAMyD,IAC1E,OAGF,MAAMmR,EAActM,EAAevI,KAAKqS,IAExC,IAAK,MAAM1H,KAAUkK,EAAa,CAChC,MAAMC,EAAU3B,GAASjL,YAAYyC,GACrC,IAAKmK,IAAyC,IAA9BA,EAAQrN,QAAQoL,UAC9B,SAGF,MAAMkC,EAAe9U,EAAM8U,eACrBC,EAAeD,EAAa5S,SAAS2S,EAAQxB,OACnD,GACEyB,EAAa5S,SAAS2S,EAAQtN,WACC,WAA9BsN,EAAQrN,QAAQoL,YAA2BmC,GACb,YAA9BF,EAAQrN,QAAQoL,WAA2BmC,EAE5C,SAIF,GAAIF,EAAQxB,MAAM1Y,SAASqF,EAAMlC,UAA4B,UAAfkC,EAAMuB,MAzV1C,QAyV8DvB,EAAMyD,KAAoB,qCAAqCyD,KAAKlH,EAAMlC,OAAOkM,UACvJ,SAGF,MAAMrJ,EAAgB,CAAEA,cAAekU,EAAQtN,UAE5B,UAAfvH,EAAMuB,OACRZ,EAAcoJ,WAAa/J,GAG7B6U,EAAQnB,cAAc/S,EACxB,CACF,CAEAqH,6BAA6BhI,GAI3B,MAAMgV,EAAU,kBAAkB9N,KAAKlH,EAAMlC,OAAOkM,SAC9CiL,EA7WS,WA6WOjV,EAAMyD,IACtByR,EAAkB,CAAClD,GAAcC,IAAgB/P,SAASlC,EAAMyD,KAEtE,IAAKyR,IAAoBD,EACvB,OAGF,GAAID,IAAYC,EACd,OAGFjV,EAAMsD,iBAGN,MAAM6R,EAAkBrU,KAAK8H,QAAQ4B,IACnC1J,KACCwH,EAAeS,KAAKjI,KAAM0J,IAAsB,IAC/ClC,EAAeY,KAAKpI,KAAM0J,IAAsB,IAChDlC,EAAeG,QAAQ+B,GAAsBxK,EAAMY,eAAetG,YAEhE6J,EAAW+O,GAASjJ,oBAAoBkL,GAE9C,GAAID,EAIF,OAHAlV,EAAMoV,kBACNjR,EAASiN,YACTjN,EAASwQ,gBAAgB3U,GAIvBmE,EAAS+M,aACXlR,EAAMoV,kBACNjR,EAASgN,OACTgE,EAAgB1B,QAEpB,EAOFpS,EAAac,GAAGvI,SAAUuY,GAAwB3H,GAAsB0I,GAASmC,uBACjFhU,EAAac,GAAGvI,SAAUuY,GAAwBE,GAAea,GAASmC,uBAC1EhU,EAAac,GAAGvI,SAAUsY,GAAsBgB,GAASoC,YACzDjU,EAAac,GAAGvI,SA7Yc,6BA6YkBsZ,GAASoC,YACzDjU,EAAac,GAAGvI,SAAUsY,GAAsB1H,IAAsB,SAAUxK,GAC9EA,EAAMsD,iBACN4P,GAASjJ,oBAAoBnJ,MAAM4J,QACrC,IAMA5O,EAAmBoX,ICrbnB,MAAMqC,GAAyB,oDACzBC,GAA0B,cAC1BC,GAAmB,gBACnBC,GAAkB,eAMxB,MAAMC,GACJhP,cACE7F,KAAKyG,SAAW3N,SAAS8B,IAC3B,CAGAka,WAEE,MAAMC,EAAgBjc,SAASoB,gBAAgB8a,YAC/C,OAAOrX,KAAKoN,IAAIjT,OAAOmd,WAAaF,EACtC,CAEA1E,OACE,MAAM6E,EAAQlV,KAAK8U,WACnB9U,KAAKmV,mBAELnV,KAAKoV,sBAAsBpV,KAAKyG,SAAUkO,IAAkBU,GAAmBA,EAAkBH,IAEjGlV,KAAKoV,sBAAsBX,GAAwBE,IAAkBU,GAAmBA,EAAkBH,IAC1GlV,KAAKoV,sBAAsBV,GAAyBE,IAAiBS,GAAmBA,EAAkBH,GAC5G,CAEAI,QACEtV,KAAKuV,wBAAwBvV,KAAKyG,SAAU,YAC5CzG,KAAKuV,wBAAwBvV,KAAKyG,SAAUkO,IAC5C3U,KAAKuV,wBAAwBd,GAAwBE,IACrD3U,KAAKuV,wBAAwBb,GAAyBE,GACxD,CAEAY,gBACE,OAAOxV,KAAK8U,WAAa,CAC3B,CAGAK,mBACEnV,KAAKyV,sBAAsBzV,KAAKyG,SAAU,YAC1CzG,KAAKyG,SAASmK,MAAM8E,SAAW,QACjC,CAEAN,sBAAsBvd,EAAU8d,EAAeza,GAC7C,MAAM0a,EAAiB5V,KAAK8U,WAW5B9U,KAAK6V,2BAA2Bhe,GAVHQ,IAC3B,GAAIA,IAAY2H,KAAKyG,UAAY3O,OAAOmd,WAAa5c,EAAQ2c,YAAcY,EACzE,OAGF5V,KAAKyV,sBAAsBpd,EAASsd,GACpC,MAAMN,EAAkBvd,OAAOqB,iBAAiBd,GAASe,iBAAiBuc,GAC1Etd,EAAQuY,MAAMkF,YAAYH,EAAgB,GAAEza,EAASuB,OAAOC,WAAW2Y,QAAsB,GAIjG,CAEAI,sBAAsBpd,EAASsd,GAC7B,MAAMI,EAAc1d,EAAQuY,MAAMxX,iBAAiBuc,GAC/CI,GACFzR,EAAYC,iBAAiBlM,EAASsd,EAAeI,EAEzD,CAEAR,wBAAwB1d,EAAU8d,GAahC3V,KAAK6V,2BAA2Bhe,GAZHQ,IAC3B,MAAMuK,EAAQ0B,EAAYY,iBAAiB7M,EAASsd,GAEtC,OAAV/S,GAKJ0B,EAAYG,oBAAoBpM,EAASsd,GACzCtd,EAAQuY,MAAMkF,YAAYH,EAAe/S,IALvCvK,EAAQuY,MAAMoF,eAAeL,EAKgB,GAInD,CAEAE,2BAA2Bhe,EAAUoe,GACnC,GAAIzd,EAAUX,GACZoe,EAASpe,QAIX,IAAK,MAAMqe,KAAO1O,EAAevI,KAAKpH,EAAUmI,KAAKyG,UACnDwP,EAASC,EAEb,EC/FF,MAEM3G,GAAkB,OAClB4G,GAAmB,wBAEnB/Q,GAAU,CACdgR,UAAW,iBACXC,cAAe,KACfpP,YAAY,EACZjO,WAAW,EACXsd,YAAa,QAGTjR,GAAc,CAClB+Q,UAAW,SACXC,cAAe,kBACfpP,WAAY,UACZjO,UAAW,UACXsd,YAAa,oBAOf,MAAMC,WAAiBpR,EACrBU,YAAYL,GACVgB,QACAxG,KAAK0G,QAAU1G,KAAKuF,WAAWC,GAC/BxF,KAAKwW,aAAc,EACnBxW,KAAKyG,SAAW,IAClB,CAGWrB,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEWhK,kBACT,MA3CS,UA4CX,CAGAiV,KAAKpV,GACH,IAAK8E,KAAK0G,QAAQ1N,UAEhB,YADA8C,EAAQZ,GAIV8E,KAAKyW,UAEL,MAAMpe,EAAU2H,KAAK0W,cACjB1W,KAAK0G,QAAQO,YACfzM,EAAOnC,GAGTA,EAAQuB,UAAUqR,IAAIsE,IAEtBvP,KAAK2W,mBAAkB,KACrB7a,EAAQZ,EAAS,GAErB,CAEAmV,KAAKnV,GACE8E,KAAK0G,QAAQ1N,WAKlBgH,KAAK0W,cAAc9c,UAAUgK,OAAO2L,IAEpCvP,KAAK2W,mBAAkB,KACrB3W,KAAK4G,UACL9K,EAAQZ,EAAS,KARjBY,EAAQZ,EAUZ,CAEA0L,UACO5G,KAAKwW,cAIVjW,EAAaC,IAAIR,KAAKyG,SAAU0P,IAEhCnW,KAAKyG,SAAS7C,SACd5D,KAAKwW,aAAc,EACrB,CAGAE,cACE,IAAK1W,KAAKyG,SAAU,CAClB,MAAMmQ,EAAW9d,SAAS+d,cAAc,OACxCD,EAASR,UAAYpW,KAAK0G,QAAQ0P,UAC9BpW,KAAK0G,QAAQO,YACf2P,EAAShd,UAAUqR,IAjGH,QAoGlBjL,KAAKyG,SAAWmQ,CAClB,CAEA,OAAO5W,KAAKyG,QACd,CAEAf,kBAAkBF,GAGhB,OADAA,EAAO8Q,YAAc1d,EAAW4M,EAAO8Q,aAChC9Q,CACT,CAEAiR,UACE,GAAIzW,KAAKwW,YACP,OAGF,MAAMne,EAAU2H,KAAK0W,cACrB1W,KAAK0G,QAAQ4P,YAAYQ,OAAOze,GAEhCkI,EAAac,GAAGhJ,EAAS8d,IAAiB,KACxCra,EAAQkE,KAAK0G,QAAQ2P,cAAc,IAGrCrW,KAAKwW,aAAc,CACrB,CAEAG,kBAAkBzb,GAChBgB,EAAuBhB,EAAU8E,KAAK0W,cAAe1W,KAAK0G,QAAQO,WACpE,EClIF,MAEMJ,GAAa,gBAMbkQ,GAAmB,WAEnB3R,GAAU,CACd4R,WAAW,EACXC,YAAa,MAGT5R,GAAc,CAClB2R,UAAW,UACXC,YAAa,WAOf,MAAMC,WAAkB/R,EACtBU,YAAYL,GACVgB,QACAxG,KAAK0G,QAAU1G,KAAKuF,WAAWC,GAC/BxF,KAAKmX,WAAY,EACjBnX,KAAKoX,qBAAuB,IAC9B,CAGWhS,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEWhK,kBACT,MA1CS,WA2CX,CAGAgc,WACMrX,KAAKmX,YAILnX,KAAK0G,QAAQsQ,WACfhX,KAAK0G,QAAQuQ,YAAYtE,QAG3BpS,EAAaC,IAAI1H,SAAU+N,IAC3BtG,EAAac,GAAGvI,SArDG,wBAqDsBoG,GAASc,KAAKsX,eAAepY,KACtEqB,EAAac,GAAGvI,SArDO,4BAqDsBoG,GAASc,KAAKuX,eAAerY,KAE1Ec,KAAKmX,WAAY,EACnB,CAEAK,aACOxX,KAAKmX,YAIVnX,KAAKmX,WAAY,EACjB5W,EAAaC,IAAI1H,SAAU+N,IAC7B,CAGAyQ,eAAepY,GACb,MAAM+X,YAAEA,GAAgBjX,KAAK0G,QAE7B,GAAIxH,EAAMlC,SAAWlE,UAAYoG,EAAMlC,SAAWia,GAAeA,EAAYpd,SAASqF,EAAMlC,QAC1F,OAGF,MAAMya,EAAWjQ,EAAec,kBAAkB2O,GAE1B,IAApBQ,EAAS5e,OACXoe,EAAYtE,QACH3S,KAAKoX,uBAAyBL,GACvCU,EAASA,EAAS5e,OAAS,GAAG8Z,QAE9B8E,EAAS,GAAG9E,OAEhB,CAEA4E,eAAerY,GApFD,QAqFRA,EAAMyD,MAIV3C,KAAKoX,qBAAuBlY,EAAMwY,SAAWX,GAxFzB,UAyFtB,EC3FF,MAQMY,GAAgB,kBAChBC,GAAc,gBAQdC,GAAkB,aAElBtI,GAAkB,OAClBuI,GAAoB,eAOpB1S,GAAU,CACdwR,UAAU,EACVjE,OAAO,EACP3G,UAAU,GAGN3G,GAAc,CAClBuR,SAAU,mBACVjE,MAAO,UACP3G,SAAU,WAOZ,MAAM+L,WAAcxR,EAClBV,YAAYxN,EAASmN,GACnBgB,MAAMnO,EAASmN,GAEfxF,KAAKgY,QAAUxQ,EAAeG,QAxBV,gBAwBmC3H,KAAKyG,UAC5DzG,KAAKiY,UAAYjY,KAAKkY,sBACtBlY,KAAKmY,WAAanY,KAAKoY,uBACvBpY,KAAKoQ,UAAW,EAChBpQ,KAAK4P,kBAAmB,EACxB5P,KAAKqY,WAAa,IAAIxD,GAEtB7U,KAAK4M,oBACP,CAGWxH,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEWhK,kBACT,MAnES,OAoEX,CAGAuO,OAAO/J,GACL,OAAOG,KAAKoQ,SAAWpQ,KAAKqQ,OAASrQ,KAAKsQ,KAAKzQ,EACjD,CAEAyQ,KAAKzQ,GACCG,KAAKoQ,UAAYpQ,KAAK4P,kBAIRrP,EAAauB,QAAQ9B,KAAKyG,SAAUmR,GAAY,CAChE/X,kBAGYqC,mBAIdlC,KAAKoQ,UAAW,EAChBpQ,KAAK4P,kBAAmB,EAExB5P,KAAKqY,WAAWhI,OAEhBvX,SAAS8B,KAAKhB,UAAUqR,IAAI4M,IAE5B7X,KAAKsY,gBAELtY,KAAKiY,UAAU3H,MAAK,IAAMtQ,KAAKuY,aAAa1Y,KAC9C,CAEAwQ,OACOrQ,KAAKoQ,WAAYpQ,KAAK4P,mBAITrP,EAAauB,QAAQ9B,KAAKyG,SAnG5B,iBAqGFvE,mBAIdlC,KAAKoQ,UAAW,EAChBpQ,KAAK4P,kBAAmB,EACxB5P,KAAKmY,WAAWX,aAEhBxX,KAAKyG,SAAS7M,UAAUgK,OAAO2L,IAE/BvP,KAAKgH,gBAAe,IAAMhH,KAAKwY,cAAcxY,KAAKyG,SAAUzG,KAAKiP,gBACnE,CAEArI,UACE,IAAK,MAAM6R,IAAe,CAAC3gB,OAAQkI,KAAKgY,SACtCzX,EAAaC,IAAIiY,EAxHJ,aA2HfzY,KAAKiY,UAAUrR,UACf5G,KAAKmY,WAAWX,aAChBhR,MAAMI,SACR,CAEA8R,eACE1Y,KAAKsY,eACP,CAGAJ,sBACE,OAAO,IAAI3B,GAAS,CAClBvd,UAAW8H,QAAQd,KAAK0G,QAAQkQ,UAChC3P,WAAYjH,KAAKiP,eAErB,CAEAmJ,uBACE,OAAO,IAAIlB,GAAU,CACnBD,YAAajX,KAAKyG,UAEtB,CAEA8R,aAAa1Y,GAEN/G,SAAS8B,KAAKf,SAASmG,KAAKyG,WAC/B3N,SAAS8B,KAAKkc,OAAO9W,KAAKyG,UAG5BzG,KAAKyG,SAASmK,MAAMoB,QAAU,QAC9BhS,KAAKyG,SAAS/B,gBAAgB,eAC9B1E,KAAKyG,SAASjC,aAAa,cAAc,GACzCxE,KAAKyG,SAASjC,aAAa,OAAQ,UACnCxE,KAAKyG,SAASkS,UAAY,EAE1B,MAAMC,EAAYpR,EAAeG,QAxIT,cAwIsC3H,KAAKgY,SAC/DY,IACFA,EAAUD,UAAY,GAGxBne,EAAOwF,KAAKyG,UAEZzG,KAAKyG,SAAS7M,UAAUqR,IAAIsE,IAa5BvP,KAAKgH,gBAXsB,KACrBhH,KAAK0G,QAAQiM,OACf3S,KAAKmY,WAAWd,WAGlBrX,KAAK4P,kBAAmB,EACxBrP,EAAauB,QAAQ9B,KAAKyG,SArKX,iBAqKkC,CAC/C5G,iBACA,GAGoCG,KAAKgY,QAAShY,KAAKiP,cAC7D,CAEArC,qBACErM,EAAac,GAAGrB,KAAKyG,SA1KM,4BA0K2BvH,IACpD,GArLa,WAqLTA,EAAMyD,IAIV,OAAI3C,KAAK0G,QAAQsF,UACf9M,EAAMsD,sBACNxC,KAAKqQ,aAIPrQ,KAAK6Y,4BAA4B,IAGnCtY,EAAac,GAAGvJ,OA3LE,mBA2LoB,KAChCkI,KAAKoQ,WAAapQ,KAAK4P,kBACzB5P,KAAKsY,eACP,IAGF/X,EAAac,GAAGrB,KAAKyG,SA/LQ,8BA+L2BvH,IAEtDqB,EAAae,IAAItB,KAAKyG,SAlMC,0BAkM8BqS,IAC/C9Y,KAAKyG,WAAavH,EAAMlC,QAAUgD,KAAKyG,WAAaqS,EAAO9b,SAIjC,WAA1BgD,KAAK0G,QAAQkQ,SAKb5W,KAAK0G,QAAQkQ,UACf5W,KAAKqQ,OALLrQ,KAAK6Y,6BAMP,GACA,GAEN,CAEAL,aACExY,KAAKyG,SAASmK,MAAMoB,QAAU,OAC9BhS,KAAKyG,SAASjC,aAAa,eAAe,GAC1CxE,KAAKyG,SAAS/B,gBAAgB,cAC9B1E,KAAKyG,SAAS/B,gBAAgB,QAC9B1E,KAAK4P,kBAAmB,EAExB5P,KAAKiY,UAAU5H,MAAK,KAClBvX,SAAS8B,KAAKhB,UAAUgK,OAAOiU,IAC/B7X,KAAK+Y,oBACL/Y,KAAKqY,WAAW/C,QAChB/U,EAAauB,QAAQ9B,KAAKyG,SAAUkR,GAAa,GAErD,CAEA1I,cACE,OAAOjP,KAAKyG,SAAS7M,UAAUC,SA7NX,OA8NtB,CAEAgf,6BAEE,GADkBtY,EAAauB,QAAQ9B,KAAKyG,SA5OlB,0BA6OZvE,iBACZ,OAGF,MAAM8W,EAAqBhZ,KAAKyG,SAASwS,aAAengB,SAASoB,gBAAgBgf,aAC3EC,EAAmBnZ,KAAKyG,SAASmK,MAAMwI,UAEpB,WAArBD,GAAiCnZ,KAAKyG,SAAS7M,UAAUC,SAASie,MAIjEkB,IACHhZ,KAAKyG,SAASmK,MAAMwI,UAAY,UAGlCpZ,KAAKyG,SAAS7M,UAAUqR,IAAI6M,IAC5B9X,KAAKgH,gBAAe,KAClBhH,KAAKyG,SAAS7M,UAAUgK,OAAOkU,IAC/B9X,KAAKgH,gBAAe,KAClBhH,KAAKyG,SAASmK,MAAMwI,UAAYD,CAAgB,GAC/CnZ,KAAKgY,QAAQ,GACfhY,KAAKgY,SAERhY,KAAKyG,SAASkM,QAChB,CAMA2F,gBACE,MAAMU,EAAqBhZ,KAAKyG,SAASwS,aAAengB,SAASoB,gBAAgBgf,aAC3EtD,EAAiB5V,KAAKqY,WAAWvD,WACjCuE,EAAoBzD,EAAiB,EAE3C,GAAIyD,IAAsBL,EAAoB,CAC5C,MAAMjT,EAAWjL,IAAU,cAAgB,eAC3CkF,KAAKyG,SAASmK,MAAM7K,GAAa,GAAE6P,KACrC,CAEA,IAAKyD,GAAqBL,EAAoB,CAC5C,MAAMjT,EAAWjL,IAAU,eAAiB,cAC5CkF,KAAKyG,SAASmK,MAAM7K,GAAa,GAAE6P,KACrC,CACF,CAEAmD,oBACE/Y,KAAKyG,SAASmK,MAAM0I,YAAc,GAClCtZ,KAAKyG,SAASmK,MAAM2I,aAAe,EACrC,CAGArS,uBAAuB1B,EAAQ3F,GAC7B,OAAOG,KAAKuJ,MAAK,WACf,MAAMC,EAAOuO,GAAM5O,oBAAoBnJ,KAAMwF,GAE7C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBgE,EAAKhE,GACd,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,GAAQ3F,EANb,CAOF,GACF,EAOFU,EAAac,GAAGvI,SA9Sc,0BAUD,4BAoSyC,SAAUoG,GAC9E,MAAMlC,EAASwK,EAAeoB,uBAAuB5I,MAEjD,CAAC,IAAK,QAAQoB,SAASpB,KAAKkJ,UAC9BhK,EAAMsD,iBAGRjC,EAAae,IAAItE,EAAQ4a,IAAY4B,IAC/BA,EAAUtX,kBAKd3B,EAAae,IAAItE,EAAQ2a,IAAc,KACjC3e,EAAUgH,OACZA,KAAK2S,OACP,GACA,IAIJ,MAAM8G,EAAcjS,EAAeG,QA5Tf,eA6ThB8R,GACF1B,GAAM5Q,YAAYsS,GAAapJ,OAGpB0H,GAAM5O,oBAAoBnM,GAElC4M,OAAO5J,KACd,IAEA8I,EAAqBiP,IAMrB/c,EAAmB+c,IC9VnB,MAOMxI,GAAkB,OAClBmK,GAAqB,UACrBC,GAAoB,SAEpBC,GAAgB,kBAKhBC,GAAwB,6BACxBlC,GAAgB,sBAOhBvS,GAAU,CACdwR,UAAU,EACV5K,UAAU,EACV8N,QAAQ,GAGJzU,GAAc,CAClBuR,SAAU,mBACV5K,SAAU,UACV8N,OAAQ,WAOV,MAAMC,WAAkBxT,EACtBV,YAAYxN,EAASmN,GACnBgB,MAAMnO,EAASmN,GAEfxF,KAAKoQ,UAAW,EAChBpQ,KAAKiY,UAAYjY,KAAKkY,sBACtBlY,KAAKmY,WAAanY,KAAKoY,uBACvBpY,KAAK4M,oBACP,CAGWxH,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEWhK,kBACT,MA5DS,WA6DX,CAGAuO,OAAO/J,GACL,OAAOG,KAAKoQ,SAAWpQ,KAAKqQ,OAASrQ,KAAKsQ,KAAKzQ,EACjD,CAEAyQ,KAAKzQ,GACCG,KAAKoQ,UAIS7P,EAAauB,QAAQ9B,KAAKyG,SA5D5B,oBA4DkD,CAAE5G,kBAEtDqC,mBAIdlC,KAAKoQ,UAAW,EAChBpQ,KAAKiY,UAAU3H,OAEVtQ,KAAK0G,QAAQoT,SAChB,IAAIjF,IAAkBxE,OAGxBrQ,KAAKyG,SAASjC,aAAa,cAAc,GACzCxE,KAAKyG,SAASjC,aAAa,OAAQ,UACnCxE,KAAKyG,SAAS7M,UAAUqR,IAAIyO,IAY5B1Z,KAAKgH,gBAVoB,KAClBhH,KAAK0G,QAAQoT,SAAU9Z,KAAK0G,QAAQkQ,UACvC5W,KAAKmY,WAAWd,WAGlBrX,KAAKyG,SAAS7M,UAAUqR,IAAIsE,IAC5BvP,KAAKyG,SAAS7M,UAAUgK,OAAO8V,IAC/BnZ,EAAauB,QAAQ9B,KAAKyG,SAnFX,qBAmFkC,CAAE5G,iBAAgB,GAG/BG,KAAKyG,UAAU,GACvD,CAEA4J,OACOrQ,KAAKoQ,WAIQ7P,EAAauB,QAAQ9B,KAAKyG,SA7F5B,qBA+FFvE,mBAIdlC,KAAKmY,WAAWX,aAChBxX,KAAKyG,SAASuT,OACdha,KAAKoQ,UAAW,EAChBpQ,KAAKyG,SAAS7M,UAAUqR,IAAI0O,IAC5B3Z,KAAKiY,UAAU5H,OAcfrQ,KAAKgH,gBAZoB,KACvBhH,KAAKyG,SAAS7M,UAAUgK,OAAO2L,GAAiBoK,IAChD3Z,KAAKyG,SAAS/B,gBAAgB,cAC9B1E,KAAKyG,SAAS/B,gBAAgB,QAEzB1E,KAAK0G,QAAQoT,SAChB,IAAIjF,IAAkBS,QAGxB/U,EAAauB,QAAQ9B,KAAKyG,SAAUkR,GAAa,GAGb3X,KAAKyG,UAAU,IACvD,CAEAG,UACE5G,KAAKiY,UAAUrR,UACf5G,KAAKmY,WAAWX,aAChBhR,MAAMI,SACR,CAGAsR,sBACE,MAUMlf,EAAY8H,QAAQd,KAAK0G,QAAQkQ,UAEvC,OAAO,IAAIL,GAAS,CAClBH,UAlJsB,qBAmJtBpd,YACAiO,YAAY,EACZqP,YAAatW,KAAKyG,SAASjN,WAC3B6c,cAAerd,EAjBK,KACU,WAA1BgH,KAAK0G,QAAQkQ,SAKjB5W,KAAKqQ,OAJH9P,EAAauB,QAAQ9B,KAAKyG,SAAUoT,GAI3B,EAWgC,MAE/C,CAEAzB,uBACE,OAAO,IAAIlB,GAAU,CACnBD,YAAajX,KAAKyG,UAEtB,CAEAmG,qBACErM,EAAac,GAAGrB,KAAKyG,SAvJM,gCAuJ2BvH,IAtKvC,WAuKTA,EAAMyD,MAIL3C,KAAK0G,QAAQsF,SAKlBhM,KAAKqQ,OAJH9P,EAAauB,QAAQ9B,KAAKyG,SAAUoT,IAI3B,GAEf,CAGA3S,uBAAuB1B,GACrB,OAAOxF,KAAKuJ,MAAK,WACf,MAAMC,EAAOuQ,GAAU5Q,oBAAoBnJ,KAAMwF,GAEjD,GAAsB,iBAAXA,EAAX,CAIA,QAAqBiE,IAAjBD,EAAKhE,IAAyBA,EAAO/D,WAAW,MAAmB,gBAAX+D,EAC1D,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,GAAQxF,KANb,CAOF,GACF,EAOFO,EAAac,GAAGvI,SA5Lc,8BAGD,gCAyLyC,SAAUoG,GAC9E,MAAMlC,EAASwK,EAAeoB,uBAAuB5I,MAMrD,GAJI,CAAC,IAAK,QAAQoB,SAASpB,KAAKkJ,UAC9BhK,EAAMsD,iBAGJ/I,EAAWuG,MACb,OAGFO,EAAae,IAAItE,EAAQ2a,IAAc,KAEjC3e,EAAUgH,OACZA,KAAK2S,OACP,IAIF,MAAM8G,EAAcjS,EAAeG,QAAQiS,IACvCH,GAAeA,IAAgBzc,GACjC+c,GAAU5S,YAAYsS,GAAapJ,OAGxB0J,GAAU5Q,oBAAoBnM,GACtC4M,OAAO5J,KACd,IAEAO,EAAac,GAAGvJ,OAvOa,8BAuOgB,KAC3C,IAAK,MAAMD,KAAY2P,EAAevI,KAAK2a,IACzCG,GAAU5Q,oBAAoBtR,GAAUyY,MAC1C,IAGF/P,EAAac,GAAGvJ,OA/NM,uBA+NgB,KACpC,IAAK,MAAMO,KAAWmP,EAAevI,KAAK,gDACG,UAAvC9F,iBAAiBd,GAAS4hB,UAC5BF,GAAU5Q,oBAAoB9Q,GAASgY,MAE3C,IAGFvH,EAAqBiR,IAMrB/e,EAAmB+e,IChRnB,MAAMG,GAAgB,IAAI3b,IAAI,CAC5B,aACA,OACA,OACA,WACA,WACA,SACA,MACA,eAUI4b,GAAmB,iEAOnBC,GAAmB,qIAEnBC,GAAmB,CAACC,EAAWC,KACnC,MAAMC,EAAgBF,EAAUG,SAASpW,cAEzC,OAAIkW,EAAqBnZ,SAASoZ,IAC5BN,GAAcza,IAAI+a,IACb1Z,QAAQqZ,GAAiB/T,KAAKkU,EAAUI,YAAcN,GAAiBhU,KAAKkU,EAAUI,YAO1FH,EAAqBxV,QAAO4V,GAAkBA,aAA0BxU,SAC5EyU,MAAKC,GAASA,EAAMzU,KAAKoU,IAAe,EAGhCM,GAAmB,CAE9B,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAlCP,kBAmC7BC,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BC,KAAM,GACNC,EAAG,GACHC,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,EAAG,GACH/N,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChDgO,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,IC/DAtX,GAAU,CACduX,UAAW7B,GACX8B,QAAS,GACTC,WAAY,GACZC,MAAM,EACNC,UAAU,EACVC,WAAY,KACZC,SAAU,eAGN5X,GAAc,CAClBsX,UAAW,SACXC,QAAS,SACTC,WAAY,oBACZC,KAAM,UACNC,SAAU,UACVC,WAAY,kBACZC,SAAU,UAGNC,GAAqB,CACzBC,MAAO,iCACPtlB,SAAU,oBAOZ,MAAMulB,WAAwBjY,EAC5BU,YAAYL,GACVgB,QACAxG,KAAK0G,QAAU1G,KAAKuF,WAAWC,EACjC,CAGWJ,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEWhK,kBACT,MA/CS,iBAgDX,CAGAgiB,aACE,OAAOte,OAAOC,OAAOgB,KAAK0G,QAAQkW,SAC/BpU,KAAIhD,GAAUxF,KAAKsd,yBAAyB9X,KAC5CT,OAAOjE,QACZ,CAEAyc,aACE,OAAOvd,KAAKqd,aAAaxkB,OAAS,CACpC,CAEA2kB,cAAcZ,GAGZ,OAFA5c,KAAKyd,cAAcb,GACnB5c,KAAK0G,QAAQkW,QAAU,IAAK5c,KAAK0G,QAAQkW,WAAYA,GAC9C5c,IACT,CAEA0d,SACE,MAAMC,EAAkB7kB,SAAS+d,cAAc,OAC/C8G,EAAgBC,UAAY5d,KAAK6d,eAAe7d,KAAK0G,QAAQuW,UAE7D,IAAK,MAAOplB,EAAUimB,KAAS/e,OAAOoC,QAAQnB,KAAK0G,QAAQkW,SACzD5c,KAAK+d,YAAYJ,EAAiBG,EAAMjmB,GAG1C,MAAMolB,EAAWU,EAAgB/V,SAAS,GACpCiV,EAAa7c,KAAKsd,yBAAyBtd,KAAK0G,QAAQmW,YAM9D,OAJIA,GACFI,EAASrjB,UAAUqR,OAAO4R,EAAWjgB,MAAM,MAGtCqgB,CACT,CAGAtX,iBAAiBH,GACfgB,MAAMb,iBAAiBH,GACvBxF,KAAKyd,cAAcjY,EAAOoX,QAC5B,CAEAa,cAAcO,GACZ,IAAK,MAAOnmB,EAAU+kB,KAAY7d,OAAOoC,QAAQ6c,GAC/CxX,MAAMb,iBAAiB,CAAE9N,WAAUslB,MAAOP,GAAWM,GAEzD,CAEAa,YAAYd,EAAUL,EAAS/kB,GAC7B,MAAMomB,EAAkBzW,EAAeG,QAAQ9P,EAAUolB,GAEpDgB,KAILrB,EAAU5c,KAAKsd,yBAAyBV,IAOpCpkB,EAAUokB,GACZ5c,KAAKke,sBAAsBtlB,EAAWgkB,GAAUqB,GAI9Cje,KAAK0G,QAAQoW,KACfmB,EAAgBL,UAAY5d,KAAK6d,eAAejB,GAIlDqB,EAAgBE,YAAcvB,EAd5BqB,EAAgBra,SAepB,CAEAia,eAAeG,GACb,OAAOhe,KAAK0G,QAAQqW,SDzDjB,SAAsBqB,EAAYzB,EAAW0B,GAClD,IAAKD,EAAWvlB,OACd,OAAOulB,EAGT,GAAIC,GAAgD,mBAArBA,EAC7B,OAAOA,EAAiBD,GAG1B,MACME,GADY,IAAIxmB,OAAOymB,WACKC,gBAAgBJ,EAAY,aACxD3G,EAAW,GAAGhQ,UAAU6W,EAAgB1jB,KAAKwF,iBAAiB,MAEpE,IAAK,MAAM/H,KAAWof,EAAU,CAC9B,MAAMgH,EAAcpmB,EAAQoiB,SAASpW,cAErC,IAAKtF,OAAO4C,KAAKgb,GAAWvb,SAASqd,GAAc,CACjDpmB,EAAQuL,SAER,QACF,CAEA,MAAM8a,EAAgB,GAAGjX,UAAUpP,EAAQuM,YACrC+Z,EAAoB,GAAGlX,OAAOkV,EAAU,MAAQ,GAAIA,EAAU8B,IAAgB,IAEpF,IAAK,MAAMnE,KAAaoE,EACjBrE,GAAiBC,EAAWqE,IAC/BtmB,EAAQqM,gBAAgB4V,EAAUG,SAGxC,CAEA,OAAO6D,EAAgB1jB,KAAKgjB,SAC9B,CCwBmCgB,CAAaZ,EAAKhe,KAAK0G,QAAQiW,UAAW3c,KAAK0G,QAAQsW,YAAcgB,CACtG,CAEAV,yBAAyBU,GACvB,OAAOliB,EAAQkiB,EAAK,CAAChe,MACvB,CAEAke,sBAAsB7lB,EAAS4lB,GAC7B,GAAIje,KAAK0G,QAAQoW,KAGf,OAFAmB,EAAgBL,UAAY,QAC5BK,EAAgBnH,OAAOze,GAIzB4lB,EAAgBE,YAAc9lB,EAAQ8lB,WACxC,ECzIF,MACMU,GAAwB,IAAItgB,IAAI,CAAC,WAAY,YAAa,eAE1DugB,GAAkB,OAElBvP,GAAkB,OAGlBwP,GAAkB,SAElBC,GAAmB,gBAEnBC,GAAgB,QAChBC,GAAgB,QAehBC,GAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAOxkB,IAAU,OAAS,QAC1BykB,OAAQ,SACRC,KAAM1kB,IAAU,QAAU,QAGtBsK,GAAU,CACduX,UAAW7B,GACX2E,WAAW,EACX1N,SAAU,kBACV2N,WAAW,EACXC,YAAa,GACbC,MAAO,EACPC,mBAAoB,CAAC,MAAO,QAAS,SAAU,QAC/C/C,MAAM,EACN7K,OAAQ,CAAC,EAAG,GACZwB,UAAW,MACXvB,aAAc,KACd6K,UAAU,EACVC,WAAY,KACZnlB,UAAU,EACVolB,SAAU,+GAIV6C,MAAO,GACPhe,QAAS,eAGLuD,GAAc,CAClBsX,UAAW,SACX8C,UAAW,UACX1N,SAAU,mBACV2N,UAAW,2BACXC,YAAa,oBACbC,MAAO,kBACPC,mBAAoB,QACpB/C,KAAM,UACN7K,OAAQ,0BACRwB,UAAW,oBACXvB,aAAc,yBACd6K,SAAU,UACVC,WAAY,kBACZnlB,SAAU,mBACVolB,SAAU,SACV6C,MAAO,4BACPhe,QAAS,UAOX,MAAMie,WAAgBxZ,EACpBV,YAAYxN,EAASmN,GACnB,QAAsB,IAAXuN,EACT,MAAM,IAAI1M,UAAU,+DAGtBG,MAAMnO,EAASmN,GAGfxF,KAAKggB,YAAa,EAClBhgB,KAAKigB,SAAW,EAChBjgB,KAAKkgB,WAAa,KAClBlgB,KAAKmgB,eAAiB,GACtBngB,KAAKqS,QAAU,KACfrS,KAAKogB,iBAAmB,KACxBpgB,KAAKqgB,YAAc,KAGnBrgB,KAAKsgB,IAAM,KAEXtgB,KAAKugB,gBAEAvgB,KAAK0G,QAAQ7O,UAChBmI,KAAKwgB,WAET,CAGWpb,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEWhK,kBACT,MAxHS,SAyHX,CAGAolB,SACEzgB,KAAKggB,YAAa,CACpB,CAEAU,UACE1gB,KAAKggB,YAAa,CACpB,CAEAW,gBACE3gB,KAAKggB,YAAchgB,KAAKggB,UAC1B,CAEApW,SACO5J,KAAKggB,aAIVhgB,KAAKmgB,eAAeS,OAAS5gB,KAAKmgB,eAAeS,MAC7C5gB,KAAKoQ,WACPpQ,KAAK6gB,SAIP7gB,KAAK8gB,SACP,CAEAla,UACEsH,aAAalO,KAAKigB,UAElB1f,EAAaC,IAAIR,KAAKyG,SAASnN,QAAQylB,IAAiBC,GAAkBhf,KAAK+gB,mBAE3E/gB,KAAKyG,SAASzM,aAAa,2BAC7BgG,KAAKyG,SAASjC,aAAa,QAASxE,KAAKyG,SAASzM,aAAa,2BAGjEgG,KAAKghB,iBACLxa,MAAMI,SACR,CAEA0J,OACE,GAAoC,SAAhCtQ,KAAKyG,SAASmK,MAAMoB,QACtB,MAAM,IAAI1M,MAAM,uCAGlB,IAAMtF,KAAKihB,mBAAoBjhB,KAAKggB,WAClC,OAGF,MAAMxG,EAAYjZ,EAAauB,QAAQ9B,KAAKyG,SAAUzG,KAAK6F,YAAY+I,UAzJxD,SA2JTsS,GADajnB,EAAe+F,KAAKyG,WACLzG,KAAKyG,SAAS0a,cAAcjnB,iBAAiBL,SAASmG,KAAKyG,UAE7F,GAAI+S,EAAUtX,mBAAqBgf,EACjC,OAIFlhB,KAAKghB,iBAEL,MAAMV,EAAMtgB,KAAKohB,iBAEjBphB,KAAKyG,SAASjC,aAAa,mBAAoB8b,EAAItmB,aAAa,OAEhE,MAAM0lB,UAAEA,GAAc1f,KAAK0G,QAe3B,GAbK1G,KAAKyG,SAAS0a,cAAcjnB,gBAAgBL,SAASmG,KAAKsgB,OAC7DZ,EAAU5I,OAAOwJ,GACjB/f,EAAauB,QAAQ9B,KAAKyG,SAAUzG,KAAK6F,YAAY+I,UA1KpC,cA6KnB5O,KAAKqS,QAAUrS,KAAK0S,cAAc4N,GAElCA,EAAI1mB,UAAUqR,IAAIsE,IAMd,iBAAkBzW,SAASoB,gBAC7B,IAAK,MAAM7B,IAAW,GAAGoP,UAAU3O,SAAS8B,KAAKgN,UAC/CrH,EAAac,GAAGhJ,EAAS,YAAakC,GAc1CyF,KAAKgH,gBAVY,KACfzG,EAAauB,QAAQ9B,KAAKyG,SAAUzG,KAAK6F,YAAY+I,UA7LvC,WA+LU,IAApB5O,KAAKkgB,YACPlgB,KAAK6gB,SAGP7gB,KAAKkgB,YAAa,CAAK,GAGKlgB,KAAKsgB,IAAKtgB,KAAKiP,cAC/C,CAEAoB,OACE,GAAKrQ,KAAKoQ,aAIQ7P,EAAauB,QAAQ9B,KAAKyG,SAAUzG,KAAK6F,YAAY+I,UAjNxD,SAkND1M,iBAAd,CASA,GALYlC,KAAKohB,iBACbxnB,UAAUgK,OAAO2L,IAIjB,iBAAkBzW,SAASoB,gBAC7B,IAAK,MAAM7B,IAAW,GAAGoP,UAAU3O,SAAS8B,KAAKgN,UAC/CrH,EAAaC,IAAInI,EAAS,YAAakC,GAI3CyF,KAAKmgB,eAA4B,OAAI,EACrCngB,KAAKmgB,eAA4B,OAAI,EACrCngB,KAAKmgB,eAA4B,OAAI,EACrCngB,KAAKkgB,WAAa,KAelBlgB,KAAKgH,gBAbY,KACXhH,KAAKqhB,yBAIJrhB,KAAKkgB,YACRlgB,KAAKghB,iBAGPhhB,KAAKyG,SAAS/B,gBAAgB,oBAC9BnE,EAAauB,QAAQ9B,KAAKyG,SAAUzG,KAAK6F,YAAY+I,UA/OtC,WA+O8D,GAGjD5O,KAAKsgB,IAAKtgB,KAAKiP,cA/B7C,CAgCF,CAEA6D,SACM9S,KAAKqS,SACPrS,KAAKqS,QAAQS,QAEjB,CAGAmO,iBACE,OAAOngB,QAAQd,KAAKshB,YACtB,CAEAF,iBAKE,OAJKphB,KAAKsgB,MACRtgB,KAAKsgB,IAAMtgB,KAAKuhB,kBAAkBvhB,KAAKqgB,aAAergB,KAAKwhB,2BAGtDxhB,KAAKsgB,GACd,CAEAiB,kBAAkB3E,GAChB,MAAM0D,EAAMtgB,KAAKyhB,oBAAoB7E,GAASc,SAG9C,IAAK4C,EACH,OAAO,KAGTA,EAAI1mB,UAAUgK,OAAOkb,GAAiBvP,IAEtC+Q,EAAI1mB,UAAUqR,IAAK,MAAKjL,KAAK6F,YAAYxK,aAEzC,MAAMqmB,ErBnRKC,KACb,GACEA,GAAUhkB,KAAKikB,MAjCH,IAiCSjkB,KAAKkkB,gBACnB/oB,SAASgpB,eAAeH,IAEjC,OAAOA,CAAM,EqB8QGI,CAAO/hB,KAAK6F,YAAYxK,MAAM0I,WAQ5C,OANAuc,EAAI9b,aAAa,KAAMkd,GAEnB1hB,KAAKiP,eACPqR,EAAI1mB,UAAUqR,IAAI6T,IAGbwB,CACT,CAEA0B,WAAWpF,GACT5c,KAAKqgB,YAAczD,EACf5c,KAAKoQ,aACPpQ,KAAKghB,iBACLhhB,KAAKsQ,OAET,CAEAmR,oBAAoB7E,GAalB,OAZI5c,KAAKogB,iBACPpgB,KAAKogB,iBAAiB5C,cAAcZ,GAEpC5c,KAAKogB,iBAAmB,IAAIhD,GAAgB,IACvCpd,KAAK0G,QAGRkW,UACAC,WAAY7c,KAAKsd,yBAAyBtd,KAAK0G,QAAQiZ,eAIpD3f,KAAKogB,gBACd,CAEAoB,yBACE,MAAO,CACL,iBAA0BxhB,KAAKshB,YAEnC,CAEAA,YACE,OAAOthB,KAAKsd,yBAAyBtd,KAAK0G,QAAQoZ,QAAU9f,KAAKyG,SAASzM,aAAa,yBACzF,CAGAioB,6BAA6B/iB,GAC3B,OAAOc,KAAK6F,YAAYsD,oBAAoBjK,EAAMY,eAAgBE,KAAKkiB,qBACzE,CAEAjT,cACE,OAAOjP,KAAK0G,QAAQ+Y,WAAczf,KAAKsgB,KAAOtgB,KAAKsgB,IAAI1mB,UAAUC,SAASilB,GAC5E,CAEA1O,WACE,OAAOpQ,KAAKsgB,KAAOtgB,KAAKsgB,IAAI1mB,UAAUC,SAAS0V,GACjD,CAEAmD,cAAc4N,GACZ,MAAM7M,EAAY3X,EAAQkE,KAAK0G,QAAQ+M,UAAW,CAACzT,KAAMsgB,EAAKtgB,KAAKyG,WAC7D0b,EAAahD,GAAc1L,EAAUnN,eAC3C,OAAOyM,EAAOG,aAAalT,KAAKyG,SAAU6Z,EAAKtgB,KAAKiT,iBAAiBkP,GACvE,CAEA7O,aACE,MAAMrB,OAAEA,GAAWjS,KAAK0G,QAExB,MAAsB,iBAAXuL,EACFA,EAAOrV,MAAM,KAAK4L,KAAI5F,GAASnG,OAAO8R,SAAS3L,EAAO,MAGzC,mBAAXqP,EACFsB,GAActB,EAAOsB,EAAYvT,KAAKyG,UAGxCwL,CACT,CAEAqL,yBAAyBU,GACvB,OAAOliB,EAAQkiB,EAAK,CAAChe,KAAKyG,UAC5B,CAEAwM,iBAAiBkP,GACf,MAAM3O,EAAwB,CAC5BC,UAAW0O,EACXzO,UAAW,CACT,CACEtY,KAAM,OACNuY,QAAS,CACPkM,mBAAoB7f,KAAK0G,QAAQmZ,qBAGrC,CACEzkB,KAAM,SACNuY,QAAS,CACP1B,OAAQjS,KAAKsT,eAGjB,CACElY,KAAM,kBACNuY,QAAS,CACP5B,SAAU/R,KAAK0G,QAAQqL,WAG3B,CACE3W,KAAM,QACNuY,QAAS,CACPtb,QAAU,IAAG2H,KAAK6F,YAAYxK,eAGlC,CACED,KAAM,kBACNwY,SAAS,EACTwO,MAAO,aACP7mB,GAAIiO,IAGFxJ,KAAKohB,iBAAiB5c,aAAa,wBAAyBgF,EAAK6Y,MAAM5O,UAAU,KAMzF,MAAO,IACFD,KACA1X,EAAQkE,KAAK0G,QAAQwL,aAAc,CAACsB,IAE3C,CAEA+M,gBACE,MAAM+B,EAAWtiB,KAAK0G,QAAQ5E,QAAQlF,MAAM,KAE5C,IAAK,MAAMkF,KAAWwgB,EACpB,GAAgB,UAAZxgB,EACFvB,EAAac,GAAGrB,KAAKyG,SAAUzG,KAAK6F,YAAY+I,UAtZpC,SAsZ4D5O,KAAK0G,QAAQ7O,UAAUqH,IAC7Ec,KAAKiiB,6BAA6B/iB,GAC1C0K,QAAQ,SAEb,GAjaU,WAiaN9H,EAA4B,CACrC,MAAMygB,EAAUzgB,IAAYmd,GAC1Bjf,KAAK6F,YAAY+I,UAzZF,cA0Zf5O,KAAK6F,YAAY+I,UA5ZL,WA6ZR4T,EAAW1gB,IAAYmd,GAC3Bjf,KAAK6F,YAAY+I,UA3ZF,cA4Zf5O,KAAK6F,YAAY+I,UA9ZJ,YAgafrO,EAAac,GAAGrB,KAAKyG,SAAU8b,EAASviB,KAAK0G,QAAQ7O,UAAUqH,IAC7D,MAAM6U,EAAU/T,KAAKiiB,6BAA6B/iB,GAClD6U,EAAQoM,eAA8B,YAAfjhB,EAAMuB,KAAqBye,GAAgBD,KAAiB,EACnFlL,EAAQ+M,QAAQ,IAElBvgB,EAAac,GAAGrB,KAAKyG,SAAU+b,EAAUxiB,KAAK0G,QAAQ7O,UAAUqH,IAC9D,MAAM6U,EAAU/T,KAAKiiB,6BAA6B/iB,GAClD6U,EAAQoM,eAA8B,aAAfjhB,EAAMuB,KAAsBye,GAAgBD,IACjElL,EAAQtN,SAAS5M,SAASqF,EAAMW,eAElCkU,EAAQ8M,QAAQ,GAEpB,CAGF7gB,KAAK+gB,kBAAoB,KACnB/gB,KAAKyG,UACPzG,KAAKqQ,MACP,EAGF9P,EAAac,GAAGrB,KAAKyG,SAASnN,QAAQylB,IAAiBC,GAAkBhf,KAAK+gB,kBAChF,CAEAP,YACE,MAAMV,EAAQ9f,KAAKyG,SAASzM,aAAa,SAEpC8lB,IAIA9f,KAAKyG,SAASzM,aAAa,eAAkBgG,KAAKyG,SAAS0X,YAAY5W,QAC1EvH,KAAKyG,SAASjC,aAAa,aAAcsb,GAG3C9f,KAAKyG,SAASjC,aAAa,yBAA0Bsb,GACrD9f,KAAKyG,SAAS/B,gBAAgB,SAChC,CAEAoc,SACM9gB,KAAKoQ,YAAcpQ,KAAKkgB,WAC1BlgB,KAAKkgB,YAAa,GAIpBlgB,KAAKkgB,YAAa,EAElBlgB,KAAKyiB,aAAY,KACXziB,KAAKkgB,YACPlgB,KAAKsQ,MACP,GACCtQ,KAAK0G,QAAQkZ,MAAMtP,MACxB,CAEAuQ,SACM7gB,KAAKqhB,yBAITrhB,KAAKkgB,YAAa,EAElBlgB,KAAKyiB,aAAY,KACVziB,KAAKkgB,YACRlgB,KAAKqQ,MACP,GACCrQ,KAAK0G,QAAQkZ,MAAMvP,MACxB,CAEAoS,YAAY1lB,EAAS2lB,GACnBxU,aAAalO,KAAKigB,UAClBjgB,KAAKigB,SAAW/iB,WAAWH,EAAS2lB,EACtC,CAEArB,uBACE,OAAOtiB,OAAOC,OAAOgB,KAAKmgB,gBAAgB/e,UAAS,EACrD,CAEAmE,WAAWC,GACT,MAAMmd,EAAiBre,EAAYK,kBAAkB3E,KAAKyG,UAE1D,IAAK,MAAMmc,KAAiB7jB,OAAO4C,KAAKghB,GAClC9D,GAAsBpf,IAAImjB,WACrBD,EAAeC,GAW1B,OAPApd,EAAS,IACJmd,KACmB,iBAAXnd,GAAuBA,EAASA,EAAS,IAEtDA,EAASxF,KAAKyF,gBAAgBD,GAC9BA,EAASxF,KAAK0F,kBAAkBF,GAChCxF,KAAK2F,iBAAiBH,GACfA,CACT,CAEAE,kBAAkBF,GAkBhB,OAjBAA,EAAOka,WAAiC,IAArBla,EAAOka,UAAsB5mB,SAAS8B,KAAOhC,EAAW4M,EAAOka,WAEtD,iBAAjBla,EAAOoa,QAChBpa,EAAOoa,MAAQ,CACbtP,KAAM9K,EAAOoa,MACbvP,KAAM7K,EAAOoa,QAIW,iBAAjBpa,EAAOsa,QAChBta,EAAOsa,MAAQta,EAAOsa,MAAM/b,YAGA,iBAAnByB,EAAOoX,UAChBpX,EAAOoX,QAAUpX,EAAOoX,QAAQ7Y,YAG3ByB,CACT,CAEA0c,qBACE,MAAM1c,EAAS,GAEf,IAAK,MAAO7C,EAAKC,KAAU7D,OAAOoC,QAAQnB,KAAK0G,SACzC1G,KAAK6F,YAAYT,QAAQzC,KAASC,IACpC4C,EAAO7C,GAAOC,GAUlB,OANA4C,EAAO3N,UAAW,EAClB2N,EAAO1D,QAAU,SAKV0D,CACT,CAEAwb,iBACMhhB,KAAKqS,UACPrS,KAAKqS,QAAQQ,UACb7S,KAAKqS,QAAU,MAGbrS,KAAKsgB,MACPtgB,KAAKsgB,IAAI1c,SACT5D,KAAKsgB,IAAM,KAEf,CAGApZ,uBAAuB1B,GACrB,OAAOxF,KAAKuJ,MAAK,WACf,MAAMC,EAAOuW,GAAQ5W,oBAAoBnJ,KAAMwF,GAE/C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBgE,EAAKhE,GACd,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,IANL,CAOF,GACF,EAOFxK,EAAmB+kB,ICtmBnB,MAKM3a,GAAU,IACX2a,GAAQ3a,QACXwX,QAAS,GACT3K,OAAQ,CAAC,EAAG,GACZwB,UAAW,QACXwJ,SAAU,8IAKVnb,QAAS,SAGLuD,GAAc,IACf0a,GAAQ1a,YACXuX,QAAS,kCAOX,MAAMiG,WAAgB9C,GAET3a,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEWhK,kBACT,MAtCS,SAuCX,CAGA4lB,iBACE,OAAOjhB,KAAKshB,aAAethB,KAAK8iB,aAClC,CAGAtB,yBACE,MAAO,CACL,kBAAkBxhB,KAAKshB,YACvB,gBAAoBthB,KAAK8iB,cAE7B,CAEAA,cACE,OAAO9iB,KAAKsd,yBAAyBtd,KAAK0G,QAAQkW,QACpD,CAGA1V,uBAAuB1B,GACrB,OAAOxF,KAAKuJ,MAAK,WACf,MAAMC,EAAOqZ,GAAQ1Z,oBAAoBnJ,KAAMwF,GAE/C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBgE,EAAKhE,GACd,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,IANL,CAOF,GACF,EAOFxK,EAAmB6nB,IC9EnB,MAMME,GAAe,qBAIfpX,GAAoB,SAGpBqX,GAAwB,SASxB5d,GAAU,CACd6M,OAAQ,KACRgR,WAAY,eACZC,cAAc,EACdlmB,OAAQ,KACRmmB,UAAW,CAAC,GAAK,GAAK,IAGlB9d,GAAc,CAClB4M,OAAQ,gBACRgR,WAAY,SACZC,aAAc,UACdlmB,OAAQ,UACRmmB,UAAW,SAOb,MAAMC,WAAkB7c,EACtBV,YAAYxN,EAASmN,GACnBgB,MAAMnO,EAASmN,GAGfxF,KAAKqjB,aAAe,IAAIngB,IACxBlD,KAAKsjB,oBAAsB,IAAIpgB,IAC/BlD,KAAKujB,aAA6D,YAA9CpqB,iBAAiB6G,KAAKyG,UAAU2S,UAA0B,KAAOpZ,KAAKyG,SAC1FzG,KAAKwjB,cAAgB,KACrBxjB,KAAKyjB,UAAY,KACjBzjB,KAAK0jB,oBAAsB,CACzBC,gBAAiB,EACjBC,gBAAiB,GAEnB5jB,KAAK6jB,SACP,CAGWze,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEWhK,kBACT,MArES,WAsEX,CAGAwoB,UACE7jB,KAAK8jB,mCACL9jB,KAAK+jB,2BAED/jB,KAAKyjB,UACPzjB,KAAKyjB,UAAUO,aAEfhkB,KAAKyjB,UAAYzjB,KAAKikB,kBAGxB,IAAK,MAAMC,KAAWlkB,KAAKsjB,oBAAoBtkB,SAC7CgB,KAAKyjB,UAAUU,QAAQD,EAE3B,CAEAtd,UACE5G,KAAKyjB,UAAUO,aACfxd,MAAMI,SACR,CAGAlB,kBAAkBF,GAWhB,OATAA,EAAOxI,OAASpE,EAAW4M,EAAOxI,SAAWlE,SAAS8B,KAGtD4K,EAAOyd,WAAazd,EAAOyM,OAAU,GAAEzM,EAAOyM,oBAAsBzM,EAAOyd,WAE3C,iBAArBzd,EAAO2d,YAChB3d,EAAO2d,UAAY3d,EAAO2d,UAAUvmB,MAAM,KAAK4L,KAAI5F,GAASnG,OAAOC,WAAWkG,MAGzE4C,CACT,CAEAue,2BACO/jB,KAAK0G,QAAQwc,eAKlB3iB,EAAaC,IAAIR,KAAK0G,QAAQ1J,OAAQ+lB,IAEtCxiB,EAAac,GAAGrB,KAAK0G,QAAQ1J,OAAQ+lB,GAAaC,IAAuB9jB,IACvE,MAAMklB,EAAoBpkB,KAAKsjB,oBAAoBtgB,IAAI9D,EAAMlC,OAAOqnB,MACpE,GAAID,EAAmB,CACrBllB,EAAMsD,iBACN,MAAMnI,EAAO2F,KAAKujB,cAAgBzrB,OAC5BwsB,EAASF,EAAkBG,UAAYvkB,KAAKyG,SAAS8d,UAC3D,GAAIlqB,EAAKmqB,SAEP,YADAnqB,EAAKmqB,SAAS,CAAEC,IAAKH,EAAQI,SAAU,WAKzCrqB,EAAKse,UAAY2L,CACnB,KAEJ,CAEAL,kBACE,MAAMtQ,EAAU,CACdtZ,KAAM2F,KAAKujB,aACXJ,UAAWnjB,KAAK0G,QAAQyc,UACxBF,WAAYjjB,KAAK0G,QAAQuc,YAG3B,OAAO,IAAI0B,sBAAqBxjB,GAAWnB,KAAK4kB,kBAAkBzjB,IAAUwS,EAC9E,CAGAiR,kBAAkBzjB,GAChB,MAAM0jB,EAAgB1H,GAASnd,KAAKqjB,aAAargB,IAAK,IAAGma,EAAMngB,OAAO7E,MAChEkf,EAAW8F,IACfnd,KAAK0jB,oBAAoBC,gBAAkBxG,EAAMngB,OAAOunB,UACxDvkB,KAAK8kB,SAASD,EAAc1H,GAAO,EAG/ByG,GAAmB5jB,KAAKujB,cAAgBzqB,SAASoB,iBAAiBye,UAClEoM,EAAkBnB,GAAmB5jB,KAAK0jB,oBAAoBE,gBACpE5jB,KAAK0jB,oBAAoBE,gBAAkBA,EAE3C,IAAK,MAAMzG,KAAShc,EAAS,CAC3B,IAAKgc,EAAM6H,eAAgB,CACzBhlB,KAAKwjB,cAAgB,KACrBxjB,KAAKilB,kBAAkBJ,EAAc1H,IAErC,QACF,CAEA,MAAM+H,EAA2B/H,EAAMngB,OAAOunB,WAAavkB,KAAK0jB,oBAAoBC,gBAEpF,GAAIoB,GAAmBG,GAGrB,GAFA7N,EAAS8F,IAEJyG,EACH,YAOCmB,GAAoBG,GACvB7N,EAAS8F,EAEb,CACF,CAEA2G,mCACE9jB,KAAKqjB,aAAe,IAAIngB,IACxBlD,KAAKsjB,oBAAsB,IAAIpgB,IAE/B,MAAMiiB,EAAc3d,EAAevI,KAAK+jB,GAAuBhjB,KAAK0G,QAAQ1J,QAE5E,IAAK,MAAMooB,KAAUD,EAAa,CAEhC,IAAKC,EAAOf,MAAQ5qB,EAAW2rB,GAC7B,SAGF,MAAMhB,EAAoB5c,EAAeG,QAAQyd,EAAOf,KAAMrkB,KAAKyG,UAG/DzN,EAAUorB,KACZpkB,KAAKqjB,aAAajgB,IAAIgiB,EAAOf,KAAMe,GACnCplB,KAAKsjB,oBAAoBlgB,IAAIgiB,EAAOf,KAAMD,GAE9C,CACF,CAEAU,SAAS9nB,GACHgD,KAAKwjB,gBAAkBxmB,IAI3BgD,KAAKilB,kBAAkBjlB,KAAK0G,QAAQ1J,QACpCgD,KAAKwjB,cAAgBxmB,EACrBA,EAAOpD,UAAUqR,IAAIU,IACrB3L,KAAKqlB,iBAAiBroB,GAEtBuD,EAAauB,QAAQ9B,KAAKyG,SAjNN,wBAiNgC,CAAE5G,cAAe7C,IACvE,CAEAqoB,iBAAiBroB,GAEf,GAAIA,EAAOpD,UAAUC,SAlNQ,iBAmN3B2N,EAAeG,QAxMY,mBAwMsB3K,EAAO1D,QAzMpC,cA0MjBM,UAAUqR,IAAIU,SAInB,IAAK,MAAM2Z,KAAa9d,EAAeO,QAAQ/K,EAnNnB,qBAsN1B,IAAK,MAAMuoB,KAAQ/d,EAAeS,KAAKqd,EAlNhB,sDAmNrBC,EAAK3rB,UAAUqR,IAAIU,GAGzB,CAEAsZ,kBAAkBvV,GAChBA,EAAO9V,UAAUgK,OAAO+H,IAExB,MAAM6Z,EAAche,EAAevI,KAAM,gBAAgDyQ,GACzF,IAAK,MAAM+V,KAAQD,EACjBC,EAAK7rB,UAAUgK,OAAO+H,GAE1B,CAGAzE,uBAAuB1B,GACrB,OAAOxF,KAAKuJ,MAAK,WACf,MAAMC,EAAO4Z,GAAUja,oBAAoBnJ,KAAMwF,GAEjD,GAAsB,iBAAXA,EAAX,CAIA,QAAqBiE,IAAjBD,EAAKhE,IAAyBA,EAAO/D,WAAW,MAAmB,gBAAX+D,EAC1D,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,IANL,CAOF,GACF,EAOFjF,EAAac,GAAGvJ,OAlQa,8BAkQgB,KAC3C,IAAK,MAAM4tB,KAAOle,EAAevI,KA9PT,0BA+PtBmkB,GAAUja,oBAAoBuc,EAChC,IAOF1qB,EAAmBooB,ICnRnB,MAYMuC,GAAiB,YACjBC,GAAkB,aAClB1U,GAAe,UACfC,GAAiB,YAEjBxF,GAAoB,SACpBmT,GAAkB,OAClBvP,GAAkB,OAUlB7F,GAAuB,2EACvBmc,GAAuB,gHAAqBnc,KAQlD,MAAMoc,WAAYvf,EAChBV,YAAYxN,GACVmO,MAAMnO,GACN2H,KAAKsS,QAAUtS,KAAKyG,SAASnN,QAfN,uCAiBlB0G,KAAKsS,UAOVtS,KAAK+lB,sBAAsB/lB,KAAKsS,QAAStS,KAAKgmB,gBAE9CzlB,EAAac,GAAGrB,KAAKyG,SA3CF,kBA2C2BvH,GAASc,KAAK6N,SAAS3O,KACvE,CAGW7D,kBACT,MAzDS,KA0DX,CAGAiV,OACE,MAAM2V,EAAYjmB,KAAKyG,SACvB,GAAIzG,KAAKkmB,cAAcD,GACrB,OAIF,MAAME,EAASnmB,KAAKomB,iBAEdC,EAAYF,EAChB5lB,EAAauB,QAAQqkB,EAnEP,cAmE2B,CAAEtmB,cAAeomB,IAC1D,KAEgB1lB,EAAauB,QAAQmkB,EApEvB,cAoE8C,CAAEpmB,cAAesmB,IAEjEjkB,kBAAqBmkB,GAAaA,EAAUnkB,mBAI1DlC,KAAKsmB,YAAYH,EAAQF,GACzBjmB,KAAKumB,UAAUN,EAAWE,GAC5B,CAGAI,UAAUluB,EAASmuB,GACZnuB,IAILA,EAAQuB,UAAUqR,IAAIU,IAEtB3L,KAAKumB,UAAU/e,EAAeoB,uBAAuBvQ,IAgBrD2H,KAAKgH,gBAdY,KACsB,QAAjC3O,EAAQ2B,aAAa,SAKzB3B,EAAQqM,gBAAgB,YACxBrM,EAAQmM,aAAa,iBAAiB,GACtCxE,KAAKymB,gBAAgBpuB,GAAS,GAC9BkI,EAAauB,QAAQzJ,EAhGN,eAgG4B,CACzCwH,cAAe2mB,KARfnuB,EAAQuB,UAAUqR,IAAIsE,GAStB,GAG0BlX,EAASA,EAAQuB,UAAUC,SAASilB,KACpE,CAEAwH,YAAYjuB,EAASmuB,GACdnuB,IAILA,EAAQuB,UAAUgK,OAAO+H,IACzBtT,EAAQ2hB,OAERha,KAAKsmB,YAAY9e,EAAeoB,uBAAuBvQ,IAcvD2H,KAAKgH,gBAZY,KACsB,QAAjC3O,EAAQ2B,aAAa,SAKzB3B,EAAQmM,aAAa,iBAAiB,GACtCnM,EAAQmM,aAAa,WAAY,MACjCxE,KAAKymB,gBAAgBpuB,GAAS,GAC9BkI,EAAauB,QAAQzJ,EA7HL,gBA6H4B,CAAEwH,cAAe2mB,KAP3DnuB,EAAQuB,UAAUgK,OAAO2L,GAOgD,GAG/ClX,EAASA,EAAQuB,UAAUC,SAASilB,KACpE,CAEAjR,SAAS3O,GACP,IAAM,CAACymB,GAAgBC,GAAiB1U,GAAcC,IAAgB/P,SAASlC,EAAMyD,KACnF,OAGFzD,EAAMoV,kBACNpV,EAAMsD,iBACN,MAAMgM,EAAS,CAACoX,GAAiBzU,IAAgB/P,SAASlC,EAAMyD,KAC1D+jB,EAAoBvpB,EAAqB6C,KAAKgmB,eAAejhB,QAAO1M,IAAYoB,EAAWpB,KAAW6G,EAAMlC,OAAQwR,GAAQ,GAE9HkY,IACFA,EAAkB/T,MAAM,CAAEgU,eAAe,IACzCb,GAAI3c,oBAAoBud,GAAmBpW,OAE/C,CAEA0V,eACE,OAAOxe,EAAevI,KAAK4mB,GAAqB7lB,KAAKsS,QACvD,CAEA8T,iBACE,OAAOpmB,KAAKgmB,eAAe/mB,MAAK4I,GAAS7H,KAAKkmB,cAAcre,MAAW,IACzE,CAEAke,sBAAsBrW,EAAQ9H,GAC5B5H,KAAK4mB,yBAAyBlX,EAAQ,OAAQ,WAE9C,IAAK,MAAM7H,KAASD,EAClB5H,KAAK6mB,6BAA6Bhf,EAEtC,CAEAgf,6BAA6Bhf,GAC3BA,EAAQ7H,KAAK8mB,iBAAiBjf,GAC9B,MAAMkf,EAAW/mB,KAAKkmB,cAAcre,GAC9Bmf,EAAYhnB,KAAKinB,iBAAiBpf,GACxCA,EAAMrD,aAAa,gBAAiBuiB,GAEhCC,IAAcnf,GAChB7H,KAAK4mB,yBAAyBI,EAAW,OAAQ,gBAG9CD,GACHlf,EAAMrD,aAAa,WAAY,MAGjCxE,KAAK4mB,yBAAyB/e,EAAO,OAAQ,OAG7C7H,KAAKknB,mCAAmCrf,EAC1C,CAEAqf,mCAAmCrf,GACjC,MAAM7K,EAASwK,EAAeoB,uBAAuBf,GAEhD7K,IAILgD,KAAK4mB,yBAAyB5pB,EAAQ,OAAQ,YAE1C6K,EAAM1P,IACR6H,KAAK4mB,yBAAyB5pB,EAAQ,kBAAoB,IAAG6K,EAAM1P,MAEvE,CAEAsuB,gBAAgBpuB,EAAS8uB,GACvB,MAAMH,EAAYhnB,KAAKinB,iBAAiB5uB,GACxC,IAAK2uB,EAAUptB,UAAUC,SAxLN,YAyLjB,OAGF,MAAM+P,EAAS,CAAC/R,EAAUue,KACxB,MAAM/d,EAAUmP,EAAeG,QAAQ9P,EAAUmvB,GAC7C3uB,GACFA,EAAQuB,UAAUgQ,OAAOwM,EAAW+Q,EACtC,EAGFvd,EAjM6B,mBAiMI+B,IACjC/B,EAjM2B,iBAiMI2F,IAC/ByX,EAAUxiB,aAAa,gBAAiB2iB,EAC1C,CAEAP,yBAAyBvuB,EAASiiB,EAAW1X,GACtCvK,EAAQ0B,aAAaugB,IACxBjiB,EAAQmM,aAAa8V,EAAW1X,EAEpC,CAEAsjB,cAAcnW,GACZ,OAAOA,EAAKnW,UAAUC,SAAS8R,GACjC,CAGAmb,iBAAiB/W,GACf,OAAOA,EAAKjI,QAAQ+d,IAAuB9V,EAAOvI,EAAeG,QAAQke,GAAqB9V,EAChG,CAGAkX,iBAAiBlX,GACf,OAAOA,EAAKzW,QAlNO,gCAkNoByW,CACzC,CAGA7I,uBAAuB1B,GACrB,OAAOxF,KAAKuJ,MAAK,WACf,MAAMC,EAAOsc,GAAI3c,oBAAoBnJ,MAErC,GAAsB,iBAAXwF,EAAX,CAIA,QAAqBiE,IAAjBD,EAAKhE,IAAyBA,EAAO/D,WAAW,MAAmB,gBAAX+D,EAC1D,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,IANL,CAOF,GACF,EAOFjF,EAAac,GAAGvI,SA9Pc,eA8PkB4Q,IAAsB,SAAUxK,GAC1E,CAAC,IAAK,QAAQkC,SAASpB,KAAKkJ,UAC9BhK,EAAMsD,iBAGJ/I,EAAWuG,OAIf8lB,GAAI3c,oBAAoBnJ,MAAMsQ,MAChC,IAKA/P,EAAac,GAAGvJ,OA3Qa,eA2QgB,KAC3C,IAAK,MAAMO,KAAWmP,EAAevI,KAtPF,iGAuPjC6mB,GAAI3c,oBAAoB9Q,EAC1B,IAMF2C,EAAmB8qB,IC9RnB,MAcMsB,GAAkB,OAClB7X,GAAkB,OAClBmK,GAAqB,UAErBrU,GAAc,CAClBoa,UAAW,UACX4H,SAAU,UACVzH,MAAO,UAGHxa,GAAU,CACdqa,WAAW,EACX4H,UAAU,EACVzH,MAAO,KAOT,MAAM0H,WAAc/gB,EAClBV,YAAYxN,EAASmN,GACnBgB,MAAMnO,EAASmN,GAEfxF,KAAKigB,SAAW,KAChBjgB,KAAKunB,sBAAuB,EAC5BvnB,KAAKwnB,yBAA0B,EAC/BxnB,KAAKugB,eACP,CAGWnb,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEWhK,kBACT,MAtDS,OAuDX,CAGAiV,OACoB/P,EAAauB,QAAQ9B,KAAKyG,SAjD5B,iBAmDFvE,mBAIdlC,KAAKynB,gBAEDznB,KAAK0G,QAAQ+Y,WACfzf,KAAKyG,SAAS7M,UAAUqR,IAvDN,QAiEpBjL,KAAKyG,SAAS7M,UAAUgK,OAAOwjB,IAC/B5sB,EAAOwF,KAAKyG,UACZzG,KAAKyG,SAAS7M,UAAUqR,IAAIsE,GAAiBmK,IAE7C1Z,KAAKgH,gBAXY,KACfhH,KAAKyG,SAAS7M,UAAUgK,OAAO8V,IAC/BnZ,EAAauB,QAAQ9B,KAAKyG,SA9DX,kBAgEfzG,KAAK0nB,oBAAoB,GAOG1nB,KAAKyG,SAAUzG,KAAK0G,QAAQ+Y,WAC5D,CAEApP,OACOrQ,KAAK2nB,YAIQpnB,EAAauB,QAAQ9B,KAAKyG,SAlF5B,iBAoFFvE,mBAUdlC,KAAKyG,SAAS7M,UAAUqR,IAAIyO,IAC5B1Z,KAAKgH,gBAPY,KACfhH,KAAKyG,SAAS7M,UAAUqR,IAAImc,IAC5BpnB,KAAKyG,SAAS7M,UAAUgK,OAAO8V,GAAoBnK,IACnDhP,EAAauB,QAAQ9B,KAAKyG,SA1FV,kBA0FiC,GAIrBzG,KAAKyG,SAAUzG,KAAK0G,QAAQ+Y,YAC5D,CAEA7Y,UACE5G,KAAKynB,gBAEDznB,KAAK2nB,WACP3nB,KAAKyG,SAAS7M,UAAUgK,OAAO2L,IAGjC/I,MAAMI,SACR,CAEA+gB,UACE,OAAO3nB,KAAKyG,SAAS7M,UAAUC,SAAS0V,GAC1C,CAIAmY,qBACO1nB,KAAK0G,QAAQ2gB,WAIdrnB,KAAKunB,sBAAwBvnB,KAAKwnB,0BAItCxnB,KAAKigB,SAAW/iB,YAAW,KACzB8C,KAAKqQ,MAAM,GACVrQ,KAAK0G,QAAQkZ,QAClB,CAEAgI,eAAe1oB,EAAO2oB,GACpB,OAAQ3oB,EAAMuB,MACZ,IAAK,YACL,IAAK,WACHT,KAAKunB,qBAAuBM,EAC5B,MAGF,IAAK,UACL,IAAK,WACH7nB,KAAKwnB,wBAA0BK,EASnC,GAAIA,EAEF,YADA7nB,KAAKynB,gBAIP,MAAMhZ,EAAcvP,EAAMW,cACtBG,KAAKyG,WAAagI,GAAezO,KAAKyG,SAAS5M,SAAS4U,IAI5DzO,KAAK0nB,oBACP,CAEAnH,gBACEhgB,EAAac,GAAGrB,KAAKyG,SArKA,sBAqK2BvH,GAASc,KAAK4nB,eAAe1oB,GAAO,KACpFqB,EAAac,GAAGrB,KAAKyG,SArKD,qBAqK2BvH,GAASc,KAAK4nB,eAAe1oB,GAAO,KACnFqB,EAAac,GAAGrB,KAAKyG,SArKF,oBAqK2BvH,GAASc,KAAK4nB,eAAe1oB,GAAO,KAClFqB,EAAac,GAAGrB,KAAKyG,SArKD,qBAqK2BvH,GAASc,KAAK4nB,eAAe1oB,GAAO,IACrF,CAEAuoB,gBACEvZ,aAAalO,KAAKigB,UAClBjgB,KAAKigB,SAAW,IAClB,CAGA/Y,uBAAuB1B,GACrB,OAAOxF,KAAKuJ,MAAK,WACf,MAAMC,EAAO8d,GAAMne,oBAAoBnJ,KAAMwF,GAE7C,GAAsB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBgE,EAAKhE,GACd,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,GAAQxF,KACf,CACF,GACF,E,OAOF8I,EAAqBwe,IAMrBtsB,EAAmBssB,IC1MJ,CACble,QACAO,SACA0C,YACAsD,YACAyC,YACA2F,SACAgC,aACA8I,WACAO,aACA0C,OACAwB,SACAvH,W"}