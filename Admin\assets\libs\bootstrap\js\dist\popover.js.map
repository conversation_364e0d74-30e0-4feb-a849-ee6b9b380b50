{"version": 3, "file": "popover.js", "sources": ["../src/popover.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport Tooltip from './tooltip'\n\n/**\n * Constants\n */\n\nconst NAME = 'popover'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\nconst Default = {\n  ...Tooltip.Default,\n  content: '',\n  offset: [0, 8],\n  placement: 'right',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n    '<div class=\"popover-arrow\"></div>' +\n    '<h3 class=\"popover-header\"></h3>' +\n    '<div class=\"popover-body\"></div>' +\n    '</div>',\n  trigger: 'click'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(null|string|element|function)'\n}\n\n/**\n * Class definition\n */\n\nclass Popover extends Tooltip {\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Overrides\n  _isWithContent() {\n    return this._getTitle() || this._getContent()\n  }\n\n  // Private\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TITLE]: this._getTitle(),\n      [SELECTOR_CONTENT]: this._getContent()\n    }\n  }\n\n  _getContent() {\n    return this._resolvePossibleFunction(this._config.content)\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Popover.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Popover)\n\nexport default Popover\n"], "names": ["NAME", "SELECTOR_TITLE", "SELECTOR_CONTENT", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "content", "offset", "placement", "template", "trigger", "DefaultType", "Popover", "_isWithContent", "_getTitle", "_getContent", "_getContentForTemplate", "_resolvePossibleFunction", "_config", "jQueryInterface", "config", "each", "data", "getOrCreateInstance", "TypeError", "defineJQueryPlugin"], "mappings": ";;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAKA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,SAAb,CAAA;EAEA,MAAMC,cAAc,GAAG,iBAAvB,CAAA;EACA,MAAMC,gBAAgB,GAAG,eAAzB,CAAA;EAEA,MAAMC,OAAO,GAAG,EACd,GAAGC,wBAAO,CAACD,OADG;EAEdE,EAAAA,OAAO,EAAE,EAFK;EAGdC,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CAHM;EAIdC,EAAAA,SAAS,EAAE,OAJG;IAKdC,QAAQ,EAAE,yCACR,mCADQ,GAER,kCAFQ,GAGR,kCAHQ,GAIR,QATY;EAUdC,EAAAA,OAAO,EAAE,OAAA;EAVK,CAAhB,CAAA;EAaA,MAAMC,WAAW,GAAG,EAClB,GAAGN,wBAAO,CAACM,WADO;EAElBL,EAAAA,OAAO,EAAE,gCAAA;EAFS,CAApB,CAAA;EAKA;EACA;EACA;;EAEA,MAAMM,OAAN,SAAsBP,wBAAtB,CAA8B;EAC5B;EACkB,EAAA,WAAPD,OAAO,GAAG;EACnB,IAAA,OAAOA,OAAP,CAAA;EACD,GAAA;;EAEqB,EAAA,WAAXO,WAAW,GAAG;EACvB,IAAA,OAAOA,WAAP,CAAA;EACD,GAAA;;EAEc,EAAA,WAAJV,IAAI,GAAG;EAChB,IAAA,OAAOA,IAAP,CAAA;EACD,GAZ2B;;;EAe5BY,EAAAA,cAAc,GAAG;EACf,IAAA,OAAO,IAAKC,CAAAA,SAAL,EAAoB,IAAA,IAAA,CAAKC,WAAL,EAA3B,CAAA;EACD,GAjB2B;;;EAoB5BC,EAAAA,sBAAsB,GAAG;MACvB,OAAO;EACL,MAAA,CAACd,cAAD,GAAkB,IAAKY,CAAAA,SAAL,EADb;QAEL,CAACX,gBAAD,GAAoB,IAAA,CAAKY,WAAL,EAAA;OAFtB,CAAA;EAID,GAAA;;EAEDA,EAAAA,WAAW,GAAG;EACZ,IAAA,OAAO,KAAKE,wBAAL,CAA8B,KAAKC,OAAL,CAAaZ,OAA3C,CAAP,CAAA;EACD,GA7B2B;;;IAgCN,OAAfa,eAAe,CAACC,MAAD,EAAS;MAC7B,OAAO,IAAA,CAAKC,IAAL,CAAU,YAAY;QAC3B,MAAMC,IAAI,GAAGV,OAAO,CAACW,mBAAR,CAA4B,IAA5B,EAAkCH,MAAlC,CAAb,CAAA;;EAEA,MAAA,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,QAAA,OAAA;EACD,OAAA;;EAED,MAAA,IAAI,OAAOE,IAAI,CAACF,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,QAAA,MAAM,IAAII,SAAJ,CAAe,CAAmBJ,iBAAAA,EAAAA,MAAO,GAAzC,CAAN,CAAA;EACD,OAAA;;QAEDE,IAAI,CAACF,MAAD,CAAJ,EAAA,CAAA;EACD,KAZM,CAAP,CAAA;EAaD,GAAA;;EA9C2B,CAAA;EAiD9B;EACA;EACA;;;AAEAK,0BAAkB,CAACb,OAAD,CAAlB;;;;;;;;"}