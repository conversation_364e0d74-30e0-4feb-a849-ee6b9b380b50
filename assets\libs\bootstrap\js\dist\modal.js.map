{"version": 3, "file": "modal.js", "sources": ["../src/modal.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, getElementFromSelector, isRTL, isVisible, reflow } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport ScrollBarHelper from './util/scrollbar'\nimport BaseComponent from './base-component'\nimport Backdrop from './util/backdrop'\nimport FocusTrap from './util/focustrap'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * Constants\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst OPEN_SELECTOR = '.modal.show'\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\n\nconst Default = {\n  backdrop: true,\n  focus: true,\n  keyboard: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  focus: 'boolean',\n  keyboard: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._isShown = false\n    this._isTransitioning = false\n    this._scrollBar = new ScrollBarHelper()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._isTransitioning = true\n\n    this._scrollBar.hide()\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n\n    this._adjustDialog()\n\n    this._backdrop.show(() => this._showElement(relatedTarget))\n  }\n\n  hide() {\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    this._isTransitioning = true\n    this._focustrap.deactivate()\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    this._queueCallback(() => this._hideModal(), this._element, this._isAnimated())\n  }\n\n  dispose() {\n    for (const htmlElement of [window, this._dialog]) {\n      EventHandler.off(htmlElement, EVENT_KEY)\n    }\n\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: Boolean(this._config.backdrop), // 'static' option will be translated to true, and booleans will keep their value,\n      isAnimated: this._isAnimated()\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _showElement(relatedTarget) {\n    // try to append dynamic modal\n    if (!document.body.contains(this._element)) {\n      document.body.append(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._focustrap.activate()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    this._queueCallback(transitionComplete, this._dialog, this._isAnimated())\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (this._config.keyboard) {\n        event.preventDefault()\n        this.hide()\n        return\n      }\n\n      this._triggerBackdropTransition()\n    })\n\n    EventHandler.on(window, EVENT_RESIZE, () => {\n      if (this._isShown && !this._isTransitioning) {\n        this._adjustDialog()\n      }\n    })\n\n    EventHandler.on(this._element, EVENT_MOUSEDOWN_DISMISS, event => {\n      // a bad trick to segregate clicks that may start inside dialog but end outside, and avoid listen to scrollbar clicks\n      EventHandler.one(this._element, EVENT_CLICK_DISMISS, event2 => {\n        if (this._element !== event.target || this._element !== event2.target) {\n          return\n        }\n\n        if (this._config.backdrop === 'static') {\n          this._triggerBackdropTransition()\n          return\n        }\n\n        if (this._config.backdrop) {\n          this.hide()\n        }\n      })\n    })\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n\n    this._backdrop.hide(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._scrollBar.reset()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const initialOverflowY = this._element.style.overflowY\n    // return if the following background transition hasn't yet completed\n    if (initialOverflowY === 'hidden' || this._element.classList.contains(CLASS_NAME_STATIC)) {\n      return\n    }\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n    this._queueCallback(() => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      this._queueCallback(() => {\n        this._element.style.overflowY = initialOverflowY\n      }, this._dialog)\n    }, this._dialog)\n\n    this._element.focus()\n  }\n\n  /**\n   * The following methods are used to handle overflowing modals\n   */\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const scrollbarWidth = this._scrollBar.getWidth()\n    const isBodyOverflowing = scrollbarWidth > 0\n\n    if (isBodyOverflowing && !isModalOverflowing) {\n      const property = isRTL() ? 'paddingLeft' : 'paddingRight'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n\n    if (!isBodyOverflowing && isModalOverflowing) {\n      const property = isRTL() ? 'paddingRight' : 'paddingLeft'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  // Static\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      const data = Modal.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](relatedTarget)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  // avoid conflict when clicking modal toggler while another one is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen) {\n    Modal.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Modal.getOrCreateInstance(target)\n\n  data.toggle(this)\n})\n\nenableDismissTrigger(Modal)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Modal)\n\nexport default Modal\n"], "names": ["NAME", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "ESCAPE_KEY", "EVENT_HIDE", "EVENT_HIDE_PREVENTED", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "EVENT_KEYDOWN_DISMISS", "EVENT_CLICK_DATA_API", "CLASS_NAME_OPEN", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "CLASS_NAME_STATIC", "OPEN_SELECTOR", "SELECTOR_DIALOG", "SELECTOR_MODAL_BODY", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON><PERSON>", "backdrop", "focus", "keyboard", "DefaultType", "Modal", "BaseComponent", "constructor", "element", "config", "_dialog", "SelectorEngine", "findOne", "_element", "_backdrop", "_initializeBackDrop", "_focustrap", "_initializeFocusTrap", "_isShown", "_isTransitioning", "_scrollBar", "ScrollBarHelper", "_addEventListeners", "toggle", "relatedTarget", "hide", "show", "showEvent", "EventHandler", "trigger", "defaultPrevented", "document", "body", "classList", "add", "_adjustDialog", "_showElement", "hideEvent", "deactivate", "remove", "_queueCallback", "_hideModal", "_isAnimated", "dispose", "htmlElement", "window", "off", "handleUpdate", "Backdrop", "isVisible", "Boolean", "_config", "isAnimated", "FocusTrap", "trapElement", "contains", "append", "style", "display", "removeAttribute", "setAttribute", "scrollTop", "modalBody", "reflow", "transitionComplete", "activate", "on", "event", "key", "preventDefault", "_triggerBackdropTransition", "one", "event2", "target", "_resetAdjustments", "reset", "isModalOverflowing", "scrollHeight", "documentElement", "clientHeight", "initialOverflowY", "overflowY", "scrollbarWidth", "getWidth", "isBodyOverflowing", "property", "isRTL", "paddingLeft", "paddingRight", "jQueryInterface", "each", "data", "getOrCreateInstance", "TypeError", "getElementFromSelector", "includes", "tagName", "alreadyOpen", "getInstance", "enableDismissTrigger", "defineJQueryPlugin"], "mappings": ";;;;;;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAWA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,OAAb,CAAA;EACA,MAAMC,QAAQ,GAAG,UAAjB,CAAA;EACA,MAAMC,SAAS,GAAI,CAAGD,CAAAA,EAAAA,QAAS,CAA/B,CAAA,CAAA;EACA,MAAME,YAAY,GAAG,WAArB,CAAA;EACA,MAAMC,UAAU,GAAG,QAAnB,CAAA;EAEA,MAAMC,UAAU,GAAI,CAAMH,IAAAA,EAAAA,SAAU,CAApC,CAAA,CAAA;EACA,MAAMI,oBAAoB,GAAI,CAAeJ,aAAAA,EAAAA,SAAU,CAAvD,CAAA,CAAA;EACA,MAAMK,YAAY,GAAI,CAAQL,MAAAA,EAAAA,SAAU,CAAxC,CAAA,CAAA;EACA,MAAMM,UAAU,GAAI,CAAMN,IAAAA,EAAAA,SAAU,CAApC,CAAA,CAAA;EACA,MAAMO,WAAW,GAAI,CAAOP,KAAAA,EAAAA,SAAU,CAAtC,CAAA,CAAA;EACA,MAAMQ,YAAY,GAAI,CAAQR,MAAAA,EAAAA,SAAU,CAAxC,CAAA,CAAA;EACA,MAAMS,mBAAmB,GAAI,CAAeT,aAAAA,EAAAA,SAAU,CAAtD,CAAA,CAAA;EACA,MAAMU,uBAAuB,GAAI,CAAmBV,iBAAAA,EAAAA,SAAU,CAA9D,CAAA,CAAA;EACA,MAAMW,qBAAqB,GAAI,CAAiBX,eAAAA,EAAAA,SAAU,CAA1D,CAAA,CAAA;EACA,MAAMY,oBAAoB,GAAI,CAAA,KAAA,EAAOZ,SAAU,CAAA,EAAEC,YAAa,CAA9D,CAAA,CAAA;EAEA,MAAMY,eAAe,GAAG,YAAxB,CAAA;EACA,MAAMC,eAAe,GAAG,MAAxB,CAAA;EACA,MAAMC,eAAe,GAAG,MAAxB,CAAA;EACA,MAAMC,iBAAiB,GAAG,cAA1B,CAAA;EAEA,MAAMC,aAAa,GAAG,aAAtB,CAAA;EACA,MAAMC,eAAe,GAAG,eAAxB,CAAA;EACA,MAAMC,mBAAmB,GAAG,aAA5B,CAAA;EACA,MAAMC,oBAAoB,GAAG,0BAA7B,CAAA;EAEA,MAAMC,OAAO,GAAG;EACdC,EAAAA,QAAQ,EAAE,IADI;EAEdC,EAAAA,KAAK,EAAE,IAFO;EAGdC,EAAAA,QAAQ,EAAE,IAAA;EAHI,CAAhB,CAAA;EAMA,MAAMC,WAAW,GAAG;EAClBH,EAAAA,QAAQ,EAAE,kBADQ;EAElBC,EAAAA,KAAK,EAAE,SAFW;EAGlBC,EAAAA,QAAQ,EAAE,SAAA;EAHQ,CAApB,CAAA;EAMA;EACA;EACA;;EAEA,MAAME,KAAN,SAAoBC,8BAApB,CAAkC;EAChCC,EAAAA,WAAW,CAACC,OAAD,EAAUC,MAAV,EAAkB;MAC3B,KAAMD,CAAAA,OAAN,EAAeC,MAAf,CAAA,CAAA;MAEA,IAAKC,CAAAA,OAAL,GAAeC,+BAAc,CAACC,OAAf,CAAuBf,eAAvB,EAAwC,IAAKgB,CAAAA,QAA7C,CAAf,CAAA;EACA,IAAA,IAAA,CAAKC,SAAL,GAAiB,IAAKC,CAAAA,mBAAL,EAAjB,CAAA;EACA,IAAA,IAAA,CAAKC,UAAL,GAAkB,IAAKC,CAAAA,oBAAL,EAAlB,CAAA;MACA,IAAKC,CAAAA,QAAL,GAAgB,KAAhB,CAAA;MACA,IAAKC,CAAAA,gBAAL,GAAwB,KAAxB,CAAA;EACA,IAAA,IAAA,CAAKC,UAAL,GAAkB,IAAIC,gCAAJ,EAAlB,CAAA;;EAEA,IAAA,IAAA,CAAKC,kBAAL,EAAA,CAAA;EACD,GAZ+B;;;EAed,EAAA,WAAPtB,OAAO,GAAG;EACnB,IAAA,OAAOA,OAAP,CAAA;EACD,GAAA;;EAEqB,EAAA,WAAXI,WAAW,GAAG;EACvB,IAAA,OAAOA,WAAP,CAAA;EACD,GAAA;;EAEc,EAAA,WAAJ3B,IAAI,GAAG;EAChB,IAAA,OAAOA,IAAP,CAAA;EACD,GAzB+B;;;IA4BhC8C,MAAM,CAACC,aAAD,EAAgB;MACpB,OAAO,IAAA,CAAKN,QAAL,GAAgB,IAAKO,CAAAA,IAAL,EAAhB,GAA8B,IAAKC,CAAAA,IAAL,CAAUF,aAAV,CAArC,CAAA;EACD,GAAA;;IAEDE,IAAI,CAACF,aAAD,EAAgB;EAClB,IAAA,IAAI,IAAKN,CAAAA,QAAL,IAAiB,IAAA,CAAKC,gBAA1B,EAA4C;EAC1C,MAAA,OAAA;EACD,KAAA;;MAED,MAAMQ,SAAS,GAAGC,6BAAY,CAACC,OAAb,CAAqB,IAAKhB,CAAAA,QAA1B,EAAoC5B,UAApC,EAAgD;EAChEuC,MAAAA,aAAAA;EADgE,KAAhD,CAAlB,CAAA;;MAIA,IAAIG,SAAS,CAACG,gBAAd,EAAgC;EAC9B,MAAA,OAAA;EACD,KAAA;;MAED,IAAKZ,CAAAA,QAAL,GAAgB,IAAhB,CAAA;MACA,IAAKC,CAAAA,gBAAL,GAAwB,IAAxB,CAAA;;MAEA,IAAKC,CAAAA,UAAL,CAAgBK,IAAhB,EAAA,CAAA;;EAEAM,IAAAA,QAAQ,CAACC,IAAT,CAAcC,SAAd,CAAwBC,GAAxB,CAA4B1C,eAA5B,CAAA,CAAA;;EAEA,IAAA,IAAA,CAAK2C,aAAL,EAAA,CAAA;;MAEA,IAAKrB,CAAAA,SAAL,CAAeY,IAAf,CAAoB,MAAM,IAAKU,CAAAA,YAAL,CAAkBZ,aAAlB,CAA1B,CAAA,CAAA;EACD,GAAA;;EAEDC,EAAAA,IAAI,GAAG;EACL,IAAA,IAAI,CAAC,IAAKP,CAAAA,QAAN,IAAkB,IAAA,CAAKC,gBAA3B,EAA6C;EAC3C,MAAA,OAAA;EACD,KAAA;;MAED,MAAMkB,SAAS,GAAGT,6BAAY,CAACC,OAAb,CAAqB,IAAKhB,CAAAA,QAA1B,EAAoC/B,UAApC,CAAlB,CAAA;;MAEA,IAAIuD,SAAS,CAACP,gBAAd,EAAgC;EAC9B,MAAA,OAAA;EACD,KAAA;;MAED,IAAKZ,CAAAA,QAAL,GAAgB,KAAhB,CAAA;MACA,IAAKC,CAAAA,gBAAL,GAAwB,IAAxB,CAAA;;MACA,IAAKH,CAAAA,UAAL,CAAgBsB,UAAhB,EAAA,CAAA;;EAEA,IAAA,IAAA,CAAKzB,QAAL,CAAcoB,SAAd,CAAwBM,MAAxB,CAA+B7C,eAA/B,CAAA,CAAA;;EAEA,IAAA,IAAA,CAAK8C,cAAL,CAAoB,MAAM,IAAA,CAAKC,UAAL,EAA1B,EAA6C,IAAA,CAAK5B,QAAlD,EAA4D,IAAK6B,CAAAA,WAAL,EAA5D,CAAA,CAAA;EACD,GAAA;;EAEDC,EAAAA,OAAO,GAAG;MACR,KAAK,MAAMC,WAAX,IAA0B,CAACC,MAAD,EAAS,IAAA,CAAKnC,OAAd,CAA1B,EAAkD;EAChDkB,MAAAA,6BAAY,CAACkB,GAAb,CAAiBF,WAAjB,EAA8BjE,SAA9B,CAAA,CAAA;EACD,KAAA;;MAED,IAAKmC,CAAAA,SAAL,CAAe6B,OAAf,EAAA,CAAA;;MACA,IAAK3B,CAAAA,UAAL,CAAgBsB,UAAhB,EAAA,CAAA;;EACA,IAAA,KAAA,CAAMK,OAAN,EAAA,CAAA;EACD,GAAA;;EAEDI,EAAAA,YAAY,GAAG;EACb,IAAA,IAAA,CAAKZ,aAAL,EAAA,CAAA;EACD,GAzF+B;;;EA4FhCpB,EAAAA,mBAAmB,GAAG;MACpB,OAAO,IAAIiC,yBAAJ,CAAa;EAClBC,MAAAA,SAAS,EAAEC,OAAO,CAAC,KAAKC,OAAL,CAAalD,QAAd,CADA;EACyB;QAC3CmD,UAAU,EAAE,KAAKV,WAAL,EAAA;EAFM,KAAb,CAAP,CAAA;EAID,GAAA;;EAEDzB,EAAAA,oBAAoB,GAAG;MACrB,OAAO,IAAIoC,0BAAJ,CAAc;EACnBC,MAAAA,WAAW,EAAE,IAAKzC,CAAAA,QAAAA;EADC,KAAd,CAAP,CAAA;EAGD,GAAA;;IAEDuB,YAAY,CAACZ,aAAD,EAAgB;EAC1B;MACA,IAAI,CAACO,QAAQ,CAACC,IAAT,CAAcuB,QAAd,CAAuB,IAAA,CAAK1C,QAA5B,CAAL,EAA4C;EAC1CkB,MAAAA,QAAQ,CAACC,IAAT,CAAcwB,MAAd,CAAqB,KAAK3C,QAA1B,CAAA,CAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKA,QAAL,CAAc4C,KAAd,CAAoBC,OAApB,GAA8B,OAA9B,CAAA;;EACA,IAAA,IAAA,CAAK7C,QAAL,CAAc8C,eAAd,CAA8B,aAA9B,CAAA,CAAA;;EACA,IAAA,IAAA,CAAK9C,QAAL,CAAc+C,YAAd,CAA2B,YAA3B,EAAyC,IAAzC,CAAA,CAAA;;EACA,IAAA,IAAA,CAAK/C,QAAL,CAAc+C,YAAd,CAA2B,MAA3B,EAAmC,QAAnC,CAAA,CAAA;;EACA,IAAA,IAAA,CAAK/C,QAAL,CAAcgD,SAAd,GAA0B,CAA1B,CAAA;MAEA,MAAMC,SAAS,GAAGnD,+BAAc,CAACC,OAAf,CAAuBd,mBAAvB,EAA4C,IAAKY,CAAAA,OAAjD,CAAlB,CAAA;;EACA,IAAA,IAAIoD,SAAJ,EAAe;QACbA,SAAS,CAACD,SAAV,GAAsB,CAAtB,CAAA;EACD,KAAA;;MAEDE,YAAM,CAAC,IAAKlD,CAAAA,QAAN,CAAN,CAAA;;EAEA,IAAA,IAAA,CAAKA,QAAL,CAAcoB,SAAd,CAAwBC,GAAxB,CAA4BxC,eAA5B,CAAA,CAAA;;MAEA,MAAMsE,kBAAkB,GAAG,MAAM;EAC/B,MAAA,IAAI,IAAKb,CAAAA,OAAL,CAAajD,KAAjB,EAAwB;UACtB,IAAKc,CAAAA,UAAL,CAAgBiD,QAAhB,EAAA,CAAA;EACD,OAAA;;QAED,IAAK9C,CAAAA,gBAAL,GAAwB,KAAxB,CAAA;EACAS,MAAAA,6BAAY,CAACC,OAAb,CAAqB,KAAKhB,QAA1B,EAAoC3B,WAApC,EAAiD;EAC/CsC,QAAAA,aAAAA;SADF,CAAA,CAAA;OANF,CAAA;;MAWA,IAAKgB,CAAAA,cAAL,CAAoBwB,kBAApB,EAAwC,KAAKtD,OAA7C,EAAsD,IAAKgC,CAAAA,WAAL,EAAtD,CAAA,CAAA;EACD,GAAA;;EAEDpB,EAAAA,kBAAkB,GAAG;MACnBM,6BAAY,CAACsC,EAAb,CAAgB,IAAA,CAAKrD,QAArB,EAA+BvB,qBAA/B,EAAsD6E,KAAK,IAAI;EAC7D,MAAA,IAAIA,KAAK,CAACC,GAAN,KAAcvF,UAAlB,EAA8B;EAC5B,QAAA,OAAA;EACD,OAAA;;EAED,MAAA,IAAI,IAAKsE,CAAAA,OAAL,CAAahD,QAAjB,EAA2B;EACzBgE,QAAAA,KAAK,CAACE,cAAN,EAAA,CAAA;EACA,QAAA,IAAA,CAAK5C,IAAL,EAAA,CAAA;EACA,QAAA,OAAA;EACD,OAAA;;EAED,MAAA,IAAA,CAAK6C,0BAAL,EAAA,CAAA;OAXF,CAAA,CAAA;EAcA1C,IAAAA,6BAAY,CAACsC,EAAb,CAAgBrB,MAAhB,EAAwB1D,YAAxB,EAAsC,MAAM;EAC1C,MAAA,IAAI,KAAK+B,QAAL,IAAiB,CAAC,IAAA,CAAKC,gBAA3B,EAA6C;EAC3C,QAAA,IAAA,CAAKgB,aAAL,EAAA,CAAA;EACD,OAAA;OAHH,CAAA,CAAA;MAMAP,6BAAY,CAACsC,EAAb,CAAgB,IAAA,CAAKrD,QAArB,EAA+BxB,uBAA/B,EAAwD8E,KAAK,IAAI;EAC/D;QACAvC,6BAAY,CAAC2C,GAAb,CAAiB,IAAA,CAAK1D,QAAtB,EAAgCzB,mBAAhC,EAAqDoF,MAAM,IAAI;EAC7D,QAAA,IAAI,IAAK3D,CAAAA,QAAL,KAAkBsD,KAAK,CAACM,MAAxB,IAAkC,IAAA,CAAK5D,QAAL,KAAkB2D,MAAM,CAACC,MAA/D,EAAuE;EACrE,UAAA,OAAA;EACD,SAAA;;EAED,QAAA,IAAI,KAAKtB,OAAL,CAAalD,QAAb,KAA0B,QAA9B,EAAwC;EACtC,UAAA,IAAA,CAAKqE,0BAAL,EAAA,CAAA;;EACA,UAAA,OAAA;EACD,SAAA;;EAED,QAAA,IAAI,IAAKnB,CAAAA,OAAL,CAAalD,QAAjB,EAA2B;EACzB,UAAA,IAAA,CAAKwB,IAAL,EAAA,CAAA;EACD,SAAA;SAZH,CAAA,CAAA;OAFF,CAAA,CAAA;EAiBD,GAAA;;EAEDgB,EAAAA,UAAU,GAAG;EACX,IAAA,IAAA,CAAK5B,QAAL,CAAc4C,KAAd,CAAoBC,OAApB,GAA8B,MAA9B,CAAA;;EACA,IAAA,IAAA,CAAK7C,QAAL,CAAc+C,YAAd,CAA2B,aAA3B,EAA0C,IAA1C,CAAA,CAAA;;EACA,IAAA,IAAA,CAAK/C,QAAL,CAAc8C,eAAd,CAA8B,YAA9B,CAAA,CAAA;;EACA,IAAA,IAAA,CAAK9C,QAAL,CAAc8C,eAAd,CAA8B,MAA9B,CAAA,CAAA;;MACA,IAAKxC,CAAAA,gBAAL,GAAwB,KAAxB,CAAA;;EAEA,IAAA,IAAA,CAAKL,SAAL,CAAeW,IAAf,CAAoB,MAAM;EACxBM,MAAAA,QAAQ,CAACC,IAAT,CAAcC,SAAd,CAAwBM,MAAxB,CAA+B/C,eAA/B,CAAA,CAAA;;EACA,MAAA,IAAA,CAAKkF,iBAAL,EAAA,CAAA;;QACA,IAAKtD,CAAAA,UAAL,CAAgBuD,KAAhB,EAAA,CAAA;;EACA/C,MAAAA,6BAAY,CAACC,OAAb,CAAqB,IAAKhB,CAAAA,QAA1B,EAAoC7B,YAApC,CAAA,CAAA;OAJF,CAAA,CAAA;EAMD,GAAA;;EAED0D,EAAAA,WAAW,GAAG;MACZ,OAAO,IAAA,CAAK7B,QAAL,CAAcoB,SAAd,CAAwBsB,QAAxB,CAAiC9D,eAAjC,CAAP,CAAA;EACD,GAAA;;EAED6E,EAAAA,0BAA0B,GAAG;MAC3B,MAAMjC,SAAS,GAAGT,6BAAY,CAACC,OAAb,CAAqB,IAAKhB,CAAAA,QAA1B,EAAoC9B,oBAApC,CAAlB,CAAA;;MACA,IAAIsD,SAAS,CAACP,gBAAd,EAAgC;EAC9B,MAAA,OAAA;EACD,KAAA;;MAED,MAAM8C,kBAAkB,GAAG,IAAA,CAAK/D,QAAL,CAAcgE,YAAd,GAA6B9C,QAAQ,CAAC+C,eAAT,CAAyBC,YAAjF,CAAA;MACA,MAAMC,gBAAgB,GAAG,IAAKnE,CAAAA,QAAL,CAAc4C,KAAd,CAAoBwB,SAA7C,CAP2B;;EAS3B,IAAA,IAAID,gBAAgB,KAAK,QAArB,IAAiC,IAAKnE,CAAAA,QAAL,CAAcoB,SAAd,CAAwBsB,QAAxB,CAAiC5D,iBAAjC,CAArC,EAA0F;EACxF,MAAA,OAAA;EACD,KAAA;;MAED,IAAI,CAACiF,kBAAL,EAAyB;EACvB,MAAA,IAAA,CAAK/D,QAAL,CAAc4C,KAAd,CAAoBwB,SAApB,GAAgC,QAAhC,CAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKpE,QAAL,CAAcoB,SAAd,CAAwBC,GAAxB,CAA4BvC,iBAA5B,CAAA,CAAA;;MACA,IAAK6C,CAAAA,cAAL,CAAoB,MAAM;EACxB,MAAA,IAAA,CAAK3B,QAAL,CAAcoB,SAAd,CAAwBM,MAAxB,CAA+B5C,iBAA/B,CAAA,CAAA;;QACA,IAAK6C,CAAAA,cAAL,CAAoB,MAAM;EACxB,QAAA,IAAA,CAAK3B,QAAL,CAAc4C,KAAd,CAAoBwB,SAApB,GAAgCD,gBAAhC,CAAA;SADF,EAEG,KAAKtE,OAFR,CAAA,CAAA;OAFF,EAKG,KAAKA,OALR,CAAA,CAAA;;MAOA,IAAKG,CAAAA,QAAL,CAAcX,KAAd,EAAA,CAAA;EACD,GAAA;EAED;EACF;EACA;;;EAEEiC,EAAAA,aAAa,GAAG;MACd,MAAMyC,kBAAkB,GAAG,IAAA,CAAK/D,QAAL,CAAcgE,YAAd,GAA6B9C,QAAQ,CAAC+C,eAAT,CAAyBC,YAAjF,CAAA;;EACA,IAAA,MAAMG,cAAc,GAAG,IAAA,CAAK9D,UAAL,CAAgB+D,QAAhB,EAAvB,CAAA;;EACA,IAAA,MAAMC,iBAAiB,GAAGF,cAAc,GAAG,CAA3C,CAAA;;EAEA,IAAA,IAAIE,iBAAiB,IAAI,CAACR,kBAA1B,EAA8C;EAC5C,MAAA,MAAMS,QAAQ,GAAGC,WAAK,EAAK,GAAA,aAAL,GAAqB,cAA3C,CAAA;QACA,IAAKzE,CAAAA,QAAL,CAAc4C,KAAd,CAAoB4B,QAApB,CAAiC,GAAA,CAAA,EAAEH,cAAe,CAAlD,EAAA,CAAA,CAAA;EACD,KAAA;;EAED,IAAA,IAAI,CAACE,iBAAD,IAAsBR,kBAA1B,EAA8C;EAC5C,MAAA,MAAMS,QAAQ,GAAGC,WAAK,EAAK,GAAA,cAAL,GAAsB,aAA5C,CAAA;QACA,IAAKzE,CAAAA,QAAL,CAAc4C,KAAd,CAAoB4B,QAApB,CAAiC,GAAA,CAAA,EAAEH,cAAe,CAAlD,EAAA,CAAA,CAAA;EACD,KAAA;EACF,GAAA;;EAEDR,EAAAA,iBAAiB,GAAG;EAClB,IAAA,IAAA,CAAK7D,QAAL,CAAc4C,KAAd,CAAoB8B,WAApB,GAAkC,EAAlC,CAAA;EACA,IAAA,IAAA,CAAK1E,QAAL,CAAc4C,KAAd,CAAoB+B,YAApB,GAAmC,EAAnC,CAAA;EACD,GA1P+B;;;EA6PV,EAAA,OAAfC,eAAe,CAAChF,MAAD,EAASe,aAAT,EAAwB;MAC5C,OAAO,IAAA,CAAKkE,IAAL,CAAU,YAAY;QAC3B,MAAMC,IAAI,GAAGtF,KAAK,CAACuF,mBAAN,CAA0B,IAA1B,EAAgCnF,MAAhC,CAAb,CAAA;;EAEA,MAAA,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,QAAA,OAAA;EACD,OAAA;;EAED,MAAA,IAAI,OAAOkF,IAAI,CAAClF,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,QAAA,MAAM,IAAIoF,SAAJ,CAAe,CAAmBpF,iBAAAA,EAAAA,MAAO,GAAzC,CAAN,CAAA;EACD,OAAA;;EAEDkF,MAAAA,IAAI,CAAClF,MAAD,CAAJ,CAAae,aAAb,CAAA,CAAA;EACD,KAZM,CAAP,CAAA;EAaD,GAAA;;EA3Q+B,CAAA;EA8QlC;EACA;EACA;;;AAEAI,+BAAY,CAACsC,EAAb,CAAgBnC,QAAhB,EAA0BxC,oBAA1B,EAAgDQ,oBAAhD,EAAsE,UAAUoE,KAAV,EAAiB;EACrF,EAAA,MAAMM,MAAM,GAAGqB,4BAAsB,CAAC,IAAD,CAArC,CAAA;;IAEA,IAAI,CAAC,GAAD,EAAM,MAAN,CAAA,CAAcC,QAAd,CAAuB,IAAA,CAAKC,OAA5B,CAAJ,EAA0C;EACxC7B,IAAAA,KAAK,CAACE,cAAN,EAAA,CAAA;EACD,GAAA;;IAEDzC,6BAAY,CAAC2C,GAAb,CAAiBE,MAAjB,EAAyBxF,UAAzB,EAAqC0C,SAAS,IAAI;MAChD,IAAIA,SAAS,CAACG,gBAAd,EAAgC;EAC9B;EACA,MAAA,OAAA;EACD,KAAA;;EAEDF,IAAAA,6BAAY,CAAC2C,GAAb,CAAiBE,MAAjB,EAAyBzF,YAAzB,EAAuC,MAAM;EAC3C,MAAA,IAAIiE,eAAS,CAAC,IAAD,CAAb,EAAqB;EACnB,QAAA,IAAA,CAAK/C,KAAL,EAAA,CAAA;EACD,OAAA;OAHH,CAAA,CAAA;EAKD,GAXD,EAPqF;;EAqBrF,EAAA,MAAM+F,WAAW,GAAGtF,+BAAc,CAACC,OAAf,CAAuBhB,aAAvB,CAApB,CAAA;;EACA,EAAA,IAAIqG,WAAJ,EAAiB;EACf5F,IAAAA,KAAK,CAAC6F,WAAN,CAAkBD,WAAlB,EAA+BxE,IAA/B,EAAA,CAAA;EACD,GAAA;;EAED,EAAA,MAAMkE,IAAI,GAAGtF,KAAK,CAACuF,mBAAN,CAA0BnB,MAA1B,CAAb,CAAA;IAEAkB,IAAI,CAACpE,MAAL,CAAY,IAAZ,CAAA,CAAA;EACD,CA7BD,CAAA,CAAA;AA+BA4E,yCAAoB,CAAC9F,KAAD,CAApB,CAAA;EAEA;EACA;EACA;;AAEA+F,0BAAkB,CAAC/F,KAAD,CAAlB;;;;;;;;"}