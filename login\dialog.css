#dialog{
  display: block;
}
.dialog-box {
    position:fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 1rem 3rem;
    background-color: white;
    width: 65%;
    padding-top: 2rem;
    border-radius: 20px;
    border: 0;
    box-shadow: 0 5px 30px 0 rgb(0 0 0 / 10%);
    animation: fadeIn 1s ease both;
    z-index: 9999;
    font-family: 'Franklin Gothic Medium', 'Arial Narrow', Arial, sans-serif;
  }
  
  .dialog-box::backdrop {
    animation: fadeIn 1s ease both;
    background: rgb(255 255 255 / 40%);
    z-index: 2;
    backdrop-filter: blur(20px);
  }
  
  .dialog-box .x {
    filter: grayscale(1);
    border: none;
    background: none;
    position: absolute;
    top: 15px;
    right: 10px;
    transition: ease filter, transform 0.3s;
    cursor: pointer;
    transform-origin: center;
  }
  
  .dialog-box .x:hover {
    filter: grayscale(0);
    transform: scale(1.1);
  }
  
  .dialog-box h2 {
    color: #240046;
    font-weight: 600;
    font-size: 2rem;
    padding-bottom: 1rem;
    
  }
  
  .dialog-box p {
    font-size: 1rem;
    line-height: 1.3rem;
    padding: 0.5rem 0;
  }
  
  .dialog-box p a:visited {
    color: rgb(var(--vs-primary));
  }
  
  .dialog-box img{
    height: 5%;
    width: 5%;
  }
  
  
  .dialog-box div {
    display: flex;
    margin-top: 2%;
    
  }