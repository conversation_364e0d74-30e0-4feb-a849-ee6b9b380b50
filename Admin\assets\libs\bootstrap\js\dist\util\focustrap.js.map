{"version": 3, "file": "focustrap.js", "sources": ["../../src/util/focustrap.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): util/focustrap.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport SelectorEngine from '../dom/selector-engine'\nimport Config from './config'\n\n/**\n * Constants\n */\n\nconst NAME = 'focustrap'\nconst DATA_KEY = 'bs.focustrap'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_KEYDOWN_TAB = `keydown.tab${EVENT_KEY}`\n\nconst TAB_KEY = 'Tab'\nconst TAB_NAV_FORWARD = 'forward'\nconst TAB_NAV_BACKWARD = 'backward'\n\nconst Default = {\n  autofocus: true,\n  trapElement: null // The element to trap focus inside of\n}\n\nconst DefaultType = {\n  autofocus: 'boolean',\n  trapElement: 'element'\n}\n\n/**\n * Class definition\n */\n\nclass FocusTrap extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isActive = false\n    this._lastTabNavDirection = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  activate() {\n    if (this._isActive) {\n      return\n    }\n\n    if (this._config.autofocus) {\n      this._config.trapElement.focus()\n    }\n\n    EventHandler.off(document, EVENT_KEY) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => this._handleFocusin(event))\n    EventHandler.on(document, EVENT_KEYDOWN_TAB, event => this._handleKeydown(event))\n\n    this._isActive = true\n  }\n\n  deactivate() {\n    if (!this._isActive) {\n      return\n    }\n\n    this._isActive = false\n    EventHandler.off(document, EVENT_KEY)\n  }\n\n  // Private\n  _handleFocusin(event) {\n    const { trapElement } = this._config\n\n    if (event.target === document || event.target === trapElement || trapElement.contains(event.target)) {\n      return\n    }\n\n    const elements = SelectorEngine.focusableChildren(trapElement)\n\n    if (elements.length === 0) {\n      trapElement.focus()\n    } else if (this._lastTabNavDirection === TAB_NAV_BACKWARD) {\n      elements[elements.length - 1].focus()\n    } else {\n      elements[0].focus()\n    }\n  }\n\n  _handleKeydown(event) {\n    if (event.key !== TAB_KEY) {\n      return\n    }\n\n    this._lastTabNavDirection = event.shiftKey ? TAB_NAV_BACKWARD : TAB_NAV_FORWARD\n  }\n}\n\nexport default FocusTrap\n"], "names": ["NAME", "DATA_KEY", "EVENT_KEY", "EVENT_FOCUSIN", "EVENT_KEYDOWN_TAB", "TAB_KEY", "TAB_NAV_FORWARD", "TAB_NAV_BACKWARD", "<PERSON><PERSON><PERSON>", "autofocus", "trapElement", "DefaultType", "FocusTrap", "Config", "constructor", "config", "_config", "_getConfig", "_isActive", "_lastTabNavDirection", "activate", "focus", "EventHandler", "off", "document", "on", "event", "_handleFocusin", "_handleKeydown", "deactivate", "target", "contains", "elements", "SelectorEngine", "focusableC<PERSON><PERSON>n", "length", "key", "shift<PERSON>ey"], "mappings": ";;;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAMA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,WAAb,CAAA;EACA,MAAMC,QAAQ,GAAG,cAAjB,CAAA;EACA,MAAMC,SAAS,GAAI,CAAGD,CAAAA,EAAAA,QAAS,CAA/B,CAAA,CAAA;EACA,MAAME,aAAa,GAAI,CAASD,OAAAA,EAAAA,SAAU,CAA1C,CAAA,CAAA;EACA,MAAME,iBAAiB,GAAI,CAAaF,WAAAA,EAAAA,SAAU,CAAlD,CAAA,CAAA;EAEA,MAAMG,OAAO,GAAG,KAAhB,CAAA;EACA,MAAMC,eAAe,GAAG,SAAxB,CAAA;EACA,MAAMC,gBAAgB,GAAG,UAAzB,CAAA;EAEA,MAAMC,OAAO,GAAG;EACdC,EAAAA,SAAS,EAAE,IADG;IAEdC,WAAW,EAAE,IAFC;;EAAA,CAAhB,CAAA;EAKA,MAAMC,WAAW,GAAG;EAClBF,EAAAA,SAAS,EAAE,SADO;EAElBC,EAAAA,WAAW,EAAE,SAAA;EAFK,CAApB,CAAA;EAKA;EACA;EACA;;EAEA,MAAME,SAAN,SAAwBC,uBAAxB,CAA+B;IAC7BC,WAAW,CAACC,MAAD,EAAS;EAClB,IAAA,KAAA,EAAA,CAAA;EACA,IAAA,IAAA,CAAKC,OAAL,GAAe,IAAA,CAAKC,UAAL,CAAgBF,MAAhB,CAAf,CAAA;MACA,IAAKG,CAAAA,SAAL,GAAiB,KAAjB,CAAA;MACA,IAAKC,CAAAA,oBAAL,GAA4B,IAA5B,CAAA;EACD,GAN4B;;;EASX,EAAA,WAAPX,OAAO,GAAG;EACnB,IAAA,OAAOA,OAAP,CAAA;EACD,GAAA;;EAEqB,EAAA,WAAXG,WAAW,GAAG;EACvB,IAAA,OAAOA,WAAP,CAAA;EACD,GAAA;;EAEc,EAAA,WAAJX,IAAI,GAAG;EAChB,IAAA,OAAOA,IAAP,CAAA;EACD,GAnB4B;;;EAsB7BoB,EAAAA,QAAQ,GAAG;MACT,IAAI,IAAA,CAAKF,SAAT,EAAoB;EAClB,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAI,IAAKF,CAAAA,OAAL,CAAaP,SAAjB,EAA4B;EAC1B,MAAA,IAAA,CAAKO,OAAL,CAAaN,WAAb,CAAyBW,KAAzB,EAAA,CAAA;EACD,KAAA;;EAEDC,IAAAA,6BAAY,CAACC,GAAb,CAAiBC,QAAjB,EAA2BtB,SAA3B,EATS;;EAUToB,IAAAA,6BAAY,CAACG,EAAb,CAAgBD,QAAhB,EAA0BrB,aAA1B,EAAyCuB,KAAK,IAAI,IAAA,CAAKC,cAAL,CAAoBD,KAApB,CAAlD,CAAA,CAAA;EACAJ,IAAAA,6BAAY,CAACG,EAAb,CAAgBD,QAAhB,EAA0BpB,iBAA1B,EAA6CsB,KAAK,IAAI,IAAA,CAAKE,cAAL,CAAoBF,KAApB,CAAtD,CAAA,CAAA;MAEA,IAAKR,CAAAA,SAAL,GAAiB,IAAjB,CAAA;EACD,GAAA;;EAEDW,EAAAA,UAAU,GAAG;MACX,IAAI,CAAC,IAAKX,CAAAA,SAAV,EAAqB;EACnB,MAAA,OAAA;EACD,KAAA;;MAED,IAAKA,CAAAA,SAAL,GAAiB,KAAjB,CAAA;EACAI,IAAAA,6BAAY,CAACC,GAAb,CAAiBC,QAAjB,EAA2BtB,SAA3B,CAAA,CAAA;EACD,GA7C4B;;;IAgD7ByB,cAAc,CAACD,KAAD,EAAQ;MACpB,MAAM;EAAEhB,MAAAA,WAAAA;EAAF,KAAA,GAAkB,KAAKM,OAA7B,CAAA;;MAEA,IAAIU,KAAK,CAACI,MAAN,KAAiBN,QAAjB,IAA6BE,KAAK,CAACI,MAAN,KAAiBpB,WAA9C,IAA6DA,WAAW,CAACqB,QAAZ,CAAqBL,KAAK,CAACI,MAA3B,CAAjE,EAAqG;EACnG,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,MAAME,QAAQ,GAAGC,+BAAc,CAACC,iBAAf,CAAiCxB,WAAjC,CAAjB,CAAA;;EAEA,IAAA,IAAIsB,QAAQ,CAACG,MAAT,KAAoB,CAAxB,EAA2B;EACzBzB,MAAAA,WAAW,CAACW,KAAZ,EAAA,CAAA;EACD,KAFD,MAEO,IAAI,IAAA,CAAKF,oBAAL,KAA8BZ,gBAAlC,EAAoD;QACzDyB,QAAQ,CAACA,QAAQ,CAACG,MAAT,GAAkB,CAAnB,CAAR,CAA8Bd,KAA9B,EAAA,CAAA;EACD,KAFM,MAEA;EACLW,MAAAA,QAAQ,CAAC,CAAD,CAAR,CAAYX,KAAZ,EAAA,CAAA;EACD,KAAA;EACF,GAAA;;IAEDO,cAAc,CAACF,KAAD,EAAQ;EACpB,IAAA,IAAIA,KAAK,CAACU,GAAN,KAAc/B,OAAlB,EAA2B;EACzB,MAAA,OAAA;EACD,KAAA;;MAED,IAAKc,CAAAA,oBAAL,GAA4BO,KAAK,CAACW,QAAN,GAAiB9B,gBAAjB,GAAoCD,eAAhE,CAAA;EACD,GAAA;;EAxE4B;;;;;;;;"}