/********************************************
 * REVOLUTION 5.0 EXTENSION - KEN BURN
 * @version: 1.2 (2.11.2016)
 * @requires jquery.themepunch.revolution.js
 * <AUTHOR>
*********************************************/
!function(a){"use strict";var b=jQuery.fn.revolution,c={alias:"KenBurns Min JS",name:"revolution.extensions.kenburn.min.js",min_core:"5.0",version:"1.2.0"};jQuery.extend(!0,b,{stopKenBurn:function(a){return"stop"!==b.compare_version(c).check&&void(void 0!=a.data("kbtl")&&a.data("kbtl").pause())},startKenBurn:function(a,d,e){if("stop"===b.compare_version(c).check)return!1;var f=a.data(),g=a.find(".defaultimg"),h=g.data("lazyload")||g.data("src"),j=(f.owidth/f.oheight,"carousel"===d.sliderType?d.carousel.slide_width:d.ul.width()),k=d.ul.height();a.data("kbtl")&&a.data("kbtl").kill(),e=e||0,0==a.find(".tp-kbimg").length&&(a.append('<div class="tp-kbimg-wrap" style="z-index:2;width:100%;height:100%;top:0px;left:0px;position:absolute;"><img class="tp-kbimg" src="'+h+'" style="position:absolute;" width="'+f.owidth+'" height="'+f.oheight+'"></div>'),a.data("kenburn",a.find(".tp-kbimg")));var m=function(a,b,c,d,e,f,g){var h=a*c,i=b*c,j=Math.abs(d-h),k=Math.abs(e-i),l=new Object;return l.l=(0-f)*j,l.r=l.l+h,l.t=(0-g)*k,l.b=l.t+i,l.h=f,l.v=g,l},n=function(a,b,c,d,e){var f=a.bgposition.split(" ")||"center center",g="center"==f[0]?"50%":"left"==f[0]||"left"==f[1]?"0%":"right"==f[0]||"right"==f[1]?"100%":f[0],h="center"==f[1]?"50%":"top"==f[0]||"top"==f[1]?"0%":"bottom"==f[0]||"bottom"==f[1]?"100%":f[1];g=parseInt(g,0)/100||0,h=parseInt(h,0)/100||0;var i=new Object;return i.start=m(e.start.width,e.start.height,e.start.scale,b,c,g,h),i.end=m(e.start.width,e.start.height,e.end.scale,b,c,g,h),i},o=function(a,b,c){var d=c.scalestart/100,e=c.scaleend/100,f=void 0!=c.offsetstart?c.offsetstart.split(" ")||[0,0]:[0,0],g=void 0!=c.offsetend?c.offsetend.split(" ")||[0,0]:[0,0];c.bgposition="center center"==c.bgposition?"50% 50%":c.bgposition;var h=new Object,i=a*d,k=(i/c.owidth*c.oheight,a*e);k/c.owidth*c.oheight;if(h.start=new Object,h.starto=new Object,h.end=new Object,h.endo=new Object,h.start.width=a,h.start.height=h.start.width/c.owidth*c.oheight,h.start.height<b){var m=b/h.start.height;h.start.height=b,h.start.width=h.start.width*m}h.start.transformOrigin=c.bgposition,h.start.scale=d,h.end.scale=e,c.rotatestart=0===c.rotatestart?.01:c.rotatestart,h.start.rotation=c.rotatestart+"deg",h.end.rotation=c.rotateend+"deg";var o=n(c,a,b,f,h);f[0]=parseFloat(f[0])+o.start.l,g[0]=parseFloat(g[0])+o.end.l,f[1]=parseFloat(f[1])+o.start.t,g[1]=parseFloat(g[1])+o.end.t;var p=o.start.r-o.start.l,q=o.start.b-o.start.t,r=o.end.r-o.end.l,s=o.end.b-o.end.t;return f[0]=f[0]>0?0:p+f[0]<a?a-p:f[0],g[0]=g[0]>0?0:r+g[0]<a?a-r:g[0],f[1]=f[1]>0?0:q+f[1]<b?b-q:f[1],g[1]=g[1]>0?0:s+g[1]<b?b-s:g[1],h.starto.x=f[0]+"px",h.starto.y=f[1]+"px",h.endo.x=g[0]+"px",h.endo.y=g[1]+"px",h.end.ease=h.endo.ease=c.ease,h.end.force3D=h.endo.force3D=!0,h};void 0!=a.data("kbtl")&&(a.data("kbtl").kill(),a.removeData("kbtl"));var p=a.data("kenburn"),q=p.parent(),r=o(j,k,f),s=new punchgs.TimelineLite;s.pause(),r.start.transformOrigin="0% 0%",r.starto.transformOrigin="0% 0%",s.add(punchgs.TweenLite.fromTo(p,f.duration/1e3,r.start,r.end),0),s.add(punchgs.TweenLite.fromTo(q,f.duration/1e3,r.starto,r.endo),0),s.progress(e),s.play(),a.data("kbtl",s)}})}(jQuery);