{"version": 3, "file": "dropdown.js", "sources": ["../src/dropdown.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getNextActiveElement,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_DROPUP_CENTER = 'dropup-center'\nconst CLASS_NAME_DROPDOWN_CENTER = 'dropdown-center'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]:not(.disabled):not(:disabled)'\nconst SELECTOR_DATA_TOGGLE_SHOWN = `${SELECTOR_DATA_TOGGLE}.${CLASS_NAME_SHOW}`\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR = '.navbar'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\nconst PLACEMENT_TOPCENTER = 'top'\nconst PLACEMENT_BOTTOMCENTER = 'bottom'\n\nconst Default = {\n  autoClose: true,\n  boundary: 'clippingParents',\n  display: 'dynamic',\n  offset: [0, 2],\n  popperConfig: null,\n  reference: 'toggle'\n}\n\nconst DefaultType = {\n  autoClose: '(boolean|string)',\n  boundary: '(string|element)',\n  display: 'string',\n  offset: '(array|string|function)',\n  popperConfig: '(null|object|function)',\n  reference: '(string|element|object)'\n}\n\n/**\n * Class definition\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._popper = null\n    this._parent = this._element.parentNode // dropdown wrapper\n    // todo: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.2/forms/input-group/\n    this._menu = SelectorEngine.next(this._element, SELECTOR_MENU)[0] ||\n      SelectorEngine.prev(this._element, SELECTOR_MENU)[0] ||\n      SelectorEngine.findOne(SELECTOR_MENU, this._parent)\n    this._inNavbar = this._detectNavbar()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    return this._isShown() ? this.hide() : this.show()\n  }\n\n  show() {\n    if (isDisabled(this._element) || this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._createPopper()\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement && !this._parent.closest(SELECTOR_NAVBAR_NAV)) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.add(CLASS_NAME_SHOW)\n    this._element.classList.add(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (isDisabled(this._element) || !this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    this._completeHide(relatedTarget)\n  }\n\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.remove(CLASS_NAME_SHOW)\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._element.setAttribute('aria-expanded', 'false')\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  _getConfig(config) {\n    config = super._getConfig(config)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _createPopper() {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n    }\n\n    let referenceElement = this._element\n\n    if (this._config.reference === 'parent') {\n      referenceElement = this._parent\n    } else if (isElement(this._config.reference)) {\n      referenceElement = getElement(this._config.reference)\n    } else if (typeof this._config.reference === 'object') {\n      referenceElement = this._config.reference\n    }\n\n    const popperConfig = this._getPopperConfig()\n    this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n  }\n\n  _isShown() {\n    return this._menu.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._parent\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP_CENTER)) {\n      return PLACEMENT_TOPCENTER\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPDOWN_CENTER)) {\n      return PLACEMENT_BOTTOMCENTER\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(SELECTOR_NAVBAR) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display or Dropdown is in Navbar\n    if (this._inNavbar || this._config.display === 'static') {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'static') // todo:v6 remove\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _selectMenuItem({ key, target }) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(element => isVisible(element))\n\n    if (!items.length) {\n      return\n    }\n\n    // if target isn't included in items (e.g. when expanding the dropdown)\n    // allow cycling to get the last item in case key equals ARROW_UP_KEY\n    getNextActiveElement(items, target, key === ARROW_DOWN_KEY, !items.includes(target)).focus()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Dropdown.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n\n  static clearMenus(event) {\n    if (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY)) {\n      return\n    }\n\n    const openToggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE_SHOWN)\n\n    for (const toggle of openToggles) {\n      const context = Dropdown.getInstance(toggle)\n      if (!context || context._config.autoClose === false) {\n        continue\n      }\n\n      const composedPath = event.composedPath()\n      const isMenuTarget = composedPath.includes(context._menu)\n      if (\n        composedPath.includes(context._element) ||\n        (context._config.autoClose === 'inside' && !isMenuTarget) ||\n        (context._config.autoClose === 'outside' && isMenuTarget)\n      ) {\n        continue\n      }\n\n      // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n      if (context._menu.contains(event.target) && ((event.type === 'keyup' && event.key === TAB_KEY) || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n        continue\n      }\n\n      const relatedTarget = { relatedTarget: context._element }\n\n      if (event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      context._completeHide(relatedTarget)\n    }\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not an UP | DOWN | ESCAPE key => not a dropdown command\n    // If input/textarea && if key is other than ESCAPE => not a dropdown command\n\n    const isInput = /input|textarea/i.test(event.target.tagName)\n    const isEscapeEvent = event.key === ESCAPE_KEY\n    const isUpOrDownEvent = [ARROW_UP_KEY, ARROW_DOWN_KEY].includes(event.key)\n\n    if (!isUpOrDownEvent && !isEscapeEvent) {\n      return\n    }\n\n    if (isInput && !isEscapeEvent) {\n      return\n    }\n\n    event.preventDefault()\n\n    // todo: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.2/forms/input-group/\n    const getToggleButton = this.matches(SELECTOR_DATA_TOGGLE) ?\n      this :\n      (SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0] ||\n        SelectorEngine.next(this, SELECTOR_DATA_TOGGLE)[0] ||\n        SelectorEngine.findOne(SELECTOR_DATA_TOGGLE, event.delegateTarget.parentNode))\n\n    const instance = Dropdown.getOrCreateInstance(getToggleButton)\n\n    if (isUpOrDownEvent) {\n      event.stopPropagation()\n      instance.show()\n      instance._selectMenuItem(event)\n      return\n    }\n\n    if (instance._isShown()) { // else is escape and we check if it is shown\n      event.stopPropagation()\n      instance.hide()\n      getToggleButton.focus()\n    }\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.getOrCreateInstance(this).toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Dropdown)\n\nexport default Dropdown\n"], "names": ["NAME", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "ESCAPE_KEY", "TAB_KEY", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "RIGHT_MOUSE_BUTTON", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_CLICK_DATA_API", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "CLASS_NAME_SHOW", "CLASS_NAME_DROPUP", "CLASS_NAME_DROPEND", "CLASS_NAME_DROPSTART", "CLASS_NAME_DROPUP_CENTER", "CLASS_NAME_DROPDOWN_CENTER", "SELECTOR_DATA_TOGGLE", "SELECTOR_DATA_TOGGLE_SHOWN", "SELECTOR_MENU", "SELECTOR_NAVBAR", "SELECTOR_NAVBAR_NAV", "SELECTOR_VISIBLE_ITEMS", "PLACEMENT_TOP", "isRTL", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "PLACEMENT_TOPCENTER", "PLACEMENT_BOTTOMCENTER", "<PERSON><PERSON><PERSON>", "autoClose", "boundary", "display", "offset", "popperConfig", "reference", "DefaultType", "Dropdown", "BaseComponent", "constructor", "element", "config", "_popper", "_parent", "_element", "parentNode", "_menu", "SelectorEngine", "next", "prev", "findOne", "_inNavbar", "_detectNavbar", "toggle", "_isShown", "hide", "show", "isDisabled", "relatedTarget", "showEvent", "EventHandler", "trigger", "defaultPrevented", "_createPopper", "document", "documentElement", "closest", "concat", "body", "children", "on", "noop", "focus", "setAttribute", "classList", "add", "_completeHide", "dispose", "destroy", "update", "hideEvent", "off", "remove", "Manipulator", "removeDataAttribute", "_getConfig", "isElement", "getBoundingClientRect", "TypeError", "toUpperCase", "<PERSON><PERSON>", "referenceElement", "_config", "getElement", "_getPopperConfig", "createPopper", "contains", "_getPlacement", "parentDropdown", "isEnd", "getComputedStyle", "getPropertyValue", "trim", "_getOffset", "split", "map", "value", "Number", "parseInt", "popperData", "defaultBsPopperConfig", "placement", "modifiers", "name", "options", "setDataAttribute", "enabled", "_selectMenuItem", "key", "target", "items", "find", "filter", "isVisible", "length", "getNextActiveElement", "includes", "jQueryInterface", "each", "data", "getOrCreateInstance", "clearMenus", "event", "button", "type", "openToggles", "context", "getInstance", "<PERSON><PERSON><PERSON>", "isMenuTarget", "test", "tagName", "clickEvent", "dataApiKeydownHandler", "isInput", "isEscapeEvent", "isUpOrDownEvent", "preventDefault", "getToggleButton", "matches", "<PERSON><PERSON><PERSON><PERSON>", "instance", "stopPropagation", "defineJQueryPlugin"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAkBA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,UAAb,CAAA;EACA,MAAMC,QAAQ,GAAG,aAAjB,CAAA;EACA,MAAMC,SAAS,GAAI,CAAGD,CAAAA,EAAAA,QAAS,CAA/B,CAAA,CAAA;EACA,MAAME,YAAY,GAAG,WAArB,CAAA;EAEA,MAAMC,UAAU,GAAG,QAAnB,CAAA;EACA,MAAMC,OAAO,GAAG,KAAhB,CAAA;EACA,MAAMC,YAAY,GAAG,SAArB,CAAA;EACA,MAAMC,cAAc,GAAG,WAAvB,CAAA;EACA,MAAMC,kBAAkB,GAAG,CAA3B;;EAEA,MAAMC,UAAU,GAAI,CAAMP,IAAAA,EAAAA,SAAU,CAApC,CAAA,CAAA;EACA,MAAMQ,YAAY,GAAI,CAAQR,MAAAA,EAAAA,SAAU,CAAxC,CAAA,CAAA;EACA,MAAMS,UAAU,GAAI,CAAMT,IAAAA,EAAAA,SAAU,CAApC,CAAA,CAAA;EACA,MAAMU,WAAW,GAAI,CAAOV,KAAAA,EAAAA,SAAU,CAAtC,CAAA,CAAA;EACA,MAAMW,oBAAoB,GAAI,CAAA,KAAA,EAAOX,SAAU,CAAA,EAAEC,YAAa,CAA9D,CAAA,CAAA;EACA,MAAMW,sBAAsB,GAAI,CAAA,OAAA,EAASZ,SAAU,CAAA,EAAEC,YAAa,CAAlE,CAAA,CAAA;EACA,MAAMY,oBAAoB,GAAI,CAAA,KAAA,EAAOb,SAAU,CAAA,EAAEC,YAAa,CAA9D,CAAA,CAAA;EAEA,MAAMa,eAAe,GAAG,MAAxB,CAAA;EACA,MAAMC,iBAAiB,GAAG,QAA1B,CAAA;EACA,MAAMC,kBAAkB,GAAG,SAA3B,CAAA;EACA,MAAMC,oBAAoB,GAAG,WAA7B,CAAA;EACA,MAAMC,wBAAwB,GAAG,eAAjC,CAAA;EACA,MAAMC,0BAA0B,GAAG,iBAAnC,CAAA;EAEA,MAAMC,oBAAoB,GAAG,2DAA7B,CAAA;EACA,MAAMC,0BAA0B,GAAI,CAAA,EAAED,oBAAqB,CAAA,CAAA,EAAGN,eAAgB,CAA9E,CAAA,CAAA;EACA,MAAMQ,aAAa,GAAG,gBAAtB,CAAA;EACA,MAAMC,eAAe,GAAG,SAAxB,CAAA;EACA,MAAMC,mBAAmB,GAAG,aAA5B,CAAA;EACA,MAAMC,sBAAsB,GAAG,6DAA/B,CAAA;EAEA,MAAMC,aAAa,GAAGC,WAAK,EAAK,GAAA,SAAL,GAAiB,WAA5C,CAAA;EACA,MAAMC,gBAAgB,GAAGD,WAAK,EAAK,GAAA,WAAL,GAAmB,SAAjD,CAAA;EACA,MAAME,gBAAgB,GAAGF,WAAK,EAAK,GAAA,YAAL,GAAoB,cAAlD,CAAA;EACA,MAAMG,mBAAmB,GAAGH,WAAK,EAAK,GAAA,cAAL,GAAsB,YAAvD,CAAA;EACA,MAAMI,eAAe,GAAGJ,WAAK,EAAK,GAAA,YAAL,GAAoB,aAAjD,CAAA;EACA,MAAMK,cAAc,GAAGL,WAAK,EAAK,GAAA,aAAL,GAAqB,YAAjD,CAAA;EACA,MAAMM,mBAAmB,GAAG,KAA5B,CAAA;EACA,MAAMC,sBAAsB,GAAG,QAA/B,CAAA;EAEA,MAAMC,OAAO,GAAG;EACdC,EAAAA,SAAS,EAAE,IADG;EAEdC,EAAAA,QAAQ,EAAE,iBAFI;EAGdC,EAAAA,OAAO,EAAE,SAHK;EAIdC,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CAJM;EAKdC,EAAAA,YAAY,EAAE,IALA;EAMdC,EAAAA,SAAS,EAAE,QAAA;EANG,CAAhB,CAAA;EASA,MAAMC,WAAW,GAAG;EAClBN,EAAAA,SAAS,EAAE,kBADO;EAElBC,EAAAA,QAAQ,EAAE,kBAFQ;EAGlBC,EAAAA,OAAO,EAAE,QAHS;EAIlBC,EAAAA,MAAM,EAAE,yBAJU;EAKlBC,EAAAA,YAAY,EAAE,wBALI;EAMlBC,EAAAA,SAAS,EAAE,yBAAA;EANO,CAApB,CAAA;EASA;EACA;EACA;;EAEA,MAAME,QAAN,SAAuBC,8BAAvB,CAAqC;EACnCC,EAAAA,WAAW,CAACC,OAAD,EAAUC,MAAV,EAAkB;MAC3B,KAAMD,CAAAA,OAAN,EAAeC,MAAf,CAAA,CAAA;MAEA,IAAKC,CAAAA,OAAL,GAAe,IAAf,CAAA;EACA,IAAA,IAAA,CAAKC,OAAL,GAAe,IAAA,CAAKC,QAAL,CAAcC,UAA7B,CAJ2B;EAK3B;;EACA,IAAA,IAAA,CAAKC,KAAL,GAAaC,+BAAc,CAACC,IAAf,CAAoB,IAAA,CAAKJ,QAAzB,EAAmC5B,aAAnC,CAAA,CAAkD,CAAlD,CAAA,IACX+B,+BAAc,CAACE,IAAf,CAAoB,IAAA,CAAKL,QAAzB,EAAmC5B,aAAnC,CAAA,CAAkD,CAAlD,CADW,IAEX+B,+BAAc,CAACG,OAAf,CAAuBlC,aAAvB,EAAsC,IAAA,CAAK2B,OAA3C,CAFF,CAAA;EAGA,IAAA,IAAA,CAAKQ,SAAL,GAAiB,IAAKC,CAAAA,aAAL,EAAjB,CAAA;EACD,GAXkC;;;EAcjB,EAAA,WAAPvB,OAAO,GAAG;EACnB,IAAA,OAAOA,OAAP,CAAA;EACD,GAAA;;EAEqB,EAAA,WAAXO,WAAW,GAAG;EACvB,IAAA,OAAOA,WAAP,CAAA;EACD,GAAA;;EAEc,EAAA,WAAJ5C,IAAI,GAAG;EAChB,IAAA,OAAOA,IAAP,CAAA;EACD,GAxBkC;;;EA2BnC6D,EAAAA,MAAM,GAAG;MACP,OAAO,IAAA,CAAKC,QAAL,EAAkB,GAAA,IAAA,CAAKC,IAAL,EAAlB,GAAgC,IAAKC,CAAAA,IAAL,EAAvC,CAAA;EACD,GAAA;;EAEDA,EAAAA,IAAI,GAAG;MACL,IAAIC,gBAAU,CAAC,IAAKb,CAAAA,QAAN,CAAV,IAA6B,IAAA,CAAKU,QAAL,EAAjC,EAAkD;EAChD,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,MAAMI,aAAa,GAAG;EACpBA,MAAAA,aAAa,EAAE,IAAKd,CAAAA,QAAAA;OADtB,CAAA;EAIA,IAAA,MAAMe,SAAS,GAAGC,6BAAY,CAACC,OAAb,CAAqB,IAAKjB,CAAAA,QAA1B,EAAoCzC,UAApC,EAAgDuD,aAAhD,CAAlB,CAAA;;MAEA,IAAIC,SAAS,CAACG,gBAAd,EAAgC;EAC9B,MAAA,OAAA;EACD,KAAA;;MAED,IAAKC,CAAAA,aAAL,GAfK;EAkBL;EACA;EACA;;;EACA,IAAA,IAAI,cAAkBC,IAAAA,QAAQ,CAACC,eAA3B,IAA8C,CAAC,IAAKtB,CAAAA,OAAL,CAAauB,OAAb,CAAqBhD,mBAArB,CAAnD,EAA8F;EAC5F,MAAA,KAAK,MAAMsB,OAAX,IAAsB,EAAA,CAAG2B,MAAH,CAAU,GAAGH,QAAQ,CAACI,IAAT,CAAcC,QAA3B,CAAtB,EAA4D;EAC1DT,QAAAA,6BAAY,CAACU,EAAb,CAAgB9B,OAAhB,EAAyB,WAAzB,EAAsC+B,UAAtC,CAAA,CAAA;EACD,OAAA;EACF,KAAA;;MAED,IAAK3B,CAAAA,QAAL,CAAc4B,KAAd,EAAA,CAAA;;EACA,IAAA,IAAA,CAAK5B,QAAL,CAAc6B,YAAd,CAA2B,eAA3B,EAA4C,IAA5C,CAAA,CAAA;;EAEA,IAAA,IAAA,CAAK3B,KAAL,CAAW4B,SAAX,CAAqBC,GAArB,CAAyBnE,eAAzB,CAAA,CAAA;;EACA,IAAA,IAAA,CAAKoC,QAAL,CAAc8B,SAAd,CAAwBC,GAAxB,CAA4BnE,eAA5B,CAAA,CAAA;;MACAoD,6BAAY,CAACC,OAAb,CAAqB,IAAA,CAAKjB,QAA1B,EAAoCxC,WAApC,EAAiDsD,aAAjD,CAAA,CAAA;EACD,GAAA;;EAEDH,EAAAA,IAAI,GAAG;MACL,IAAIE,gBAAU,CAAC,IAAA,CAAKb,QAAN,CAAV,IAA6B,CAAC,IAAA,CAAKU,QAAL,EAAlC,EAAmD;EACjD,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,MAAMI,aAAa,GAAG;EACpBA,MAAAA,aAAa,EAAE,IAAKd,CAAAA,QAAAA;OADtB,CAAA;;MAIA,IAAKgC,CAAAA,aAAL,CAAmBlB,aAAnB,CAAA,CAAA;EACD,GAAA;;EAEDmB,EAAAA,OAAO,GAAG;MACR,IAAI,IAAA,CAAKnC,OAAT,EAAkB;QAChB,IAAKA,CAAAA,OAAL,CAAaoC,OAAb,EAAA,CAAA;EACD,KAAA;;EAED,IAAA,KAAA,CAAMD,OAAN,EAAA,CAAA;EACD,GAAA;;EAEDE,EAAAA,MAAM,GAAG;EACP,IAAA,IAAA,CAAK5B,SAAL,GAAiB,IAAKC,CAAAA,aAAL,EAAjB,CAAA;;MACA,IAAI,IAAA,CAAKV,OAAT,EAAkB;QAChB,IAAKA,CAAAA,OAAL,CAAaqC,MAAb,EAAA,CAAA;EACD,KAAA;EACF,GA3FkC;;;IA8FnCH,aAAa,CAAClB,aAAD,EAAgB;EAC3B,IAAA,MAAMsB,SAAS,GAAGpB,6BAAY,CAACC,OAAb,CAAqB,IAAKjB,CAAAA,QAA1B,EAAoC3C,UAApC,EAAgDyD,aAAhD,CAAlB,CAAA;;MACA,IAAIsB,SAAS,CAAClB,gBAAd,EAAgC;EAC9B,MAAA,OAAA;EACD,KAJ0B;EAO3B;;;EACA,IAAA,IAAI,cAAkBE,IAAAA,QAAQ,CAACC,eAA/B,EAAgD;EAC9C,MAAA,KAAK,MAAMzB,OAAX,IAAsB,EAAA,CAAG2B,MAAH,CAAU,GAAGH,QAAQ,CAACI,IAAT,CAAcC,QAA3B,CAAtB,EAA4D;EAC1DT,QAAAA,6BAAY,CAACqB,GAAb,CAAiBzC,OAAjB,EAA0B,WAA1B,EAAuC+B,UAAvC,CAAA,CAAA;EACD,OAAA;EACF,KAAA;;MAED,IAAI,IAAA,CAAK7B,OAAT,EAAkB;QAChB,IAAKA,CAAAA,OAAL,CAAaoC,OAAb,EAAA,CAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKhC,KAAL,CAAW4B,SAAX,CAAqBQ,MAArB,CAA4B1E,eAA5B,CAAA,CAAA;;EACA,IAAA,IAAA,CAAKoC,QAAL,CAAc8B,SAAd,CAAwBQ,MAAxB,CAA+B1E,eAA/B,CAAA,CAAA;;EACA,IAAA,IAAA,CAAKoC,QAAL,CAAc6B,YAAd,CAA2B,eAA3B,EAA4C,OAA5C,CAAA,CAAA;;EACAU,IAAAA,4BAAW,CAACC,mBAAZ,CAAgC,IAAKtC,CAAAA,KAArC,EAA4C,QAA5C,CAAA,CAAA;MACAc,6BAAY,CAACC,OAAb,CAAqB,IAAA,CAAKjB,QAA1B,EAAoC1C,YAApC,EAAkDwD,aAAlD,CAAA,CAAA;EACD,GAAA;;IAED2B,UAAU,CAAC5C,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,KAAA,CAAM4C,UAAN,CAAiB5C,MAAjB,CAAT,CAAA;;MAEA,IAAI,OAAOA,MAAM,CAACN,SAAd,KAA4B,QAA5B,IAAwC,CAACmD,eAAS,CAAC7C,MAAM,CAACN,SAAR,CAAlD,IACF,OAAOM,MAAM,CAACN,SAAP,CAAiBoD,qBAAxB,KAAkD,UADpD,EAEE;EACA;QACA,MAAM,IAAIC,SAAJ,CAAe,CAAA,EAAEhG,IAAI,CAACiG,WAAL,EAAmB,CAAA,8FAAA,CAApC,CAAN,CAAA;EACD,KAAA;;EAED,IAAA,OAAOhD,MAAP,CAAA;EACD,GAAA;;EAEDsB,EAAAA,aAAa,GAAG;EACd,IAAA,IAAI,OAAO2B,iBAAP,KAAkB,WAAtB,EAAmC;EACjC,MAAA,MAAM,IAAIF,SAAJ,CAAc,+DAAd,CAAN,CAAA;EACD,KAAA;;MAED,IAAIG,gBAAgB,GAAG,IAAA,CAAK/C,QAA5B,CAAA;;EAEA,IAAA,IAAI,KAAKgD,OAAL,CAAazD,SAAb,KAA2B,QAA/B,EAAyC;QACvCwD,gBAAgB,GAAG,KAAKhD,OAAxB,CAAA;OADF,MAEO,IAAI2C,eAAS,CAAC,KAAKM,OAAL,CAAazD,SAAd,CAAb,EAAuC;EAC5CwD,MAAAA,gBAAgB,GAAGE,gBAAU,CAAC,KAAKD,OAAL,CAAazD,SAAd,CAA7B,CAAA;OADK,MAEA,IAAI,OAAO,IAAA,CAAKyD,OAAL,CAAazD,SAApB,KAAkC,QAAtC,EAAgD;EACrDwD,MAAAA,gBAAgB,GAAG,IAAA,CAAKC,OAAL,CAAazD,SAAhC,CAAA;EACD,KAAA;;EAED,IAAA,MAAMD,YAAY,GAAG,IAAK4D,CAAAA,gBAAL,EAArB,CAAA;;EACA,IAAA,IAAA,CAAKpD,OAAL,GAAegD,iBAAM,CAACK,YAAP,CAAoBJ,gBAApB,EAAsC,IAAK7C,CAAAA,KAA3C,EAAkDZ,YAAlD,CAAf,CAAA;EACD,GAAA;;EAEDoB,EAAAA,QAAQ,GAAG;MACT,OAAO,IAAA,CAAKR,KAAL,CAAW4B,SAAX,CAAqBsB,QAArB,CAA8BxF,eAA9B,CAAP,CAAA;EACD,GAAA;;EAEDyF,EAAAA,aAAa,GAAG;MACd,MAAMC,cAAc,GAAG,IAAA,CAAKvD,OAA5B,CAAA;;MAEA,IAAIuD,cAAc,CAACxB,SAAf,CAAyBsB,QAAzB,CAAkCtF,kBAAlC,CAAJ,EAA2D;EACzD,MAAA,OAAOe,eAAP,CAAA;EACD,KAAA;;MAED,IAAIyE,cAAc,CAACxB,SAAf,CAAyBsB,QAAzB,CAAkCrF,oBAAlC,CAAJ,EAA6D;EAC3D,MAAA,OAAOe,cAAP,CAAA;EACD,KAAA;;MAED,IAAIwE,cAAc,CAACxB,SAAf,CAAyBsB,QAAzB,CAAkCpF,wBAAlC,CAAJ,EAAiE;EAC/D,MAAA,OAAOe,mBAAP,CAAA;EACD,KAAA;;MAED,IAAIuE,cAAc,CAACxB,SAAf,CAAyBsB,QAAzB,CAAkCnF,0BAAlC,CAAJ,EAAmE;EACjE,MAAA,OAAOe,sBAAP,CAAA;EACD,KAjBa;;;EAoBd,IAAA,MAAMuE,KAAK,GAAGC,gBAAgB,CAAC,KAAKtD,KAAN,CAAhB,CAA6BuD,gBAA7B,CAA8C,eAA9C,CAA+DC,CAAAA,IAA/D,OAA0E,KAAxF,CAAA;;MAEA,IAAIJ,cAAc,CAACxB,SAAf,CAAyBsB,QAAzB,CAAkCvF,iBAAlC,CAAJ,EAA0D;EACxD,MAAA,OAAO0F,KAAK,GAAG7E,gBAAH,GAAsBF,aAAlC,CAAA;EACD,KAAA;;EAED,IAAA,OAAO+E,KAAK,GAAG3E,mBAAH,GAAyBD,gBAArC,CAAA;EACD,GAAA;;EAED6B,EAAAA,aAAa,GAAG;EACd,IAAA,OAAO,KAAKR,QAAL,CAAcsB,OAAd,CAAsBjD,eAAtB,MAA2C,IAAlD,CAAA;EACD,GAAA;;EAEDsF,EAAAA,UAAU,GAAG;MACX,MAAM;EAAEtE,MAAAA,MAAAA;EAAF,KAAA,GAAa,KAAK2D,OAAxB,CAAA;;EAEA,IAAA,IAAI,OAAO3D,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,MAAA,OAAOA,MAAM,CAACuE,KAAP,CAAa,GAAb,CAAA,CAAkBC,GAAlB,CAAsBC,KAAK,IAAIC,MAAM,CAACC,QAAP,CAAgBF,KAAhB,EAAuB,EAAvB,CAA/B,CAAP,CAAA;EACD,KAAA;;EAED,IAAA,IAAI,OAAOzE,MAAP,KAAkB,UAAtB,EAAkC;QAChC,OAAO4E,UAAU,IAAI5E,MAAM,CAAC4E,UAAD,EAAa,IAAA,CAAKjE,QAAlB,CAA3B,CAAA;EACD,KAAA;;EAED,IAAA,OAAOX,MAAP,CAAA;EACD,GAAA;;EAED6D,EAAAA,gBAAgB,GAAG;EACjB,IAAA,MAAMgB,qBAAqB,GAAG;QAC5BC,SAAS,EAAE,IAAKd,CAAAA,aAAL,EADiB;EAE5Be,MAAAA,SAAS,EAAE,CAAC;EACVC,QAAAA,IAAI,EAAE,iBADI;EAEVC,QAAAA,OAAO,EAAE;YACPnF,QAAQ,EAAE,IAAK6D,CAAAA,OAAL,CAAa7D,QAAAA;EADhB,SAAA;EAFC,OAAD,EAMX;EACEkF,QAAAA,IAAI,EAAE,QADR;EAEEC,QAAAA,OAAO,EAAE;YACPjF,MAAM,EAAE,KAAKsE,UAAL,EAAA;EADD,SAAA;SARA,CAAA;EAFiB,KAA9B,CADiB;;MAkBjB,IAAI,IAAA,CAAKpD,SAAL,IAAkB,IAAA,CAAKyC,OAAL,CAAa5D,OAAb,KAAyB,QAA/C,EAAyD;QACvDmD,4BAAW,CAACgC,gBAAZ,CAA6B,IAAKrE,CAAAA,KAAlC,EAAyC,QAAzC,EAAmD,QAAnD,CAAA,CADuD;;QAEvDgE,qBAAqB,CAACE,SAAtB,GAAkC,CAAC;EACjCC,QAAAA,IAAI,EAAE,aAD2B;EAEjCG,QAAAA,OAAO,EAAE,KAAA;EAFwB,OAAD,CAAlC,CAAA;EAID,KAAA;;MAED,OAAO,EACL,GAAGN,qBADE;EAEL,MAAA,IAAI,OAAO,IAAKlB,CAAAA,OAAL,CAAa1D,YAApB,KAAqC,UAArC,GAAkD,IAAA,CAAK0D,OAAL,CAAa1D,YAAb,CAA0B4E,qBAA1B,CAAlD,GAAqG,IAAKlB,CAAAA,OAAL,CAAa1D,YAAtH,CAAA;OAFF,CAAA;EAID,GAAA;;EAEDmF,EAAAA,eAAe,CAAC;MAAEC,GAAF;EAAOC,IAAAA,MAAAA;EAAP,GAAD,EAAkB;EAC/B,IAAA,MAAMC,KAAK,GAAGzE,+BAAc,CAAC0E,IAAf,CAAoBtG,sBAApB,EAA4C,IAAA,CAAK2B,KAAjD,CAAwD4E,CAAAA,MAAxD,CAA+DlF,OAAO,IAAImF,eAAS,CAACnF,OAAD,CAAnF,CAAd,CAAA;;EAEA,IAAA,IAAI,CAACgF,KAAK,CAACI,MAAX,EAAmB;EACjB,MAAA,OAAA;EACD,KAL8B;EAQ/B;;;EACAC,IAAAA,0BAAoB,CAACL,KAAD,EAAQD,MAAR,EAAgBD,GAAG,KAAKvH,cAAxB,EAAwC,CAACyH,KAAK,CAACM,QAAN,CAAeP,MAAf,CAAzC,CAApB,CAAqF/C,KAArF,EAAA,CAAA;EACD,GApPkC;;;IAuPb,OAAfuD,eAAe,CAACtF,MAAD,EAAS;MAC7B,OAAO,IAAA,CAAKuF,IAAL,CAAU,YAAY;QAC3B,MAAMC,IAAI,GAAG5F,QAAQ,CAAC6F,mBAAT,CAA6B,IAA7B,EAAmCzF,MAAnC,CAAb,CAAA;;EAEA,MAAA,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,QAAA,OAAA;EACD,OAAA;;EAED,MAAA,IAAI,OAAOwF,IAAI,CAACxF,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,QAAA,MAAM,IAAI+C,SAAJ,CAAe,CAAmB/C,iBAAAA,EAAAA,MAAO,GAAzC,CAAN,CAAA;EACD,OAAA;;QAEDwF,IAAI,CAACxF,MAAD,CAAJ,EAAA,CAAA;EACD,KAZM,CAAP,CAAA;EAaD,GAAA;;IAEgB,OAAV0F,UAAU,CAACC,KAAD,EAAQ;EACvB,IAAA,IAAIA,KAAK,CAACC,MAAN,KAAiBrI,kBAAjB,IAAwCoI,KAAK,CAACE,IAAN,KAAe,OAAf,IAA0BF,KAAK,CAACd,GAAN,KAAczH,OAApF,EAA8F;EAC5F,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,MAAM0I,WAAW,GAAGxF,+BAAc,CAAC0E,IAAf,CAAoB1G,0BAApB,CAApB,CAAA;;EAEA,IAAA,KAAK,MAAMsC,MAAX,IAAqBkF,WAArB,EAAkC;EAChC,MAAA,MAAMC,OAAO,GAAGnG,QAAQ,CAACoG,WAAT,CAAqBpF,MAArB,CAAhB,CAAA;;QACA,IAAI,CAACmF,OAAD,IAAYA,OAAO,CAAC5C,OAAR,CAAgB9D,SAAhB,KAA8B,KAA9C,EAAqD;EACnD,QAAA,SAAA;EACD,OAAA;;EAED,MAAA,MAAM4G,YAAY,GAAGN,KAAK,CAACM,YAAN,EAArB,CAAA;QACA,MAAMC,YAAY,GAAGD,YAAY,CAACZ,QAAb,CAAsBU,OAAO,CAAC1F,KAA9B,CAArB,CAAA;;EACA,MAAA,IACE4F,YAAY,CAACZ,QAAb,CAAsBU,OAAO,CAAC5F,QAA9B,CAAA,IACC4F,OAAO,CAAC5C,OAAR,CAAgB9D,SAAhB,KAA8B,QAA9B,IAA0C,CAAC6G,YAD5C,IAECH,OAAO,CAAC5C,OAAR,CAAgB9D,SAAhB,KAA8B,SAA9B,IAA2C6G,YAH9C,EAIE;EACA,QAAA,SAAA;EACD,OAd+B;;;EAiBhC,MAAA,IAAIH,OAAO,CAAC1F,KAAR,CAAckD,QAAd,CAAuBoC,KAAK,CAACb,MAA7B,CAA0Ca,KAAAA,KAAK,CAACE,IAAN,KAAe,OAAf,IAA0BF,KAAK,CAACd,GAAN,KAAczH,OAAzC,IAAqD,qCAAqC+I,IAArC,CAA0CR,KAAK,CAACb,MAAN,CAAasB,OAAvD,CAA9F,CAAJ,EAAoK;EAClK,QAAA,SAAA;EACD,OAAA;;EAED,MAAA,MAAMnF,aAAa,GAAG;UAAEA,aAAa,EAAE8E,OAAO,CAAC5F,QAAAA;SAA/C,CAAA;;EAEA,MAAA,IAAIwF,KAAK,CAACE,IAAN,KAAe,OAAnB,EAA4B;UAC1B5E,aAAa,CAACoF,UAAd,GAA2BV,KAA3B,CAAA;EACD,OAAA;;QAEDI,OAAO,CAAC5D,aAAR,CAAsBlB,aAAtB,CAAA,CAAA;EACD,KAAA;EACF,GAAA;;IAE2B,OAArBqF,qBAAqB,CAACX,KAAD,EAAQ;EAClC;EACA;MAEA,MAAMY,OAAO,GAAG,iBAAA,CAAkBJ,IAAlB,CAAuBR,KAAK,CAACb,MAAN,CAAasB,OAApC,CAAhB,CAAA;EACA,IAAA,MAAMI,aAAa,GAAGb,KAAK,CAACd,GAAN,KAAc1H,UAApC,CAAA;EACA,IAAA,MAAMsJ,eAAe,GAAG,CAACpJ,YAAD,EAAeC,cAAf,CAA+B+H,CAAAA,QAA/B,CAAwCM,KAAK,CAACd,GAA9C,CAAxB,CAAA;;EAEA,IAAA,IAAI,CAAC4B,eAAD,IAAoB,CAACD,aAAzB,EAAwC;EACtC,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAID,OAAO,IAAI,CAACC,aAAhB,EAA+B;EAC7B,MAAA,OAAA;EACD,KAAA;;MAEDb,KAAK,CAACe,cAAN,EAAA,CAhBkC;;EAmBlC,IAAA,MAAMC,eAAe,GAAG,IAAA,CAAKC,OAAL,CAAavI,oBAAb,IACtB,IADsB,GAErBiC,+BAAc,CAACE,IAAf,CAAoB,IAApB,EAA0BnC,oBAA1B,CAAA,CAAgD,CAAhD,CACCiC,IAAAA,+BAAc,CAACC,IAAf,CAAoB,IAApB,EAA0BlC,oBAA1B,CAAgD,CAAA,CAAhD,CADD,IAECiC,+BAAc,CAACG,OAAf,CAAuBpC,oBAAvB,EAA6CsH,KAAK,CAACkB,cAAN,CAAqBzG,UAAlE,CAJJ,CAAA;EAMA,IAAA,MAAM0G,QAAQ,GAAGlH,QAAQ,CAAC6F,mBAAT,CAA6BkB,eAA7B,CAAjB,CAAA;;EAEA,IAAA,IAAIF,eAAJ,EAAqB;EACnBd,MAAAA,KAAK,CAACoB,eAAN,EAAA,CAAA;EACAD,MAAAA,QAAQ,CAAC/F,IAAT,EAAA,CAAA;;QACA+F,QAAQ,CAAClC,eAAT,CAAyBe,KAAzB,CAAA,CAAA;;EACA,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAImB,QAAQ,CAACjG,QAAT,EAAJ,EAAyB;EAAE;EACzB8E,MAAAA,KAAK,CAACoB,eAAN,EAAA,CAAA;EACAD,MAAAA,QAAQ,CAAChG,IAAT,EAAA,CAAA;EACA6F,MAAAA,eAAe,CAAC5E,KAAhB,EAAA,CAAA;EACD,KAAA;EACF,GAAA;;EApVkC,CAAA;EAuVrC;EACA;EACA;;;AAEAZ,+BAAY,CAACU,EAAb,CAAgBN,QAAhB,EAA0B1D,sBAA1B,EAAkDQ,oBAAlD,EAAwEuB,QAAQ,CAAC0G,qBAAjF,CAAA,CAAA;AACAnF,+BAAY,CAACU,EAAb,CAAgBN,QAAhB,EAA0B1D,sBAA1B,EAAkDU,aAAlD,EAAiEqB,QAAQ,CAAC0G,qBAA1E,CAAA,CAAA;AACAnF,+BAAY,CAACU,EAAb,CAAgBN,QAAhB,EAA0B3D,oBAA1B,EAAgDgC,QAAQ,CAAC8F,UAAzD,CAAA,CAAA;AACAvE,+BAAY,CAACU,EAAb,CAAgBN,QAAhB,EAA0BzD,oBAA1B,EAAgD8B,QAAQ,CAAC8F,UAAzD,CAAA,CAAA;AACAvE,+BAAY,CAACU,EAAb,CAAgBN,QAAhB,EAA0B3D,oBAA1B,EAAgDS,oBAAhD,EAAsE,UAAUsH,KAAV,EAAiB;EACrFA,EAAAA,KAAK,CAACe,cAAN,EAAA,CAAA;EACA9G,EAAAA,QAAQ,CAAC6F,mBAAT,CAA6B,IAA7B,EAAmC7E,MAAnC,EAAA,CAAA;EACD,CAHD,CAAA,CAAA;EAKA;EACA;EACA;;AAEAoG,0BAAkB,CAACpH,QAAD,CAAlB;;;;;;;;"}