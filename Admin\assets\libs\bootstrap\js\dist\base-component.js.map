{"version": 3, "file": "base-component.js", "sources": ["../src/base-component.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data'\nimport { executeAfterTransition, getElement } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Config from './util/config'\n\n/**\n * Constants\n */\n\nconst VERSION = '5.2.3'\n\n/**\n * Class definition\n */\n\nclass BaseComponent extends Config {\n  constructor(element, config) {\n    super()\n\n    element = getElement(element)\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    this._config = this._getConfig(config)\n\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  // Public\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n\n    for (const propertyName of Object.getOwnPropertyNames(this)) {\n      this[propertyName] = null\n    }\n  }\n\n  _queueCallback(callback, element, isAnimated = true) {\n    executeAfterTransition(callback, element, isAnimated)\n  }\n\n  _getConfig(config) {\n    config = this._mergeConfigObj(config, this._element)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  // Static\n  static getInstance(element) {\n    return Data.get(getElement(element), this.DATA_KEY)\n  }\n\n  static getOrCreateInstance(element, config = {}) {\n    return this.getInstance(element) || new this(element, typeof config === 'object' ? config : null)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DATA_KEY() {\n    return `bs.${this.NAME}`\n  }\n\n  static get EVENT_KEY() {\n    return `.${this.DATA_KEY}`\n  }\n\n  static eventName(name) {\n    return `${name}${this.EVENT_KEY}`\n  }\n}\n\nexport default BaseComponent\n"], "names": ["VERSION", "BaseComponent", "Config", "constructor", "element", "config", "getElement", "_element", "_config", "_getConfig", "Data", "set", "DATA_KEY", "dispose", "remove", "EventHandler", "off", "EVENT_KEY", "propertyName", "Object", "getOwnPropertyNames", "_queueCallback", "callback", "isAnimated", "executeAfterTransition", "_mergeConfigObj", "_configAfterMerge", "_typeCheckConfig", "getInstance", "get", "getOrCreateInstance", "NAME", "eventName", "name"], "mappings": ";;;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAOA;EACA;EACA;;EAEA,MAAMA,OAAO,GAAG,OAAhB,CAAA;EAEA;EACA;EACA;;EAEA,MAAMC,aAAN,SAA4BC,uBAA5B,CAAmC;EACjCC,EAAAA,WAAW,CAACC,OAAD,EAAUC,MAAV,EAAkB;EAC3B,IAAA,KAAA,EAAA,CAAA;EAEAD,IAAAA,OAAO,GAAGE,gBAAU,CAACF,OAAD,CAApB,CAAA;;MACA,IAAI,CAACA,OAAL,EAAc;EACZ,MAAA,OAAA;EACD,KAAA;;MAED,IAAKG,CAAAA,QAAL,GAAgBH,OAAhB,CAAA;EACA,IAAA,IAAA,CAAKI,OAAL,GAAe,IAAA,CAAKC,UAAL,CAAgBJ,MAAhB,CAAf,CAAA;MAEAK,qBAAI,CAACC,GAAL,CAAS,IAAKJ,CAAAA,QAAd,EAAwB,IAAA,CAAKJ,WAAL,CAAiBS,QAAzC,EAAmD,IAAnD,CAAA,CAAA;EACD,GAbgC;;;EAgBjCC,EAAAA,OAAO,GAAG;MACRH,qBAAI,CAACI,MAAL,CAAY,IAAA,CAAKP,QAAjB,EAA2B,IAAA,CAAKJ,WAAL,CAAiBS,QAA5C,CAAA,CAAA;MACAG,6BAAY,CAACC,GAAb,CAAiB,IAAA,CAAKT,QAAtB,EAAgC,IAAA,CAAKJ,WAAL,CAAiBc,SAAjD,CAAA,CAAA;;MAEA,KAAK,MAAMC,YAAX,IAA2BC,MAAM,CAACC,mBAAP,CAA2B,IAA3B,CAA3B,EAA6D;QAC3D,IAAKF,CAAAA,YAAL,IAAqB,IAArB,CAAA;EACD,KAAA;EACF,GAAA;;IAEDG,cAAc,CAACC,QAAD,EAAWlB,OAAX,EAAoBmB,UAAU,GAAG,IAAjC,EAAuC;EACnDC,IAAAA,4BAAsB,CAACF,QAAD,EAAWlB,OAAX,EAAoBmB,UAApB,CAAtB,CAAA;EACD,GAAA;;IAEDd,UAAU,CAACJ,MAAD,EAAS;MACjBA,MAAM,GAAG,KAAKoB,eAAL,CAAqBpB,MAArB,EAA6B,IAAA,CAAKE,QAAlC,CAAT,CAAA;EACAF,IAAAA,MAAM,GAAG,IAAA,CAAKqB,iBAAL,CAAuBrB,MAAvB,CAAT,CAAA;;MACA,IAAKsB,CAAAA,gBAAL,CAAsBtB,MAAtB,CAAA,CAAA;;EACA,IAAA,OAAOA,MAAP,CAAA;EACD,GAlCgC;;;IAqCf,OAAXuB,WAAW,CAACxB,OAAD,EAAU;MAC1B,OAAOM,qBAAI,CAACmB,GAAL,CAASvB,gBAAU,CAACF,OAAD,CAAnB,EAA8B,IAAKQ,CAAAA,QAAnC,CAAP,CAAA;EACD,GAAA;;EAEyB,EAAA,OAAnBkB,mBAAmB,CAAC1B,OAAD,EAAUC,MAAM,GAAG,EAAnB,EAAuB;EAC/C,IAAA,OAAO,KAAKuB,WAAL,CAAiBxB,OAAjB,CAA6B,IAAA,IAAI,IAAJ,CAASA,OAAT,EAAkB,OAAOC,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAxD,CAApC,CAAA;EACD,GAAA;;EAEiB,EAAA,WAAPL,OAAO,GAAG;EACnB,IAAA,OAAOA,OAAP,CAAA;EACD,GAAA;;EAEkB,EAAA,WAARY,QAAQ,GAAG;MACpB,OAAQ,CAAA,GAAA,EAAK,IAAKmB,CAAAA,IAAK,CAAvB,CAAA,CAAA;EACD,GAAA;;EAEmB,EAAA,WAATd,SAAS,GAAG;MACrB,OAAQ,CAAA,CAAA,EAAG,IAAKL,CAAAA,QAAS,CAAzB,CAAA,CAAA;EACD,GAAA;;IAEe,OAAToB,SAAS,CAACC,IAAD,EAAO;EACrB,IAAA,OAAQ,CAAEA,EAAAA,IAAK,CAAE,EAAA,IAAA,CAAKhB,SAAU,CAAhC,CAAA,CAAA;EACD,GAAA;;EA3DgC;;;;;;;;"}