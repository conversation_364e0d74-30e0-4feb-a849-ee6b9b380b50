{"version": 3, "file": "offcanvas.js", "sources": ["../src/offcanvas.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isDisabled,\n  isVisible\n} from './util/index'\nimport ScrollBarHelper from './util/scrollbar'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport SelectorEngine from './dom/selector-engine'\nimport Backdrop from './util/backdrop'\nimport FocusTrap from './util/focustrap'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * Constants\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\nconst CLASS_NAME_HIDING = 'hiding'\nconst CLASS_NAME_BACKDROP = 'offcanvas-backdrop'\nconst OPEN_SELECTOR = '.offcanvas.show'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isShown = false\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._backdrop.show()\n\n    if (!this._config.scroll) {\n      new ScrollBarHelper().hide()\n    }\n\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOWING)\n\n    const completeCallBack = () => {\n      if (!this._config.scroll || this._config.backdrop) {\n        this._focustrap.activate()\n      }\n\n      this._element.classList.add(CLASS_NAME_SHOW)\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n    }\n\n    this._queueCallback(completeCallBack, this._element, true)\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._focustrap.deactivate()\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.add(CLASS_NAME_HIDING)\n    this._backdrop.hide()\n\n    const completeCallback = () => {\n      this._element.classList.remove(CLASS_NAME_SHOW, CLASS_NAME_HIDING)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n\n      if (!this._config.scroll) {\n        new ScrollBarHelper().reset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._queueCallback(completeCallback, this._element, true)\n  }\n\n  dispose() {\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    const clickCallback = () => {\n      if (this._config.backdrop === 'static') {\n        EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n        return\n      }\n\n      this.hide()\n    }\n\n    // 'static' option will be translated to true, and booleans will keep their value\n    const isVisible = Boolean(this._config.backdrop)\n\n    return new Backdrop({\n      className: CLASS_NAME_BACKDROP,\n      isVisible,\n      isAnimated: true,\n      rootElement: this._element.parentNode,\n      clickCallback: isVisible ? clickCallback : null\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (!this._config.keyboard) {\n        EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n        return\n      }\n\n      this.hide()\n    })\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Offcanvas.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen && alreadyOpen !== target) {\n    Offcanvas.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Offcanvas.getOrCreateInstance(target)\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const selector of SelectorEngine.find(OPEN_SELECTOR)) {\n    Offcanvas.getOrCreateInstance(selector).show()\n  }\n})\n\nEventHandler.on(window, EVENT_RESIZE, () => {\n  for (const element of SelectorEngine.find('[aria-modal][class*=show][class*=offcanvas-]')) {\n    if (getComputedStyle(element).position !== 'fixed') {\n      Offcanvas.getOrCreateInstance(element).hide()\n    }\n  }\n})\n\nenableDismissTrigger(Offcanvas)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Offcanvas)\n\nexport default Offcanvas\n"], "names": ["NAME", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "EVENT_LOAD_DATA_API", "ESCAPE_KEY", "CLASS_NAME_SHOW", "CLASS_NAME_SHOWING", "CLASS_NAME_HIDING", "CLASS_NAME_BACKDROP", "OPEN_SELECTOR", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDE_PREVENTED", "EVENT_HIDDEN", "EVENT_RESIZE", "EVENT_CLICK_DATA_API", "EVENT_KEYDOWN_DISMISS", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON><PERSON>", "backdrop", "keyboard", "scroll", "DefaultType", "<PERSON><PERSON><PERSON>", "BaseComponent", "constructor", "element", "config", "_isShown", "_backdrop", "_initializeBackDrop", "_focustrap", "_initializeFocusTrap", "_addEventListeners", "toggle", "relatedTarget", "hide", "show", "showEvent", "EventHandler", "trigger", "_element", "defaultPrevented", "_config", "ScrollBarHelper", "setAttribute", "classList", "add", "completeCallBack", "activate", "remove", "_queueCallback", "hideEvent", "deactivate", "blur", "completeCallback", "removeAttribute", "reset", "dispose", "clickCallback", "isVisible", "Boolean", "Backdrop", "className", "isAnimated", "rootElement", "parentNode", "FocusTrap", "trapElement", "on", "event", "key", "jQueryInterface", "each", "data", "getOrCreateInstance", "undefined", "startsWith", "TypeError", "document", "target", "getElementFromSelector", "includes", "tagName", "preventDefault", "isDisabled", "one", "focus", "alreadyOpen", "SelectorEngine", "findOne", "getInstance", "window", "selector", "find", "getComputedStyle", "position", "enableDismissTrigger", "defineJQueryPlugin"], "mappings": ";;;;;;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAgBA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,WAAb,CAAA;EACA,MAAMC,QAAQ,GAAG,cAAjB,CAAA;EACA,MAAMC,SAAS,GAAI,CAAGD,CAAAA,EAAAA,QAAS,CAA/B,CAAA,CAAA;EACA,MAAME,YAAY,GAAG,WAArB,CAAA;EACA,MAAMC,mBAAmB,GAAI,CAAA,IAAA,EAAMF,SAAU,CAAA,EAAEC,YAAa,CAA5D,CAAA,CAAA;EACA,MAAME,UAAU,GAAG,QAAnB,CAAA;EAEA,MAAMC,eAAe,GAAG,MAAxB,CAAA;EACA,MAAMC,kBAAkB,GAAG,SAA3B,CAAA;EACA,MAAMC,iBAAiB,GAAG,QAA1B,CAAA;EACA,MAAMC,mBAAmB,GAAG,oBAA5B,CAAA;EACA,MAAMC,aAAa,GAAG,iBAAtB,CAAA;EAEA,MAAMC,UAAU,GAAI,CAAMT,IAAAA,EAAAA,SAAU,CAApC,CAAA,CAAA;EACA,MAAMU,WAAW,GAAI,CAAOV,KAAAA,EAAAA,SAAU,CAAtC,CAAA,CAAA;EACA,MAAMW,UAAU,GAAI,CAAMX,IAAAA,EAAAA,SAAU,CAApC,CAAA,CAAA;EACA,MAAMY,oBAAoB,GAAI,CAAeZ,aAAAA,EAAAA,SAAU,CAAvD,CAAA,CAAA;EACA,MAAMa,YAAY,GAAI,CAAQb,MAAAA,EAAAA,SAAU,CAAxC,CAAA,CAAA;EACA,MAAMc,YAAY,GAAI,CAAQd,MAAAA,EAAAA,SAAU,CAAxC,CAAA,CAAA;EACA,MAAMe,oBAAoB,GAAI,CAAA,KAAA,EAAOf,SAAU,CAAA,EAAEC,YAAa,CAA9D,CAAA,CAAA;EACA,MAAMe,qBAAqB,GAAI,CAAiBhB,eAAAA,EAAAA,SAAU,CAA1D,CAAA,CAAA;EAEA,MAAMiB,oBAAoB,GAAG,8BAA7B,CAAA;EAEA,MAAMC,OAAO,GAAG;EACdC,EAAAA,QAAQ,EAAE,IADI;EAEdC,EAAAA,QAAQ,EAAE,IAFI;EAGdC,EAAAA,MAAM,EAAE,KAAA;EAHM,CAAhB,CAAA;EAMA,MAAMC,WAAW,GAAG;EAClBH,EAAAA,QAAQ,EAAE,kBADQ;EAElBC,EAAAA,QAAQ,EAAE,SAFQ;EAGlBC,EAAAA,MAAM,EAAE,SAAA;EAHU,CAApB,CAAA;EAMA;EACA;EACA;;EAEA,MAAME,SAAN,SAAwBC,8BAAxB,CAAsC;EACpCC,EAAAA,WAAW,CAACC,OAAD,EAAUC,MAAV,EAAkB;MAC3B,KAAMD,CAAAA,OAAN,EAAeC,MAAf,CAAA,CAAA;MAEA,IAAKC,CAAAA,QAAL,GAAgB,KAAhB,CAAA;EACA,IAAA,IAAA,CAAKC,SAAL,GAAiB,IAAKC,CAAAA,mBAAL,EAAjB,CAAA;EACA,IAAA,IAAA,CAAKC,UAAL,GAAkB,IAAKC,CAAAA,oBAAL,EAAlB,CAAA;;EACA,IAAA,IAAA,CAAKC,kBAAL,EAAA,CAAA;EACD,GARmC;;;EAWlB,EAAA,WAAPf,OAAO,GAAG;EACnB,IAAA,OAAOA,OAAP,CAAA;EACD,GAAA;;EAEqB,EAAA,WAAXI,WAAW,GAAG;EACvB,IAAA,OAAOA,WAAP,CAAA;EACD,GAAA;;EAEc,EAAA,WAAJxB,IAAI,GAAG;EAChB,IAAA,OAAOA,IAAP,CAAA;EACD,GArBmC;;;IAwBpCoC,MAAM,CAACC,aAAD,EAAgB;MACpB,OAAO,IAAA,CAAKP,QAAL,GAAgB,IAAKQ,CAAAA,IAAL,EAAhB,GAA8B,IAAKC,CAAAA,IAAL,CAAUF,aAAV,CAArC,CAAA;EACD,GAAA;;IAEDE,IAAI,CAACF,aAAD,EAAgB;MAClB,IAAI,IAAA,CAAKP,QAAT,EAAmB;EACjB,MAAA,OAAA;EACD,KAAA;;MAED,MAAMU,SAAS,GAAGC,6BAAY,CAACC,OAAb,CAAqB,IAAKC,CAAAA,QAA1B,EAAoChC,UAApC,EAAgD;EAAE0B,MAAAA,aAAAA;EAAF,KAAhD,CAAlB,CAAA;;MAEA,IAAIG,SAAS,CAACI,gBAAd,EAAgC;EAC9B,MAAA,OAAA;EACD,KAAA;;MAED,IAAKd,CAAAA,QAAL,GAAgB,IAAhB,CAAA;;MACA,IAAKC,CAAAA,SAAL,CAAeQ,IAAf,EAAA,CAAA;;EAEA,IAAA,IAAI,CAAC,IAAA,CAAKM,OAAL,CAAatB,MAAlB,EAA0B;QACxB,IAAIuB,gCAAJ,GAAsBR,IAAtB,EAAA,CAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKK,QAAL,CAAcI,YAAd,CAA2B,YAA3B,EAAyC,IAAzC,CAAA,CAAA;;EACA,IAAA,IAAA,CAAKJ,QAAL,CAAcI,YAAd,CAA2B,MAA3B,EAAmC,QAAnC,CAAA,CAAA;;EACA,IAAA,IAAA,CAAKJ,QAAL,CAAcK,SAAd,CAAwBC,GAAxB,CAA4B1C,kBAA5B,CAAA,CAAA;;MAEA,MAAM2C,gBAAgB,GAAG,MAAM;QAC7B,IAAI,CAAC,IAAKL,CAAAA,OAAL,CAAatB,MAAd,IAAwB,IAAKsB,CAAAA,OAAL,CAAaxB,QAAzC,EAAmD;UACjD,IAAKY,CAAAA,UAAL,CAAgBkB,QAAhB,EAAA,CAAA;EACD,OAAA;;EAED,MAAA,IAAA,CAAKR,QAAL,CAAcK,SAAd,CAAwBC,GAAxB,CAA4B3C,eAA5B,CAAA,CAAA;;EACA,MAAA,IAAA,CAAKqC,QAAL,CAAcK,SAAd,CAAwBI,MAAxB,CAA+B7C,kBAA/B,CAAA,CAAA;;EACAkC,MAAAA,6BAAY,CAACC,OAAb,CAAqB,KAAKC,QAA1B,EAAoC/B,WAApC,EAAiD;EAAEyB,QAAAA,aAAAA;SAAnD,CAAA,CAAA;OAPF,CAAA;;EAUA,IAAA,IAAA,CAAKgB,cAAL,CAAoBH,gBAApB,EAAsC,IAAKP,CAAAA,QAA3C,EAAqD,IAArD,CAAA,CAAA;EACD,GAAA;;EAEDL,EAAAA,IAAI,GAAG;MACL,IAAI,CAAC,IAAKR,CAAAA,QAAV,EAAoB;EAClB,MAAA,OAAA;EACD,KAAA;;MAED,MAAMwB,SAAS,GAAGb,6BAAY,CAACC,OAAb,CAAqB,IAAKC,CAAAA,QAA1B,EAAoC9B,UAApC,CAAlB,CAAA;;MAEA,IAAIyC,SAAS,CAACV,gBAAd,EAAgC;EAC9B,MAAA,OAAA;EACD,KAAA;;MAED,IAAKX,CAAAA,UAAL,CAAgBsB,UAAhB,EAAA,CAAA;;MACA,IAAKZ,CAAAA,QAAL,CAAca,IAAd,EAAA,CAAA;;MACA,IAAK1B,CAAAA,QAAL,GAAgB,KAAhB,CAAA;;EACA,IAAA,IAAA,CAAKa,QAAL,CAAcK,SAAd,CAAwBC,GAAxB,CAA4BzC,iBAA5B,CAAA,CAAA;;MACA,IAAKuB,CAAAA,SAAL,CAAeO,IAAf,EAAA,CAAA;;MAEA,MAAMmB,gBAAgB,GAAG,MAAM;QAC7B,IAAKd,CAAAA,QAAL,CAAcK,SAAd,CAAwBI,MAAxB,CAA+B9C,eAA/B,EAAgDE,iBAAhD,CAAA,CAAA;;EACA,MAAA,IAAA,CAAKmC,QAAL,CAAce,eAAd,CAA8B,YAA9B,CAAA,CAAA;;EACA,MAAA,IAAA,CAAKf,QAAL,CAAce,eAAd,CAA8B,MAA9B,CAAA,CAAA;;EAEA,MAAA,IAAI,CAAC,IAAA,CAAKb,OAAL,CAAatB,MAAlB,EAA0B;UACxB,IAAIuB,gCAAJ,GAAsBa,KAAtB,EAAA,CAAA;EACD,OAAA;;EAEDlB,MAAAA,6BAAY,CAACC,OAAb,CAAqB,IAAKC,CAAAA,QAA1B,EAAoC5B,YAApC,CAAA,CAAA;OATF,CAAA;;EAYA,IAAA,IAAA,CAAKsC,cAAL,CAAoBI,gBAApB,EAAsC,IAAKd,CAAAA,QAA3C,EAAqD,IAArD,CAAA,CAAA;EACD,GAAA;;EAEDiB,EAAAA,OAAO,GAAG;MACR,IAAK7B,CAAAA,SAAL,CAAe6B,OAAf,EAAA,CAAA;;MACA,IAAK3B,CAAAA,UAAL,CAAgBsB,UAAhB,EAAA,CAAA;;EACA,IAAA,KAAA,CAAMK,OAAN,EAAA,CAAA;EACD,GAnGmC;;;EAsGpC5B,EAAAA,mBAAmB,GAAG;MACpB,MAAM6B,aAAa,GAAG,MAAM;EAC1B,MAAA,IAAI,KAAKhB,OAAL,CAAaxB,QAAb,KAA0B,QAA9B,EAAwC;EACtCoB,QAAAA,6BAAY,CAACC,OAAb,CAAqB,IAAKC,CAAAA,QAA1B,EAAoC7B,oBAApC,CAAA,CAAA;EACA,QAAA,OAAA;EACD,OAAA;;EAED,MAAA,IAAA,CAAKwB,IAAL,EAAA,CAAA;EACD,KAPD,CADoB;;;MAWpB,MAAMwB,SAAS,GAAGC,OAAO,CAAC,KAAKlB,OAAL,CAAaxB,QAAd,CAAzB,CAAA;MAEA,OAAO,IAAI2C,yBAAJ,CAAa;EAClBC,MAAAA,SAAS,EAAExD,mBADO;QAElBqD,SAFkB;EAGlBI,MAAAA,UAAU,EAAE,IAHM;EAIlBC,MAAAA,WAAW,EAAE,IAAA,CAAKxB,QAAL,CAAcyB,UAJT;EAKlBP,MAAAA,aAAa,EAAEC,SAAS,GAAGD,aAAH,GAAmB,IAAA;EALzB,KAAb,CAAP,CAAA;EAOD,GAAA;;EAED3B,EAAAA,oBAAoB,GAAG;MACrB,OAAO,IAAImC,0BAAJ,CAAc;EACnBC,MAAAA,WAAW,EAAE,IAAK3B,CAAAA,QAAAA;EADC,KAAd,CAAP,CAAA;EAGD,GAAA;;EAEDR,EAAAA,kBAAkB,GAAG;MACnBM,6BAAY,CAAC8B,EAAb,CAAgB,IAAA,CAAK5B,QAArB,EAA+BzB,qBAA/B,EAAsDsD,KAAK,IAAI;EAC7D,MAAA,IAAIA,KAAK,CAACC,GAAN,KAAcpE,UAAlB,EAA8B;EAC5B,QAAA,OAAA;EACD,OAAA;;EAED,MAAA,IAAI,CAAC,IAAA,CAAKwC,OAAL,CAAavB,QAAlB,EAA4B;EAC1BmB,QAAAA,6BAAY,CAACC,OAAb,CAAqB,IAAKC,CAAAA,QAA1B,EAAoC7B,oBAApC,CAAA,CAAA;EACA,QAAA,OAAA;EACD,OAAA;;EAED,MAAA,IAAA,CAAKwB,IAAL,EAAA,CAAA;OAVF,CAAA,CAAA;EAYD,GA/ImC;;;IAkJd,OAAfoC,eAAe,CAAC7C,MAAD,EAAS;MAC7B,OAAO,IAAA,CAAK8C,IAAL,CAAU,YAAY;QAC3B,MAAMC,IAAI,GAAGnD,SAAS,CAACoD,mBAAV,CAA8B,IAA9B,EAAoChD,MAApC,CAAb,CAAA;;EAEA,MAAA,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,QAAA,OAAA;EACD,OAAA;;EAED,MAAA,IAAI+C,IAAI,CAAC/C,MAAD,CAAJ,KAAiBiD,SAAjB,IAA8BjD,MAAM,CAACkD,UAAP,CAAkB,GAAlB,CAA9B,IAAwDlD,MAAM,KAAK,aAAvE,EAAsF;EACpF,QAAA,MAAM,IAAImD,SAAJ,CAAe,CAAmBnD,iBAAAA,EAAAA,MAAO,GAAzC,CAAN,CAAA;EACD,OAAA;;EAED+C,MAAAA,IAAI,CAAC/C,MAAD,CAAJ,CAAa,IAAb,CAAA,CAAA;EACD,KAZM,CAAP,CAAA;EAaD,GAAA;;EAhKmC,CAAA;EAmKtC;EACA;EACA;;;AAEAY,+BAAY,CAAC8B,EAAb,CAAgBU,QAAhB,EAA0BhE,oBAA1B,EAAgDE,oBAAhD,EAAsE,UAAUqD,KAAV,EAAiB;EACrF,EAAA,MAAMU,MAAM,GAAGC,4BAAsB,CAAC,IAAD,CAArC,CAAA;;IAEA,IAAI,CAAC,GAAD,EAAM,MAAN,CAAA,CAAcC,QAAd,CAAuB,IAAA,CAAKC,OAA5B,CAAJ,EAA0C;EACxCb,IAAAA,KAAK,CAACc,cAAN,EAAA,CAAA;EACD,GAAA;;EAED,EAAA,IAAIC,gBAAU,CAAC,IAAD,CAAd,EAAsB;EACpB,IAAA,OAAA;EACD,GAAA;;EAED9C,EAAAA,6BAAY,CAAC+C,GAAb,CAAiBN,MAAjB,EAAyBnE,YAAzB,EAAuC,MAAM;EAC3C;EACA,IAAA,IAAI+C,eAAS,CAAC,IAAD,CAAb,EAAqB;EACnB,MAAA,IAAA,CAAK2B,KAAL,EAAA,CAAA;EACD,KAAA;EACF,GALD,EAXqF;;EAmBrF,EAAA,MAAMC,WAAW,GAAGC,+BAAc,CAACC,OAAf,CAAuBlF,aAAvB,CAApB,CAAA;;EACA,EAAA,IAAIgF,WAAW,IAAIA,WAAW,KAAKR,MAAnC,EAA2C;EACzCzD,IAAAA,SAAS,CAACoE,WAAV,CAAsBH,WAAtB,EAAmCpD,IAAnC,EAAA,CAAA;EACD,GAAA;;EAED,EAAA,MAAMsC,IAAI,GAAGnD,SAAS,CAACoD,mBAAV,CAA8BK,MAA9B,CAAb,CAAA;IACAN,IAAI,CAACxC,MAAL,CAAY,IAAZ,CAAA,CAAA;EACD,CA1BD,CAAA,CAAA;AA4BAK,+BAAY,CAAC8B,EAAb,CAAgBuB,MAAhB,EAAwB1F,mBAAxB,EAA6C,MAAM;IACjD,KAAK,MAAM2F,QAAX,IAAuBJ,+BAAc,CAACK,IAAf,CAAoBtF,aAApB,CAAvB,EAA2D;EACzDe,IAAAA,SAAS,CAACoD,mBAAV,CAA8BkB,QAA9B,EAAwCxD,IAAxC,EAAA,CAAA;EACD,GAAA;EACF,CAJD,CAAA,CAAA;AAMAE,+BAAY,CAAC8B,EAAb,CAAgBuB,MAAhB,EAAwB9E,YAAxB,EAAsC,MAAM;IAC1C,KAAK,MAAMY,OAAX,IAAsB+D,+BAAc,CAACK,IAAf,CAAoB,8CAApB,CAAtB,EAA2F;MACzF,IAAIC,gBAAgB,CAACrE,OAAD,CAAhB,CAA0BsE,QAA1B,KAAuC,OAA3C,EAAoD;EAClDzE,MAAAA,SAAS,CAACoD,mBAAV,CAA8BjD,OAA9B,EAAuCU,IAAvC,EAAA,CAAA;EACD,KAAA;EACF,GAAA;EACF,CAND,CAAA,CAAA;AAQA6D,yCAAoB,CAAC1E,SAAD,CAApB,CAAA;EAEA;EACA;EACA;;AAEA2E,0BAAkB,CAAC3E,SAAD,CAAlB;;;;;;;;"}