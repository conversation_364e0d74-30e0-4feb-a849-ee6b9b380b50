<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../../../dist/css/bootstrap.min.css" rel="stylesheet">
    <title>Toast</title>
    <style>
      .notifications {
        position: absolute;
        top: 10px;
        right: 10px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>Toast <small>Bootstrap Visual Test</small></h1>

      <div class="row mt-3">
        <div class="col-md-12">
          <button id="btnShowToast" class="btn btn-primary">Show toast</button>
          <button id="btnHideToast" class="btn btn-primary">Hide toast</button>
        </div>
      </div>
    </div>

    <div class="notifications">
      <div id="toastAutoHide" class="toast" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="2000">
        <div class="toast-header">
          <span class="d-block bg-primary rounded me-2" style="width: 20px; height: 20px;"></span>
          <strong class="me-auto">Bootstrap</strong>
          <small>11 mins ago</small>
        </div>
        <div class="toast-body">
          Hello, world! This is a toast message with <strong>autohide</strong> in 2 seconds
        </div>
      </div>

      <div class="toast" data-bs-autohide="false" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
          <span class="d-block bg-primary rounded me-2" style="width: 20px; height: 20px;"></span>
          <strong class="me-auto">Bootstrap</strong>
          <small class="text-muted">2 seconds ago</small>
          <button type="button" class="ms-2 mb-1 btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body">
          Heads up, toasts will stack automatically
        </div>
      </div>
    </div>

    <script src="../../../dist/js/bootstrap.bundle.js"></script>
    <script>
      /* global bootstrap: false */

      window.addEventListener('load', () => {
        document.querySelectorAll('.toast').forEach(toastEl => new bootstrap.Toast(toastEl))

        document.getElementById('btnShowToast').addEventListener('click', () => {
          document.querySelectorAll('.toast').forEach(toastEl => bootstrap.Toast.getInstance(toastEl).show())
        })

        document.getElementById('btnHideToast').addEventListener('click', () => {
          document.querySelectorAll('.toast').forEach(toastEl => bootstrap.Toast.getInstance(toastEl).hide())
        })
      })
    </script>
  </body>
</html>
