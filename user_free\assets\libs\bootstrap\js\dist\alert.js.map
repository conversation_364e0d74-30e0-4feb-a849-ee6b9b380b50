{"version": 3, "file": "alert.js", "sources": ["../src/alert.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * Constants\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * Class definition\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  close() {\n    const closeEvent = EventHandler.trigger(this._element, EVENT_CLOSE)\n\n    if (closeEvent.defaultPrevented) {\n      return\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    const isAnimated = this._element.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(() => this._destroyElement(), this._element, isAnimated)\n  }\n\n  // Private\n  _destroyElement() {\n    this._element.remove()\n    EventHandler.trigger(this._element, EVENT_CLOSED)\n    this.dispose()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Alert.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Alert, 'close')\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Alert)\n\nexport default Alert\n"], "names": ["NAME", "DATA_KEY", "EVENT_KEY", "EVENT_CLOSE", "EVENT_CLOSED", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "<PERSON><PERSON>", "BaseComponent", "close", "closeEvent", "EventHandler", "trigger", "_element", "defaultPrevented", "classList", "remove", "isAnimated", "contains", "_queueCallback", "_destroyElement", "dispose", "jQueryInterface", "config", "each", "data", "getOrCreateInstance", "undefined", "startsWith", "TypeError", "enableDismissTrigger", "defineJQueryPlugin"], "mappings": ";;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAOA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,OAAb,CAAA;EACA,MAAMC,QAAQ,GAAG,UAAjB,CAAA;EACA,MAAMC,SAAS,GAAI,CAAGD,CAAAA,EAAAA,QAAS,CAA/B,CAAA,CAAA;EAEA,MAAME,WAAW,GAAI,CAAOD,KAAAA,EAAAA,SAAU,CAAtC,CAAA,CAAA;EACA,MAAME,YAAY,GAAI,CAAQF,MAAAA,EAAAA,SAAU,CAAxC,CAAA,CAAA;EACA,MAAMG,eAAe,GAAG,MAAxB,CAAA;EACA,MAAMC,eAAe,GAAG,MAAxB,CAAA;EAEA;EACA;EACA;;EAEA,MAAMC,KAAN,SAAoBC,8BAApB,CAAkC;EAChC;EACe,EAAA,WAAJR,IAAI,GAAG;EAChB,IAAA,OAAOA,IAAP,CAAA;EACD,GAJ+B;;;EAOhCS,EAAAA,KAAK,GAAG;MACN,MAAMC,UAAU,GAAGC,6BAAY,CAACC,OAAb,CAAqB,IAAKC,CAAAA,QAA1B,EAAoCV,WAApC,CAAnB,CAAA;;MAEA,IAAIO,UAAU,CAACI,gBAAf,EAAiC;EAC/B,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKD,QAAL,CAAcE,SAAd,CAAwBC,MAAxB,CAA+BV,eAA/B,CAAA,CAAA;;MAEA,MAAMW,UAAU,GAAG,IAAA,CAAKJ,QAAL,CAAcE,SAAd,CAAwBG,QAAxB,CAAiCb,eAAjC,CAAnB,CAAA;;MACA,IAAKc,CAAAA,cAAL,CAAoB,MAAM,IAAKC,CAAAA,eAAL,EAA1B,EAAkD,IAAA,CAAKP,QAAvD,EAAiEI,UAAjE,CAAA,CAAA;EACD,GAlB+B;;;EAqBhCG,EAAAA,eAAe,GAAG;MAChB,IAAKP,CAAAA,QAAL,CAAcG,MAAd,EAAA,CAAA;;EACAL,IAAAA,6BAAY,CAACC,OAAb,CAAqB,IAAKC,CAAAA,QAA1B,EAAoCT,YAApC,CAAA,CAAA;EACA,IAAA,IAAA,CAAKiB,OAAL,EAAA,CAAA;EACD,GAzB+B;;;IA4BV,OAAfC,eAAe,CAACC,MAAD,EAAS;MAC7B,OAAO,IAAA,CAAKC,IAAL,CAAU,YAAY;EAC3B,MAAA,MAAMC,IAAI,GAAGlB,KAAK,CAACmB,mBAAN,CAA0B,IAA1B,CAAb,CAAA;;EAEA,MAAA,IAAI,OAAOH,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,QAAA,OAAA;EACD,OAAA;;EAED,MAAA,IAAIE,IAAI,CAACF,MAAD,CAAJ,KAAiBI,SAAjB,IAA8BJ,MAAM,CAACK,UAAP,CAAkB,GAAlB,CAA9B,IAAwDL,MAAM,KAAK,aAAvE,EAAsF;EACpF,QAAA,MAAM,IAAIM,SAAJ,CAAe,CAAmBN,iBAAAA,EAAAA,MAAO,GAAzC,CAAN,CAAA;EACD,OAAA;;EAEDE,MAAAA,IAAI,CAACF,MAAD,CAAJ,CAAa,IAAb,CAAA,CAAA;EACD,KAZM,CAAP,CAAA;EAaD,GAAA;;EA1C+B,CAAA;EA6ClC;EACA;EACA;;;AAEAO,yCAAoB,CAACvB,KAAD,EAAQ,OAAR,CAApB,CAAA;EAEA;EACA;EACA;;AAEAwB,0BAAkB,CAACxB,KAAD,CAAlB;;;;;;;;"}