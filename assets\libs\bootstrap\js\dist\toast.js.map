{"version": 3, "file": "toast.js", "sources": ["../src/toast.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, reflow } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * Constants\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide' // @deprecated - kept here only for backwards compatibility\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\n/**\n * Class definition\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._timeout = null\n    this._hasMouseInteraction = false\n    this._hasKeyboardInteraction = false\n    this._setListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      this._maybeScheduleHide()\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE) // @deprecated\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOW, CLASS_NAME_SHOWING)\n\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  hide() {\n    if (!this.isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE) // @deprecated\n      this._element.classList.remove(CLASS_NAME_SHOWING, CLASS_NAME_SHOW)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this.isShown()) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    super.dispose()\n  }\n\n  isShown() {\n    return this._element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return\n    }\n\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return\n    }\n\n    this._timeout = setTimeout(() => {\n      this.hide()\n    }, this._config.delay)\n  }\n\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout': {\n        this._hasMouseInteraction = isInteracting\n        break\n      }\n\n      case 'focusin':\n      case 'focusout': {\n        this._hasKeyboardInteraction = isInteracting\n        break\n      }\n\n      default: {\n        break\n      }\n    }\n\n    if (isInteracting) {\n      this._clearTimeout()\n      return\n    }\n\n    const nextElement = event.relatedTarget\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return\n    }\n\n    this._maybeScheduleHide()\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false))\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false))\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Toast.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Toast)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Toast)\n\nexport default Toast\n"], "names": ["NAME", "DATA_KEY", "EVENT_KEY", "EVENT_MOUSEOVER", "EVENT_MOUSEOUT", "EVENT_FOCUSIN", "EVENT_FOCUSOUT", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "CLASS_NAME_FADE", "CLASS_NAME_HIDE", "CLASS_NAME_SHOW", "CLASS_NAME_SHOWING", "DefaultType", "animation", "autohide", "delay", "<PERSON><PERSON><PERSON>", "Toast", "BaseComponent", "constructor", "element", "config", "_timeout", "_hasMouseInteraction", "_hasKeyboardInteraction", "_setListeners", "show", "showEvent", "EventHandler", "trigger", "_element", "defaultPrevented", "_clearTimeout", "_config", "classList", "add", "complete", "remove", "_maybeScheduleHide", "reflow", "_queueCallback", "hide", "isShown", "hideEvent", "dispose", "contains", "setTimeout", "_onInteraction", "event", "isInteracting", "type", "nextElement", "relatedTarget", "on", "clearTimeout", "jQueryInterface", "each", "data", "getOrCreateInstance", "TypeError", "enableDismissTrigger", "defineJQueryPlugin"], "mappings": ";;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAOA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,OAAb,CAAA;EACA,MAAMC,QAAQ,GAAG,UAAjB,CAAA;EACA,MAAMC,SAAS,GAAI,CAAGD,CAAAA,EAAAA,QAAS,CAA/B,CAAA,CAAA;EAEA,MAAME,eAAe,GAAI,CAAWD,SAAAA,EAAAA,SAAU,CAA9C,CAAA,CAAA;EACA,MAAME,cAAc,GAAI,CAAUF,QAAAA,EAAAA,SAAU,CAA5C,CAAA,CAAA;EACA,MAAMG,aAAa,GAAI,CAASH,OAAAA,EAAAA,SAAU,CAA1C,CAAA,CAAA;EACA,MAAMI,cAAc,GAAI,CAAUJ,QAAAA,EAAAA,SAAU,CAA5C,CAAA,CAAA;EACA,MAAMK,UAAU,GAAI,CAAML,IAAAA,EAAAA,SAAU,CAApC,CAAA,CAAA;EACA,MAAMM,YAAY,GAAI,CAAQN,MAAAA,EAAAA,SAAU,CAAxC,CAAA,CAAA;EACA,MAAMO,UAAU,GAAI,CAAMP,IAAAA,EAAAA,SAAU,CAApC,CAAA,CAAA;EACA,MAAMQ,WAAW,GAAI,CAAOR,KAAAA,EAAAA,SAAU,CAAtC,CAAA,CAAA;EAEA,MAAMS,eAAe,GAAG,MAAxB,CAAA;EACA,MAAMC,eAAe,GAAG,MAAxB;;EACA,MAAMC,eAAe,GAAG,MAAxB,CAAA;EACA,MAAMC,kBAAkB,GAAG,SAA3B,CAAA;EAEA,MAAMC,WAAW,GAAG;EAClBC,EAAAA,SAAS,EAAE,SADO;EAElBC,EAAAA,QAAQ,EAAE,SAFQ;EAGlBC,EAAAA,KAAK,EAAE,QAAA;EAHW,CAApB,CAAA;EAMA,MAAMC,OAAO,GAAG;EACdH,EAAAA,SAAS,EAAE,IADG;EAEdC,EAAAA,QAAQ,EAAE,IAFI;EAGdC,EAAAA,KAAK,EAAE,IAAA;EAHO,CAAhB,CAAA;EAMA;EACA;EACA;;EAEA,MAAME,KAAN,SAAoBC,8BAApB,CAAkC;EAChCC,EAAAA,WAAW,CAACC,OAAD,EAAUC,MAAV,EAAkB;MAC3B,KAAMD,CAAAA,OAAN,EAAeC,MAAf,CAAA,CAAA;MAEA,IAAKC,CAAAA,QAAL,GAAgB,IAAhB,CAAA;MACA,IAAKC,CAAAA,oBAAL,GAA4B,KAA5B,CAAA;MACA,IAAKC,CAAAA,uBAAL,GAA+B,KAA/B,CAAA;;EACA,IAAA,IAAA,CAAKC,aAAL,EAAA,CAAA;EACD,GAR+B;;;EAWd,EAAA,WAAPT,OAAO,GAAG;EACnB,IAAA,OAAOA,OAAP,CAAA;EACD,GAAA;;EAEqB,EAAA,WAAXJ,WAAW,GAAG;EACvB,IAAA,OAAOA,WAAP,CAAA;EACD,GAAA;;EAEc,EAAA,WAAJf,IAAI,GAAG;EAChB,IAAA,OAAOA,IAAP,CAAA;EACD,GArB+B;;;EAwBhC6B,EAAAA,IAAI,GAAG;MACL,MAAMC,SAAS,GAAGC,6BAAY,CAACC,OAAb,CAAqB,IAAKC,CAAAA,QAA1B,EAAoCxB,UAApC,CAAlB,CAAA;;MAEA,IAAIqB,SAAS,CAACI,gBAAd,EAAgC;EAC9B,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKC,aAAL,EAAA,CAAA;;EAEA,IAAA,IAAI,IAAKC,CAAAA,OAAL,CAAapB,SAAjB,EAA4B;EAC1B,MAAA,IAAA,CAAKiB,QAAL,CAAcI,SAAd,CAAwBC,GAAxB,CAA4B3B,eAA5B,CAAA,CAAA;EACD,KAAA;;MAED,MAAM4B,QAAQ,GAAG,MAAM;EACrB,MAAA,IAAA,CAAKN,QAAL,CAAcI,SAAd,CAAwBG,MAAxB,CAA+B1B,kBAA/B,CAAA,CAAA;;EACAiB,MAAAA,6BAAY,CAACC,OAAb,CAAqB,IAAKC,CAAAA,QAA1B,EAAoCvB,WAApC,CAAA,CAAA;;EAEA,MAAA,IAAA,CAAK+B,kBAAL,EAAA,CAAA;OAJF,CAAA;;MAOA,IAAKR,CAAAA,QAAL,CAAcI,SAAd,CAAwBG,MAAxB,CAA+B5B,eAA/B,EApBK;;;MAqBL8B,YAAM,CAAC,IAAKT,CAAAA,QAAN,CAAN,CAAA;;MACA,IAAKA,CAAAA,QAAL,CAAcI,SAAd,CAAwBC,GAAxB,CAA4BzB,eAA5B,EAA6CC,kBAA7C,CAAA,CAAA;;MAEA,IAAK6B,CAAAA,cAAL,CAAoBJ,QAApB,EAA8B,IAAA,CAAKN,QAAnC,EAA6C,IAAA,CAAKG,OAAL,CAAapB,SAA1D,CAAA,CAAA;EACD,GAAA;;EAED4B,EAAAA,IAAI,GAAG;EACL,IAAA,IAAI,CAAC,IAAA,CAAKC,OAAL,EAAL,EAAqB;EACnB,MAAA,OAAA;EACD,KAAA;;MAED,MAAMC,SAAS,GAAGf,6BAAY,CAACC,OAAb,CAAqB,IAAKC,CAAAA,QAA1B,EAAoC1B,UAApC,CAAlB,CAAA;;MAEA,IAAIuC,SAAS,CAACZ,gBAAd,EAAgC;EAC9B,MAAA,OAAA;EACD,KAAA;;MAED,MAAMK,QAAQ,GAAG,MAAM;QACrB,IAAKN,CAAAA,QAAL,CAAcI,SAAd,CAAwBC,GAAxB,CAA4B1B,eAA5B,EADqB;;;QAErB,IAAKqB,CAAAA,QAAL,CAAcI,SAAd,CAAwBG,MAAxB,CAA+B1B,kBAA/B,EAAmDD,eAAnD,CAAA,CAAA;;EACAkB,MAAAA,6BAAY,CAACC,OAAb,CAAqB,IAAKC,CAAAA,QAA1B,EAAoCzB,YAApC,CAAA,CAAA;OAHF,CAAA;;EAMA,IAAA,IAAA,CAAKyB,QAAL,CAAcI,SAAd,CAAwBC,GAAxB,CAA4BxB,kBAA5B,CAAA,CAAA;;MACA,IAAK6B,CAAAA,cAAL,CAAoBJ,QAApB,EAA8B,IAAA,CAAKN,QAAnC,EAA6C,IAAA,CAAKG,OAAL,CAAapB,SAA1D,CAAA,CAAA;EACD,GAAA;;EAED+B,EAAAA,OAAO,GAAG;EACR,IAAA,IAAA,CAAKZ,aAAL,EAAA,CAAA;;MAEA,IAAI,IAAA,CAAKU,OAAL,EAAJ,EAAoB;EAClB,MAAA,IAAA,CAAKZ,QAAL,CAAcI,SAAd,CAAwBG,MAAxB,CAA+B3B,eAA/B,CAAA,CAAA;EACD,KAAA;;EAED,IAAA,KAAA,CAAMkC,OAAN,EAAA,CAAA;EACD,GAAA;;EAEDF,EAAAA,OAAO,GAAG;MACR,OAAO,IAAA,CAAKZ,QAAL,CAAcI,SAAd,CAAwBW,QAAxB,CAAiCnC,eAAjC,CAAP,CAAA;EACD,GApF+B;;;EAwFhC4B,EAAAA,kBAAkB,GAAG;EACnB,IAAA,IAAI,CAAC,IAAA,CAAKL,OAAL,CAAanB,QAAlB,EAA4B;EAC1B,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAI,IAAKS,CAAAA,oBAAL,IAA6B,IAAA,CAAKC,uBAAtC,EAA+D;EAC7D,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKF,QAAL,GAAgBwB,UAAU,CAAC,MAAM;EAC/B,MAAA,IAAA,CAAKL,IAAL,EAAA,CAAA;EACD,KAFyB,EAEvB,IAAA,CAAKR,OAAL,CAAalB,KAFU,CAA1B,CAAA;EAGD,GAAA;;EAEDgC,EAAAA,cAAc,CAACC,KAAD,EAAQC,aAAR,EAAuB;MACnC,QAAQD,KAAK,CAACE,IAAd;EACE,MAAA,KAAK,WAAL,CAAA;EACA,MAAA,KAAK,UAAL;EAAiB,QAAA;YACf,IAAK3B,CAAAA,oBAAL,GAA4B0B,aAA5B,CAAA;EACA,UAAA,MAAA;EACD,SAAA;;EAED,MAAA,KAAK,SAAL,CAAA;EACA,MAAA,KAAK,UAAL;EAAiB,QAAA;YACf,IAAKzB,CAAAA,uBAAL,GAA+ByB,aAA/B,CAAA;EACA,UAAA,MAAA;EACD,SAAA;EAXH,KAAA;;EAkBA,IAAA,IAAIA,aAAJ,EAAmB;EACjB,MAAA,IAAA,CAAKjB,aAAL,EAAA,CAAA;;EACA,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,MAAMmB,WAAW,GAAGH,KAAK,CAACI,aAA1B,CAAA;;EACA,IAAA,IAAI,IAAKtB,CAAAA,QAAL,KAAkBqB,WAAlB,IAAiC,IAAA,CAAKrB,QAAL,CAAce,QAAd,CAAuBM,WAAvB,CAArC,EAA0E;EACxE,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKb,kBAAL,EAAA,CAAA;EACD,GAAA;;EAEDb,EAAAA,aAAa,GAAG;EACdG,IAAAA,6BAAY,CAACyB,EAAb,CAAgB,IAAKvB,CAAAA,QAArB,EAA+B9B,eAA/B,EAAgDgD,KAAK,IAAI,KAAKD,cAAL,CAAoBC,KAApB,EAA2B,IAA3B,CAAzD,CAAA,CAAA;EACApB,IAAAA,6BAAY,CAACyB,EAAb,CAAgB,IAAKvB,CAAAA,QAArB,EAA+B7B,cAA/B,EAA+C+C,KAAK,IAAI,KAAKD,cAAL,CAAoBC,KAApB,EAA2B,KAA3B,CAAxD,CAAA,CAAA;EACApB,IAAAA,6BAAY,CAACyB,EAAb,CAAgB,IAAKvB,CAAAA,QAArB,EAA+B5B,aAA/B,EAA8C8C,KAAK,IAAI,KAAKD,cAAL,CAAoBC,KAApB,EAA2B,IAA3B,CAAvD,CAAA,CAAA;EACApB,IAAAA,6BAAY,CAACyB,EAAb,CAAgB,IAAKvB,CAAAA,QAArB,EAA+B3B,cAA/B,EAA+C6C,KAAK,IAAI,KAAKD,cAAL,CAAoBC,KAApB,EAA2B,KAA3B,CAAxD,CAAA,CAAA;EACD,GAAA;;EAEDhB,EAAAA,aAAa,GAAG;MACdsB,YAAY,CAAC,IAAKhC,CAAAA,QAAN,CAAZ,CAAA;MACA,IAAKA,CAAAA,QAAL,GAAgB,IAAhB,CAAA;EACD,GAhJ+B;;;IAmJV,OAAfiC,eAAe,CAAClC,MAAD,EAAS;MAC7B,OAAO,IAAA,CAAKmC,IAAL,CAAU,YAAY;QAC3B,MAAMC,IAAI,GAAGxC,KAAK,CAACyC,mBAAN,CAA0B,IAA1B,EAAgCrC,MAAhC,CAAb,CAAA;;EAEA,MAAA,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,QAAA,IAAI,OAAOoC,IAAI,CAACpC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,UAAA,MAAM,IAAIsC,SAAJ,CAAe,CAAmBtC,iBAAAA,EAAAA,MAAO,GAAzC,CAAN,CAAA;EACD,SAAA;;EAEDoC,QAAAA,IAAI,CAACpC,MAAD,CAAJ,CAAa,IAAb,CAAA,CAAA;EACD,OAAA;EACF,KAVM,CAAP,CAAA;EAWD,GAAA;;EA/J+B,CAAA;EAkKlC;EACA;EACA;;;AAEAuC,yCAAoB,CAAC3C,KAAD,CAApB,CAAA;EAEA;EACA;EACA;;AAEA4C,0BAAkB,CAAC5C,KAAD,CAAlB;;;;;;;;"}