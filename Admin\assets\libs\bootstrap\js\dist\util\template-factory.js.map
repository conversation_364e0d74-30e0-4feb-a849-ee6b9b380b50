{"version": 3, "file": "template-factory.js", "sources": ["../../src/util/template-factory.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): util/template-factory.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { DefaultAllowlist, sanitizeHtml } from './sanitizer'\nimport { getElement, isElement } from '../util/index'\nimport SelectorEngine from '../dom/selector-engine'\nimport Config from './config'\n\n/**\n * Constants\n */\n\nconst NAME = 'TemplateFactory'\n\nconst Default = {\n  allowList: DefaultAllowlist,\n  content: {}, // { selector : text ,  selector2 : text2 , }\n  extraClass: '',\n  html: false,\n  sanitize: true,\n  sanitizeFn: null,\n  template: '<div></div>'\n}\n\nconst DefaultType = {\n  allowList: 'object',\n  content: 'object',\n  extraClass: '(string|function)',\n  html: 'boolean',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  template: 'string'\n}\n\nconst DefaultContentType = {\n  entry: '(string|element|function|null)',\n  selector: '(string|element)'\n}\n\n/**\n * Class definition\n */\n\nclass TemplateFactory extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  getContent() {\n    return Object.values(this._config.content)\n      .map(config => this._resolvePossibleFunction(config))\n      .filter(Boolean)\n  }\n\n  hasContent() {\n    return this.getContent().length > 0\n  }\n\n  changeContent(content) {\n    this._checkContent(content)\n    this._config.content = { ...this._config.content, ...content }\n    return this\n  }\n\n  toHtml() {\n    const templateWrapper = document.createElement('div')\n    templateWrapper.innerHTML = this._maybeSanitize(this._config.template)\n\n    for (const [selector, text] of Object.entries(this._config.content)) {\n      this._setContent(templateWrapper, text, selector)\n    }\n\n    const template = templateWrapper.children[0]\n    const extraClass = this._resolvePossibleFunction(this._config.extraClass)\n\n    if (extraClass) {\n      template.classList.add(...extraClass.split(' '))\n    }\n\n    return template\n  }\n\n  // Private\n  _typeCheckConfig(config) {\n    super._typeCheckConfig(config)\n    this._checkContent(config.content)\n  }\n\n  _checkContent(arg) {\n    for (const [selector, content] of Object.entries(arg)) {\n      super._typeCheckConfig({ selector, entry: content }, DefaultContentType)\n    }\n  }\n\n  _setContent(template, content, selector) {\n    const templateElement = SelectorEngine.findOne(selector, template)\n\n    if (!templateElement) {\n      return\n    }\n\n    content = this._resolvePossibleFunction(content)\n\n    if (!content) {\n      templateElement.remove()\n      return\n    }\n\n    if (isElement(content)) {\n      this._putElementInTemplate(getElement(content), templateElement)\n      return\n    }\n\n    if (this._config.html) {\n      templateElement.innerHTML = this._maybeSanitize(content)\n      return\n    }\n\n    templateElement.textContent = content\n  }\n\n  _maybeSanitize(arg) {\n    return this._config.sanitize ? sanitizeHtml(arg, this._config.allowList, this._config.sanitizeFn) : arg\n  }\n\n  _resolvePossibleFunction(arg) {\n    return typeof arg === 'function' ? arg(this) : arg\n  }\n\n  _putElementInTemplate(element, templateElement) {\n    if (this._config.html) {\n      templateElement.innerHTML = ''\n      templateElement.append(element)\n      return\n    }\n\n    templateElement.textContent = element.textContent\n  }\n}\n\nexport default TemplateFactory\n"], "names": ["NAME", "<PERSON><PERSON><PERSON>", "allowList", "DefaultAllowlist", "content", "extraClass", "html", "sanitize", "sanitizeFn", "template", "DefaultType", "DefaultContentType", "entry", "selector", "TemplateFactory", "Config", "constructor", "config", "_config", "_getConfig", "get<PERSON>ontent", "Object", "values", "map", "_resolvePossibleFunction", "filter", "Boolean", "<PERSON><PERSON><PERSON><PERSON>", "length", "changeContent", "_checkContent", "toHtml", "templateWrapper", "document", "createElement", "innerHTML", "_maybeSanitize", "text", "entries", "_setContent", "children", "classList", "add", "split", "_typeCheckConfig", "arg", "templateElement", "SelectorEngine", "findOne", "remove", "isElement", "_putElementInTemplate", "getElement", "textContent", "sanitizeHtml", "element", "append"], "mappings": ";;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAOA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,iBAAb,CAAA;EAEA,MAAMC,OAAO,GAAG;EACdC,EAAAA,SAAS,EAAEC,0BADG;EAEdC,EAAAA,OAAO,EAAE,EAFK;EAED;EACbC,EAAAA,UAAU,EAAE,EAHE;EAIdC,EAAAA,IAAI,EAAE,KAJQ;EAKdC,EAAAA,QAAQ,EAAE,IALI;EAMdC,EAAAA,UAAU,EAAE,IANE;EAOdC,EAAAA,QAAQ,EAAE,aAAA;EAPI,CAAhB,CAAA;EAUA,MAAMC,WAAW,GAAG;EAClBR,EAAAA,SAAS,EAAE,QADO;EAElBE,EAAAA,OAAO,EAAE,QAFS;EAGlBC,EAAAA,UAAU,EAAE,mBAHM;EAIlBC,EAAAA,IAAI,EAAE,SAJY;EAKlBC,EAAAA,QAAQ,EAAE,SALQ;EAMlBC,EAAAA,UAAU,EAAE,iBANM;EAOlBC,EAAAA,QAAQ,EAAE,QAAA;EAPQ,CAApB,CAAA;EAUA,MAAME,kBAAkB,GAAG;EACzBC,EAAAA,KAAK,EAAE,gCADkB;EAEzBC,EAAAA,QAAQ,EAAE,kBAAA;EAFe,CAA3B,CAAA;EAKA;EACA;EACA;;EAEA,MAAMC,eAAN,SAA8BC,uBAA9B,CAAqC;IACnCC,WAAW,CAACC,MAAD,EAAS;EAClB,IAAA,KAAA,EAAA,CAAA;EACA,IAAA,IAAA,CAAKC,OAAL,GAAe,IAAA,CAAKC,UAAL,CAAgBF,MAAhB,CAAf,CAAA;EACD,GAJkC;;;EAOjB,EAAA,WAAPhB,OAAO,GAAG;EACnB,IAAA,OAAOA,OAAP,CAAA;EACD,GAAA;;EAEqB,EAAA,WAAXS,WAAW,GAAG;EACvB,IAAA,OAAOA,WAAP,CAAA;EACD,GAAA;;EAEc,EAAA,WAAJV,IAAI,GAAG;EAChB,IAAA,OAAOA,IAAP,CAAA;EACD,GAjBkC;;;EAoBnCoB,EAAAA,UAAU,GAAG;MACX,OAAOC,MAAM,CAACC,MAAP,CAAc,KAAKJ,OAAL,CAAad,OAA3B,CAAA,CACJmB,GADI,CACAN,MAAM,IAAI,IAAA,CAAKO,wBAAL,CAA8BP,MAA9B,CADV,CAEJQ,CAAAA,MAFI,CAEGC,OAFH,CAAP,CAAA;EAGD,GAAA;;EAEDC,EAAAA,UAAU,GAAG;EACX,IAAA,OAAO,IAAKP,CAAAA,UAAL,EAAkBQ,CAAAA,MAAlB,GAA2B,CAAlC,CAAA;EACD,GAAA;;IAEDC,aAAa,CAACzB,OAAD,EAAU;MACrB,IAAK0B,CAAAA,aAAL,CAAmB1B,OAAnB,CAAA,CAAA;;MACA,IAAKc,CAAAA,OAAL,CAAad,OAAb,GAAuB,EAAE,GAAG,IAAA,CAAKc,OAAL,CAAad,OAAlB;QAA2B,GAAGA,OAAAA;OAArD,CAAA;EACA,IAAA,OAAO,IAAP,CAAA;EACD,GAAA;;EAED2B,EAAAA,MAAM,GAAG;EACP,IAAA,MAAMC,eAAe,GAAGC,QAAQ,CAACC,aAAT,CAAuB,KAAvB,CAAxB,CAAA;MACAF,eAAe,CAACG,SAAhB,GAA4B,IAAKC,CAAAA,cAAL,CAAoB,IAAKlB,CAAAA,OAAL,CAAaT,QAAjC,CAA5B,CAAA;;EAEA,IAAA,KAAK,MAAM,CAACI,QAAD,EAAWwB,IAAX,CAAX,IAA+BhB,MAAM,CAACiB,OAAP,CAAe,IAAKpB,CAAAA,OAAL,CAAad,OAA5B,CAA/B,EAAqE;EACnE,MAAA,IAAA,CAAKmC,WAAL,CAAiBP,eAAjB,EAAkCK,IAAlC,EAAwCxB,QAAxC,CAAA,CAAA;EACD,KAAA;;EAED,IAAA,MAAMJ,QAAQ,GAAGuB,eAAe,CAACQ,QAAhB,CAAyB,CAAzB,CAAjB,CAAA;;MACA,MAAMnC,UAAU,GAAG,IAAKmB,CAAAA,wBAAL,CAA8B,IAAKN,CAAAA,OAAL,CAAab,UAA3C,CAAnB,CAAA;;EAEA,IAAA,IAAIA,UAAJ,EAAgB;QACdI,QAAQ,CAACgC,SAAT,CAAmBC,GAAnB,CAAuB,GAAGrC,UAAU,CAACsC,KAAX,CAAiB,GAAjB,CAA1B,CAAA,CAAA;EACD,KAAA;;EAED,IAAA,OAAOlC,QAAP,CAAA;EACD,GApDkC;;;IAuDnCmC,gBAAgB,CAAC3B,MAAD,EAAS;MACvB,KAAM2B,CAAAA,gBAAN,CAAuB3B,MAAvB,CAAA,CAAA;;EACA,IAAA,IAAA,CAAKa,aAAL,CAAmBb,MAAM,CAACb,OAA1B,CAAA,CAAA;EACD,GAAA;;IAED0B,aAAa,CAACe,GAAD,EAAM;EACjB,IAAA,KAAK,MAAM,CAAChC,QAAD,EAAWT,OAAX,CAAX,IAAkCiB,MAAM,CAACiB,OAAP,CAAeO,GAAf,CAAlC,EAAuD;EACrD,MAAA,KAAA,CAAMD,gBAAN,CAAuB;UAAE/B,QAAF;EAAYD,QAAAA,KAAK,EAAER,OAAAA;EAAnB,OAAvB,EAAqDO,kBAArD,CAAA,CAAA;EACD,KAAA;EACF,GAAA;;EAED4B,EAAAA,WAAW,CAAC9B,QAAD,EAAWL,OAAX,EAAoBS,QAApB,EAA8B;MACvC,MAAMiC,eAAe,GAAGC,+BAAc,CAACC,OAAf,CAAuBnC,QAAvB,EAAiCJ,QAAjC,CAAxB,CAAA;;MAEA,IAAI,CAACqC,eAAL,EAAsB;EACpB,MAAA,OAAA;EACD,KAAA;;EAED1C,IAAAA,OAAO,GAAG,IAAA,CAAKoB,wBAAL,CAA8BpB,OAA9B,CAAV,CAAA;;MAEA,IAAI,CAACA,OAAL,EAAc;EACZ0C,MAAAA,eAAe,CAACG,MAAhB,EAAA,CAAA;EACA,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAIC,eAAS,CAAC9C,OAAD,CAAb,EAAwB;EACtB,MAAA,IAAA,CAAK+C,qBAAL,CAA2BC,gBAAU,CAAChD,OAAD,CAArC,EAAgD0C,eAAhD,CAAA,CAAA;;EACA,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAI,IAAK5B,CAAAA,OAAL,CAAaZ,IAAjB,EAAuB;EACrBwC,MAAAA,eAAe,CAACX,SAAhB,GAA4B,KAAKC,cAAL,CAAoBhC,OAApB,CAA5B,CAAA;EACA,MAAA,OAAA;EACD,KAAA;;MAED0C,eAAe,CAACO,WAAhB,GAA8BjD,OAA9B,CAAA;EACD,GAAA;;IAEDgC,cAAc,CAACS,GAAD,EAAM;MAClB,OAAO,IAAA,CAAK3B,OAAL,CAAaX,QAAb,GAAwB+C,sBAAY,CAACT,GAAD,EAAM,IAAA,CAAK3B,OAAL,CAAahB,SAAnB,EAA8B,IAAKgB,CAAAA,OAAL,CAAaV,UAA3C,CAApC,GAA6FqC,GAApG,CAAA;EACD,GAAA;;IAEDrB,wBAAwB,CAACqB,GAAD,EAAM;MAC5B,OAAO,OAAOA,GAAP,KAAe,UAAf,GAA4BA,GAAG,CAAC,IAAD,CAA/B,GAAwCA,GAA/C,CAAA;EACD,GAAA;;EAEDM,EAAAA,qBAAqB,CAACI,OAAD,EAAUT,eAAV,EAA2B;EAC9C,IAAA,IAAI,IAAK5B,CAAAA,OAAL,CAAaZ,IAAjB,EAAuB;QACrBwC,eAAe,CAACX,SAAhB,GAA4B,EAA5B,CAAA;QACAW,eAAe,CAACU,MAAhB,CAAuBD,OAAvB,CAAA,CAAA;EACA,MAAA,OAAA;EACD,KAAA;;EAEDT,IAAAA,eAAe,CAACO,WAAhB,GAA8BE,OAAO,CAACF,WAAtC,CAAA;EACD,GAAA;;EA7GkC;;;;;;;;"}