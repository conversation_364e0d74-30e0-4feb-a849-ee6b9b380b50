# Football Management System  M<PERSON><PERSON> et <PERSON><PERSON><PERSON>

# Description du Projet: Système de Gestion de Football
Le Système de Gestion de Football est une plateforme web complète conçue pour gérer divers aspects d'un club ou d'une organisation de football. Le projet intègre plusieurs fonctionnalités clés, chacune visant à offrir une expérience utilisateur riche et interactive pour différents types d'utilisateurs, y compris les utilisateurs gratuits et premium, ainsi qu'un administrateur.

# Home Page 
| ![1](https://github.com/jerbi2026/football_management_system/assets/116197682/5b83b889-8041-445c-b46f-25fcf654e3b2) | ![2](https://github.com/jerbi2026/football_management_system/assets/116197682/8a8b62da-294c-4867-bad6-4a81daf95db2) |
|---|---|
| Home Page | Boutique |
| ![3](https://github.com/jerbi2026/football_management_system/assets/116197682/4147af3f-6432-4b3c-97fd-97439cd64607) | ![4](https://github.com/jerbi2026/football_management_system/assets/116197682/ae08172a-77c7-4aba-bf66-650b1cd92527) |
| Nos Stades | A propos|
| ![5](https://github.com/jerbi2026/football_management_system/assets/116197682/5d0040f0-e9ab-4fdc-bffc-b142c4f10f6a) |  |
| Contact us |  |

# S'authentifier et S'inscrire
| ![6](https://github.com/jerbi2026/football_management_system/assets/116197682/73646f96-442b-48db-bb55-2ebd32776f33) | ![7](https://github.com/jerbi2026/football_management_system/assets/116197682/da3c612d-d7a0-4bb8-b7a2-d4b61415bfe4) |
|---|---|
| ![8](https://github.com/jerbi2026/football_management_system/assets/116197682/2a9f5846-8246-442b-bdde-242aada63b8a) | ![9](https://github.com/jerbi2026/football_management_system/assets/116197682/5663dce5-6ebd-4a03-a06a-b1204d54c052) |
| ![10](https://github.com/jerbi2026/football_management_system/assets/116197682/de438816-d5f8-4935-b51d-941115414237) | ![11](https://github.com/jerbi2026/football_management_system/assets/116197682/21a51e72-c839-47fc-8bcd-24082a54b65d) |
| ![12](https://github.com/jerbi2026/football_management_system/assets/116197682/3bc04641-5158-4b88-943e-ea1c1dd64bbc) | ![13](https://github.com/jerbi2026/football_management_system/assets/116197682/cd49d0ce-f1d4-46b8-bef7-c6b7626fb9d2) |
| ![14](https://github.com/jerbi2026/football_management_system/assets/116197682/5efba11e-4d37-4b02-8042-760c743d4294) | |


# User Free Fonctionnalités 


https://github.com/jerbi2026/football_management_system/assets/116197682/eed27d6a-256a-4061-8b7b-41d2cda95eb0


# User Premuim Fonctionnalités 
Il a les memes fonctionnalités que utilisateur free mais les reservations effectuées par ce type d'utilisateur sont directement validés



https://github.com/jerbi2026/football_management_system/assets/116197682/dac66551-4c05-4412-8d63-f3ee32439c4e



# Admin Fonctionnalités


https://github.com/jerbi2026/football_management_system/assets/116197682/d2f6f629-47d8-4a22-89ce-f27fa739c1f7



# Boutique et Shopping Cart


https://github.com/jerbi2026/football_management_system/assets/116197682/21c8580c-943e-4586-8643-f28dabf7192b



## cookies de cart
![15](https://github.com/jerbi2026/football_management_system/assets/116197682/43699a19-f451-4955-b1e8-375d8f781f3d)


# Chatroom 
Nous n'avons pas travaillé avec websocket mais nous avons ajouté une minuterie, chaque 5 secondes il execute la requete select pour les messages 



https://github.com/jerbi2026/football_management_system/assets/116197682/2f62cad1-b373-414e-baf0-4347facba484


# Version Mobile




| Image 1 | Image 2 | Image 3 |
| ------- | ------- | ------- |
| ![mob1](https://github.com/jerbi2026/football_management_system/assets/116197682/a01ab9ee-5fc2-4b6d-b2d5-e4ec5c33ba53) | ![mob2](https://github.com/jerbi2026/football_management_system/assets/116197682/779e5c32-bbe6-46cb-80f0-566121a6f72b) | ![mob3](https://github.com/jerbi2026/football_management_system/assets/116197682/f610de4e-3bd1-409c-bee9-50d392158d73) |
| ![mob4](https://github.com/jerbi2026/football_management_system/assets/116197682/9272dad9-9929-455f-be40-1cdee4ea2709) | ![mob5](https://github.com/jerbi2026/football_management_system/assets/116197682/c8cad82c-096c-4a15-86dc-ff093137a1b9) | ![mob6](https://github.com/jerbi2026/football_management_system/assets/116197682/a9ea52cc-aa7a-4de6-a0cf-63bcc2f61040) |
| ![mob7](https://github.com/jerbi2026/football_management_system/assets/116197682/2aabdf2d-948c-4240-bfce-dced059dac7e) | ![mob8](https://github.com/jerbi2026/football_management_system/assets/116197682/e13aea75-6d47-44f2-8eed-44d44293229b) | ![mob9](https://github.com/jerbi2026/football_management_system/assets/116197682/52d00c1e-e812-437a-b814-5ab29dab5d9e) |
| ![mob10](https://github.com/jerbi2026/football_management_system/assets/116197682/30606a6b-6763-4cab-b835-ac39169a8f93) |       |       |


# Api et Bibliotheques utilisées
## FullCalendar : 
Pour les calendriers
## RSS : 
pour les actualités de sport 
## OpenWidgets : 
pour les actualités de météo
## Email Js :
Pour l'envoie des emails
## Sessions et cookies 


# nb views 👁️
<img align="left" src = "https://profile-counter.glitch.me/football_management_system/count.svg" alt ="Loading">



