{"version": "7", "about": "This is a Prepros (https://prepros.io) configuration file. You can commit this file to a git repo to backup and sync project configurations.", "config": {"proxy": {"enable": false, "target": "", "useLocalAssets": false}, "reload": {"enable": true, "delay": 0, "animate": true, "afterUpload": false}, "sync": {"enable": false, "mouse": true, "keyboard": true, "form": true, "scroll": true}, "watcher": {"enable": true, "maxFiles": 2000, "usePolling": false, "pollingInterval": 500, "extensions": [".html", ".htm", ".php"], "ignore": {"patterns": [".*", "wp-admin", "wp-includes", "node_modules", "Prepros Export", "bower_components"], "exceptions": []}}, "exporter": {"ignore": {"patterns": [".*", "desktop.ini", "prepros.cfg", "node_modules", "Prepros Export", "prepros.config", "prepros-6.config", "*-original.jpg", "*-original.jpeg", "*-original.png", "*-original.svg", "*.scss", "*.sass", "*.less", "*.pug", "*.jade", "*.styl", "*.haml", "*.slim", "*.coffee", "*.kit", "*.turf", "*.ts"], "exceptions": []}}, "uploader": {"remotePath": "", "timeout": 20000, "autoUpload": false, "reuseConnection": true, "connectionType": "ftp", "history": []}, "packages": {"createPackageLock": true}, "images": {"preserveOriginal": true}, "tasks": {"autoprefixer": {"cascade": true, "add": true, "remove": true, "supports": true, "flexbox": true, "grid": "autoplace", "browsers": ["last 2 versions"], "sourceMap": false}, "babel": {"sourceMap": false, "presets": {"@babel/preset-env": {"enable": true, "options": {"targets": ["last 2 versions"], "preserveImports": false, "polyfills": false}}, "@babel/preset-react": true, "@babel/preset-flow": false}, "plugins": {"@babel/plugin-proposal-class-static-block": false, "@babel/plugin-proposal-class-properties": false, "@babel/plugin-proposal-decorators": {"enable": false, "options": {"decoratorsBeforeExport": true}}, "@babel/plugin-proposal-export-namespace-from": false, "@babel/plugin-proposal-function-sent": false, "@babel/plugin-proposal-logical-assignment-operators": false, "@babel/plugin-proposal-nullish-coalescing-operator": false, "@babel/plugin-proposal-numeric-separator": false, "@babel/plugin-proposal-optional-chaining": false, "@babel/plugin-proposal-private-methods": false, "@babel/plugin-proposal-throw-expressions": false}, "customPresets": [], "customPlugins": []}, "bundle-js": {"sourceMap": false, "exclude": ["node_modules", "bower_components"], "devMode": true, "globals": [], "externals": [], "babel": {"enable": true, "options": {"sourceMap": false, "presets": {"@babel/preset-env": {"enable": true, "options": {"targets": ["last 2 versions"], "preserveImports": false, "polyfills": false}}, "@babel/preset-react": true, "@babel/preset-flow": false}, "plugins": {"@babel/plugin-proposal-class-static-block": false, "@babel/plugin-proposal-class-properties": false, "@babel/plugin-proposal-decorators": {"enable": false, "options": {"decoratorsBeforeExport": true}}, "@babel/plugin-proposal-export-namespace-from": false, "@babel/plugin-proposal-function-sent": false, "@babel/plugin-proposal-logical-assignment-operators": false, "@babel/plugin-proposal-nullish-coalescing-operator": false, "@babel/plugin-proposal-numeric-separator": false, "@babel/plugin-proposal-optional-chaining": false, "@babel/plugin-proposal-private-methods": false, "@babel/plugin-proposal-throw-expressions": false}, "customPresets": [], "customPlugins": []}}, "css": {"enable": true}}, "coffeescript": {"header": false, "bare": false, "sourceMap": false}, "command": {"command": "", "rootDir": ""}, "concat-js": {"sourceMap": false, "rootDir": ""}, "copy": {"sourceMap": false}, "dart-sass": {"indentType": "space", "allowWildcardImports": false, "indentWidth": 2, "linefeed": "lf", "removeCharset": false, "sourceMap": false}, "haml": {"doubleQuoteAttributes": true}, "jpg": {"quality": 90}, "less": {"javascriptEnabled": false, "strictImports": false, "insecure": false, "math": "always", "strictUnits": false, "dumpLineNumbers": false, "sourceMap": false}, "markdown": {"githubFlavored": true, "wrapWithHtml": false}, "minify-css": {"sourceMap": false}, "minify-html": {"caseSensitive": false, "collapseBooleanAttributes": true, "collapseInlineTagWhitespace": false, "collapseWhitespace": true, "conservativeCollapse": false, "decodeEntities": false, "html5": true, "includeAutoGeneratedTags": true, "keepClosingSlash": false, "minifyCSS": true, "minifyJS": true, "preserveLineBreaks": false, "preventAttributesEscaping": false, "processConditionalComments": false, "removeAttributeQuotes": false, "removeComments": true, "removeEmptyAttributes": false, "removeEmptyElement": false, "removeOptionalTags": false, "removeRedundantAttributes": false, "removeScriptTypeAttributes": false, "removeStyleLinkTypeAttributes": false, "removeTagWhitespace": false, "sortAttributes": false, "sortClassName": false, "useShortDoctype": true}, "minify-js": {"parse": {"bare_returns": false}, "compress": {"arrows": true, "arguments": false, "booleans": true, "booleans_as_integers": false, "collapse_vars": true, "comparisons": true, "computed_props": true, "conditionals": true, "dead_code": true, "directives": true, "drop_console": false, "drop_debugger": true, "evaluate": true, "expression": false, "global_defs": [], "hoist_funs": false, "hoist_props": true, "hoist_vars": false, "if_return": true, "inline": 3, "join_vars": true, "keep_fargs": true, "keep_infinity": false, "loops": true, "negate_iife": true, "properties": true, "pure_funcs": [], "pure_getters": false, "reduce_funcs": true, "reduce_vars": true, "sequences": true, "side_effects": true, "switches": true, "top_retain": [], "typeofs": true, "unsafe": false, "unsafe_arrows": false, "unsafe_comps": false, "unsafe_Function": false, "unsafe_math": false, "unsafe_proto": false, "unsafe_regexp": false, "unsafe_undefined": false, "unused": true}, "mangle": {"eval": false, "reserved": []}, "output": {"ascii_only": false, "braces": false, "comments": "none", "inline_script": true, "keep_numbers": false, "keep_quoted_props": false, "preamble": null, "quote_keys": false, "quote_style": 0, "semicolons": true, "shebang": true, "webkit": false, "wrap_iife": false, "wrap_func_args": true}, "sourceMap": false, "toplevel": false, "ie8": false, "keep_classnames": false, "keep_fnames": false, "safari10": false}, "node-sass": {"indentType": "space", "allowWildcardImports": false, "indentWidth": 2, "linefeed": "lf", "outputStyle": "expanded", "precision": 10, "sourceMap": false, "removeCharset": false, "sourceComments": false}, "png": {"quality": 90}, "postcss-import": {"ignoreKeywords": [], "sourceMap": false}, "postcss-preset-env": {"stage": 2, "browsers": ["last 2 versions"], "sourceMap": false}, "pug": {"pretty": true}, "slim": {"indent": "space", "indentSize": 2, "pretty": true}, "stylus": {"useNib": true, "sourceMap": false, "linenos": false}, "svg": {"cleanupAttrs": true, "removeDoctype": true, "removeXMLProcInst": true, "removeComments": true, "removeMetadata": true, "removeTitle": true, "removeDesc": true, "removeUselessDefs": true, "removeEditorsNSData": true, "removeEmptyAttrs": true, "removeHiddenElems": true, "removeEmptyText": true, "removeEmptyContainers": true, "removeViewBox": false, "cleanupEnableBackground": true, "convertStyleToAttrs": true, "convertColors": true, "convertPathData": true, "convertTransform": true, "removeUnknownsAndDefaults": true, "removeNonInheritableGroupAttrs": true, "removeUselessStrokeAndFill": true, "removeUnusedNS": true, "cleanupIDs": true, "cleanupNumericValues": true, "moveElemsAttrsToGroup": true, "moveGroupAttrsToElems": true, "collapseGroups": true, "removeRasterImages": false, "mergePaths": true, "convertShapeToPath": true, "sortAttrs": true, "removeDimensions": true}, "tailwindcss": {"rootDir": "", "content": ["**/*.html", "**/*.php", "**/*.js", "!wp-admin", "!wp-includes", "!node_modules", "!Prepros Export"]}, "turf": {"rootDir": ""}, "typescript": {"allowJs": false, "allowSyntheticDefaultImports": true, "allowUmdGlobalAccess": false, "allowUnreachableCode": false, "allowUnusedLabels": false, "alwaysStrict": false, "charset": "utf8", "checkJs": false, "declaration": false, "disableSizeLimit": false, "downlevelIteration": false, "emitBOM": false, "emitDecoratorMetadata": false, "experimentalDecorators": false, "forceConsistentCasingInFileNames": false, "importHelpers": false, "jsx": "React", "keyofStringsOnly": false, "lib": [], "maxNodeModuleJsDepth": 0, "module": "ES2015", "moduleResolution": "NodeJs", "newLine": "LineFeed", "noFallthroughCasesInSwitch": false, "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "noStrictGenericChecks": false, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitUseStrict": false, "noLib": false, "noResolve": false, "preserveConstEnums": false, "jsxFactory": "React.createElement", "removeComments": false, "skipLibCheck": false, "sourceMap": false, "strict": false, "strictFunctionTypes": false, "strictBindCallApply": false, "strictNullChecks": false, "strictPropertyInitialization": false, "suppressExcessPropertyErrors": false, "suppressImplicitAnyIndexErrors": false, "target": "ES3", "resolveJsonModule": false, "esModuleInterop": false, "useDefineForClassFields": false}}, "fileTypes": {"sass": {"extensions": [".scss", ".sass"], "autoCompile": true, "sourceMap": false, "tasks": [{"task": "dart-sass", "enable": true}, {"task": "autoprefixer", "enable": true}, {"task": "minify-css", "enable": false}], "output": {"extension": ".css", "type": "REPLACE_SEGMENTS", "segments": [{"segment": "scss", "replaceWith": "css"}, {"segment": "sass", "replaceWith": "css"}]}}, "less": {"extensions": [".less"], "autoCompile": true, "sourceMap": false, "tasks": [{"task": "less", "enable": true}, {"task": "autoprefixer", "enable": true}, {"task": "minify-css", "enable": false}], "output": {"extension": ".css", "type": "REPLACE_SEGMENTS", "segments": [{"segment": "less", "replaceWith": "css"}]}}, "pug": {"extensions": [".pug", ".jade"], "autoCompile": true, "tasks": [{"task": "pug", "enable": true}, {"task": "minify-html", "enable": false}], "output": {"extension": ".html", "type": "REPLACE_SEGMENTS", "segments": [{"segment": "pug", "replaceWith": "html"}]}}, "css": {"extensions": [".css"], "autoCompile": false, "sourceMap": false, "tasks": [{"task": "copy", "enable": true}, {"task": "tailwindcss", "enable": false}, {"task": "postcss-import", "enable": false}, {"task": "postcss-preset-env", "enable": false}, {"task": "autoprefixer", "enable": true}, {"task": "minify-css", "enable": true}], "output": {"extension": ".css", "type": "SOURCE_RELATIVE", "relativePath": "", "suffix": "-dist", "alwaysSuffix": false}}, "javascript": {"extensions": [".js", ".jsx"], "autoCompile": false, "sourceMap": false, "tasks": [{"task": "copy", "enable": true}, {"task": "concat-js", "enable": false}, {"task": "babel", "enable": false}, {"task": "bundle-js", "enable": false}, {"task": "minify-js", "enable": true}], "output": {"extension": ".js", "type": "SOURCE_RELATIVE", "relativePath": "", "suffix": "-dist", "alwaysSuffix": false}}, "stylus": {"extensions": [".styl"], "autoCompile": true, "sourceMap": false, "tasks": [{"task": "stylus", "enable": true}, {"task": "autoprefixer", "enable": true}, {"task": "minify-css", "enable": false}], "output": {"extension": ".css", "type": "REPLACE_SEGMENTS", "segments": [{"segment": "stylus", "replaceWith": "css"}, {"segment": "styl", "replaceWith": "css"}]}}, "markdown": {"extensions": [".md", ".markdown", ".mkd"], "autoCompile": false, "tasks": [{"task": "markdown", "enable": true}, {"task": "minify-html", "enable": false}], "output": {"extension": ".html", "type": "REPLACE_SEGMENTS", "segments": [{"segment": "markdown", "replaceWith": "html"}]}}, "haml": {"extensions": [".haml"], "autoCompile": true, "tasks": [{"task": "haml", "enable": true}, {"task": "minify-html", "enable": false}], "output": {"extension": ".html", "type": "REPLACE_SEGMENTS", "segments": [{"segment": "haml", "replaceWith": "html"}]}}, "slim": {"extensions": [".slim"], "autoCompile": true, "tasks": [{"task": "slim", "enable": true}, {"task": "minify-html", "enable": false}], "output": {"extension": ".html", "type": "REPLACE_SEGMENTS", "segments": [{"segment": "slim", "replaceWith": "html"}]}}, "coffeescript": {"extensions": [".coffee"], "autoCompile": true, "sourceMap": false, "tasks": [{"task": "coffeescript", "enable": true}, {"task": "babel", "enable": false}, {"task": "bundle-js", "enable": false}, {"task": "minify-js", "enable": false}], "output": {"extension": ".js", "type": "REPLACE_SEGMENTS", "segments": [{"segment": "coffee-script", "replaceWith": "js"}, {"segment": "coffeescript", "replaceWith": "js"}, {"segment": "coffee", "replaceWith": "js"}]}}, "turf": {"extensions": [".turf", ".kit"], "autoCompile": true, "tasks": [{"task": "turf", "enable": true}, {"task": "minify-html", "enable": false}], "output": {"extension": ".html", "type": "REPLACE_SEGMENTS", "segments": [{"segment": "turf", "replaceWith": "html"}]}}, "typescript": {"extensions": [".ts", ".tsx"], "autoCompile": true, "sourceMap": false, "tasks": [{"task": "typescript", "enable": true}, {"task": "babel", "enable": false}, {"task": "bundle-js", "enable": false}, {"task": "minify-js", "enable": false}], "output": {"extension": ".js", "type": "REPLACE_SEGMENTS", "segments": [{"segment": "typescript", "replaceWith": "js"}, {"segment": "ts", "replaceWith": "js"}]}}, "jpg": {"extensions": [".jpg", ".jpeg"], "tasks": [{"task": "jpg", "enable": true}], "output": {"extension": ".jpg", "type": "SOURCE_RELATIVE", "relativePath": ""}}, "png": {"extensions": [".png"], "tasks": [{"task": "png", "enable": true}], "output": {"extension": ".png", "type": "SOURCE_RELATIVE", "relativePath": ""}}, "svg": {"extensions": [".svg"], "tasks": [{"task": "svg", "enable": true}], "output": {"extension": ".svg", "type": "SOURCE_RELATIVE", "relativePath": ""}}}, "files": [{"file": "styles.scss", "config": {"tasks": {"minify-css": {"enable": true}}, "customOutput": "C:\\Users\\<USER>\\Desktop\\office\\bitbucket\\bootstrap\\Modernize-Free\\src\\assets\\css\\styles.min.css"}}]}}