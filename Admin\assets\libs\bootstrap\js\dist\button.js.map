{"version": 3, "file": "button.js", "sources": ["../src/button.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * Class definition\n */\n\nclass Button extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Button.getOrCreateInstance(this)\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n  const data = Button.getOrCreateInstance(button)\n\n  data.toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Button)\n\nexport default Button\n"], "names": ["NAME", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "CLASS_NAME_ACTIVE", "SELECTOR_DATA_TOGGLE", "EVENT_CLICK_DATA_API", "<PERSON><PERSON>", "BaseComponent", "toggle", "_element", "setAttribute", "classList", "jQueryInterface", "config", "each", "data", "getOrCreateInstance", "EventHandler", "on", "document", "event", "preventDefault", "button", "target", "closest", "defineJQueryPlugin"], "mappings": ";;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAMA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,QAAb,CAAA;EACA,MAAMC,QAAQ,GAAG,WAAjB,CAAA;EACA,MAAMC,SAAS,GAAI,CAAGD,CAAAA,EAAAA,QAAS,CAA/B,CAAA,CAAA;EACA,MAAME,YAAY,GAAG,WAArB,CAAA;EAEA,MAAMC,iBAAiB,GAAG,QAA1B,CAAA;EACA,MAAMC,oBAAoB,GAAG,2BAA7B,CAAA;EACA,MAAMC,oBAAoB,GAAI,CAAA,KAAA,EAAOJ,SAAU,CAAA,EAAEC,YAAa,CAA9D,CAAA,CAAA;EAEA;EACA;EACA;;EAEA,MAAMI,MAAN,SAAqBC,8BAArB,CAAmC;EACjC;EACe,EAAA,WAAJR,IAAI,GAAG;EAChB,IAAA,OAAOA,IAAP,CAAA;EACD,GAJgC;;;EAOjCS,EAAAA,MAAM,GAAG;EACP;EACA,IAAA,IAAA,CAAKC,QAAL,CAAcC,YAAd,CAA2B,cAA3B,EAA2C,IAAA,CAAKD,QAAL,CAAcE,SAAd,CAAwBH,MAAxB,CAA+BL,iBAA/B,CAA3C,CAAA,CAAA;EACD,GAVgC;;;IAaX,OAAfS,eAAe,CAACC,MAAD,EAAS;MAC7B,OAAO,IAAA,CAAKC,IAAL,CAAU,YAAY;EAC3B,MAAA,MAAMC,IAAI,GAAGT,MAAM,CAACU,mBAAP,CAA2B,IAA3B,CAAb,CAAA;;QAEA,IAAIH,MAAM,KAAK,QAAf,EAAyB;UACvBE,IAAI,CAACF,MAAD,CAAJ,EAAA,CAAA;EACD,OAAA;EACF,KANM,CAAP,CAAA;EAOD,GAAA;;EArBgC,CAAA;EAwBnC;EACA;EACA;;;AAEAI,+BAAY,CAACC,EAAb,CAAgBC,QAAhB,EAA0Bd,oBAA1B,EAAgDD,oBAAhD,EAAsEgB,KAAK,IAAI;EAC7EA,EAAAA,KAAK,CAACC,cAAN,EAAA,CAAA;IAEA,MAAMC,MAAM,GAAGF,KAAK,CAACG,MAAN,CAAaC,OAAb,CAAqBpB,oBAArB,CAAf,CAAA;EACA,EAAA,MAAMW,IAAI,GAAGT,MAAM,CAACU,mBAAP,CAA2BM,MAA3B,CAAb,CAAA;EAEAP,EAAAA,IAAI,CAACP,MAAL,EAAA,CAAA;EACD,CAPD,CAAA,CAAA;EASA;EACA;EACA;;AAEAiB,0BAAkB,CAACnB,MAAD,CAAlB;;;;;;;;"}