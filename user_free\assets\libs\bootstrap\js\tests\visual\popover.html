<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../../../dist/css/bootstrap.min.css" rel="stylesheet">
    <title>Popover</title>
  </head>
  <body>
    <div class="container">
      <h1>Popover <small>Bootstrap Visual Test</small></h1>

      <button type="button" class="btn btn-secondary" data-bs-container="body" data-bs-toggle="popover" data-bs-placement="auto" data-bs-content="Vivamus sagittis lacus vel augue laoreet rutrum faucibus.">
        Popover on auto
      </button>

      <button type="button" class="btn btn-secondary" data-bs-container="body" data-bs-toggle="popover" data-bs-placement="top" data-bs-content="Default placement was on top but not enough place">
        Popover on top
      </button>

      <button type="button" class="btn btn-secondary" data-bs-container="body" data-bs-toggle="popover" data-bs-placement="right" data-bs-content="Vivamus sagittis lacus vel augue laoreet rutrum faucibus.">
        Popover on end
      </button>

      <button type="button" class="btn btn-secondary" data-bs-container="body" data-bs-toggle="popover" data-bs-placement="bottom" data-bs-content="Vivamus sagittis lacus vel augue laoreet rutrum faucibus.">
        Popover on bottom
      </button>

      <button type="button" class="btn btn-secondary" data-bs-container="body" data-bs-toggle="popover" data-bs-placement="left" data-bs-content="Vivamus sagittis lacus vel augue laoreet rutrum faucibus.">
        Popover on start
      </button>
    </div>

    <script src="../../../dist/js/bootstrap.bundle.js"></script>
    <script>
      /* global bootstrap: false */

      document.querySelectorAll('[data-bs-toggle="popover"]').forEach(popoverEl => new bootstrap.Popover(popoverEl))
    </script>
  </body>
</html>
