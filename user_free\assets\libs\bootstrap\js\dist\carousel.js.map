{"version": 3, "file": "carousel.js", "sources": ["../src/carousel.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  getNextActiveElement,\n  isRTL,\n  isVisible,\n  reflow,\n  triggerTransitionEnd\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport Swipe from './util/swipe'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ACTIVE_ITEM = SELECTOR_ACTIVE + SELECTOR_ITEM\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst KEY_TO_DIRECTION = {\n  [ARROW_LEFT_KEY]: DIRECTION_RIGHT,\n  [ARROW_RIGHT_KEY]: DIRECTION_LEFT\n}\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  pause: 'hover',\n  ride: false,\n  touch: true,\n  wrap: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)', // TODO:v6 remove boolean support\n  keyboard: 'boolean',\n  pause: '(string|boolean)',\n  ride: '(boolean|string)',\n  touch: 'boolean',\n  wrap: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._interval = null\n    this._activeElement = null\n    this._isSliding = false\n    this.touchTimeout = null\n    this._swipeHelper = null\n\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._addEventListeners()\n\n    if (this._config.ride === CLASS_NAME_CAROUSEL) {\n      this.cycle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  next() {\n    this._slide(ORDER_NEXT)\n  }\n\n  nextWhenVisible() {\n    // FIXME TODO use `document.visibilityState`\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    this._slide(ORDER_PREV)\n  }\n\n  pause() {\n    if (this._isSliding) {\n      triggerTransitionEnd(this._element)\n    }\n\n    this._clearInterval()\n  }\n\n  cycle() {\n    this._clearInterval()\n    this._updateInterval()\n\n    this._interval = setInterval(() => this.nextWhenVisible(), this._config.interval)\n  }\n\n  _maybeEnableCycle() {\n    if (!this._config.ride) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.cycle())\n      return\n    }\n\n    this.cycle()\n  }\n\n  to(index) {\n    const items = this._getItems()\n    if (index > items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    const activeIndex = this._getItemIndex(this._getActive())\n    if (activeIndex === index) {\n      return\n    }\n\n    const order = index > activeIndex ? ORDER_NEXT : ORDER_PREV\n\n    this._slide(order, items[index])\n  }\n\n  dispose() {\n    if (this._swipeHelper) {\n      this._swipeHelper.dispose()\n    }\n\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.defaultInterval = config.interval\n    return config\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, () => this.pause())\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, () => this._maybeEnableCycle())\n    }\n\n    if (this._config.touch && Swipe.isSupported()) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    for (const img of SelectorEngine.find(SELECTOR_ITEM_IMG, this._element)) {\n      EventHandler.on(img, EVENT_DRAG_START, event => event.preventDefault())\n    }\n\n    const endCallBack = () => {\n      if (this._config.pause !== 'hover') {\n        return\n      }\n\n      // If it's a touch-enabled device, mouseenter/leave are fired as\n      // part of the mouse compatibility events on first tap - the carousel\n      // would stop cycling until user tapped out of it;\n      // here, we listen for touchend, explicitly pause the carousel\n      // (as if it's the second time we tap on it, mouseenter compat event\n      // is NOT fired) and after a timeout (to allow for mouse compatibility\n      // events to fire) we explicitly restart cycling\n\n      this.pause()\n      if (this.touchTimeout) {\n        clearTimeout(this.touchTimeout)\n      }\n\n      this.touchTimeout = setTimeout(() => this._maybeEnableCycle(), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n    }\n\n    const swipeConfig = {\n      leftCallback: () => this._slide(this._directionToOrder(DIRECTION_LEFT)),\n      rightCallback: () => this._slide(this._directionToOrder(DIRECTION_RIGHT)),\n      endCallback: endCallBack\n    }\n\n    this._swipeHelper = new Swipe(this._element, swipeConfig)\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    const direction = KEY_TO_DIRECTION[event.key]\n    if (direction) {\n      event.preventDefault()\n      this._slide(this._directionToOrder(direction))\n    }\n  }\n\n  _getItemIndex(element) {\n    return this._getItems().indexOf(element)\n  }\n\n  _setActiveIndicatorElement(index) {\n    if (!this._indicatorsElement) {\n      return\n    }\n\n    const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n    activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n    activeIndicator.removeAttribute('aria-current')\n\n    const newActiveIndicator = SelectorEngine.findOne(`[data-bs-slide-to=\"${index}\"]`, this._indicatorsElement)\n\n    if (newActiveIndicator) {\n      newActiveIndicator.classList.add(CLASS_NAME_ACTIVE)\n      newActiveIndicator.setAttribute('aria-current', 'true')\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || this._getActive()\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    this._config.interval = elementInterval || this._config.defaultInterval\n  }\n\n  _slide(order, element = null) {\n    if (this._isSliding) {\n      return\n    }\n\n    const activeElement = this._getActive()\n    const isNext = order === ORDER_NEXT\n    const nextElement = element || getNextActiveElement(this._getItems(), activeElement, isNext, this._config.wrap)\n\n    if (nextElement === activeElement) {\n      return\n    }\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n\n    const triggerEvent = eventName => {\n      return EventHandler.trigger(this._element, eventName, {\n        relatedTarget: nextElement,\n        direction: this._orderToDirection(order),\n        from: this._getItemIndex(activeElement),\n        to: nextElementIndex\n      })\n    }\n\n    const slideEvent = triggerEvent(EVENT_SLIDE)\n\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      // todo: change tests that use empty divs to avoid this check\n      return\n    }\n\n    const isCycling = Boolean(this._interval)\n    this.pause()\n\n    this._isSliding = true\n\n    this._setActiveIndicatorElement(nextElementIndex)\n    this._activeElement = nextElement\n\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n\n    nextElement.classList.add(orderClassName)\n\n    reflow(nextElement)\n\n    activeElement.classList.add(directionalClassName)\n    nextElement.classList.add(directionalClassName)\n\n    const completeCallBack = () => {\n      nextElement.classList.remove(directionalClassName, orderClassName)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n      this._isSliding = false\n\n      triggerEvent(EVENT_SLID)\n    }\n\n    this._queueCallback(completeCallBack, activeElement, this._isAnimated())\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_SLIDE)\n  }\n\n  _getActive() {\n    return SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n  }\n\n  _getItems() {\n    return SelectorEngine.find(SELECTOR_ITEM, this._element)\n  }\n\n  _clearInterval() {\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (isRTL()) {\n      return direction === DIRECTION_LEFT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_LEFT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (isRTL()) {\n      return order === ORDER_PREV ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_PREV ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Carousel.getOrCreateInstance(this, config)\n\n      if (typeof config === 'number') {\n        data.to(config)\n        return\n      }\n\n      if (typeof config === 'string') {\n        if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n    return\n  }\n\n  event.preventDefault()\n\n  const carousel = Carousel.getOrCreateInstance(target)\n  const slideIndex = this.getAttribute('data-bs-slide-to')\n\n  if (slideIndex) {\n    carousel.to(slideIndex)\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  if (Manipulator.getDataAttribute(this, 'slide') === 'next') {\n    carousel.next()\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  carousel.prev()\n  carousel._maybeEnableCycle()\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (const carousel of carousels) {\n    Carousel.getOrCreateInstance(carousel)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Carousel)\n\nexport default Carousel\n"], "names": ["NAME", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "TOUCHEVENT_COMPAT_WAIT", "ORDER_NEXT", "ORDER_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLIDE", "EVENT_SLID", "EVENT_KEYDOWN", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "EVENT_DRAG_START", "EVENT_LOAD_DATA_API", "EVENT_CLICK_DATA_API", "CLASS_NAME_CAROUSEL", "CLASS_NAME_ACTIVE", "CLASS_NAME_SLIDE", "CLASS_NAME_END", "CLASS_NAME_START", "CLASS_NAME_NEXT", "CLASS_NAME_PREV", "SELECTOR_ACTIVE", "SELECTOR_ITEM", "SELECTOR_ACTIVE_ITEM", "SELECTOR_ITEM_IMG", "SELECTOR_INDICATORS", "SELECTOR_DATA_SLIDE", "SELECTOR_DATA_RIDE", "KEY_TO_DIRECTION", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "pause", "ride", "touch", "wrap", "DefaultType", "Carousel", "BaseComponent", "constructor", "element", "config", "_interval", "_activeElement", "_isSliding", "touchTimeout", "_swipe<PERSON><PERSON>per", "_indicatorsElement", "SelectorEngine", "findOne", "_element", "_addEventListeners", "_config", "cycle", "next", "_slide", "nextWhenVisible", "document", "hidden", "isVisible", "prev", "triggerTransitionEnd", "_clearInterval", "_updateInterval", "setInterval", "_maybeEnableCycle", "EventHandler", "one", "to", "index", "items", "_getItems", "length", "activeIndex", "_getItemIndex", "_getActive", "order", "dispose", "_configAfterMerge", "defaultInterval", "on", "event", "_keydown", "Swipe", "isSupported", "_addTouchEventListeners", "img", "find", "preventDefault", "endCallBack", "clearTimeout", "setTimeout", "swipeConfig", "leftCallback", "_directionToOrder", "<PERSON><PERSON><PERSON><PERSON>", "endCallback", "test", "target", "tagName", "direction", "key", "indexOf", "_setActiveIndicatorElement", "activeIndicator", "classList", "remove", "removeAttribute", "newActiveIndicator", "add", "setAttribute", "elementInterval", "Number", "parseInt", "getAttribute", "activeElement", "isNext", "nextElement", "getNextActiveElement", "nextElementIndex", "triggerEvent", "eventName", "trigger", "relatedTarget", "_orderToDirection", "from", "slideEvent", "defaultPrevented", "isCycling", "Boolean", "directionalClassName", "orderClassName", "reflow", "completeCallBack", "_queueCallback", "_isAnimated", "contains", "clearInterval", "isRTL", "jQueryInterface", "each", "data", "getOrCreateInstance", "undefined", "startsWith", "TypeError", "getElementFromSelector", "carousel", "slideIndex", "Manipulator", "getDataAttribute", "window", "carousels", "defineJQueryPlugin"], "mappings": ";;;;;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAiBA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,UAAb,CAAA;EACA,MAAMC,QAAQ,GAAG,aAAjB,CAAA;EACA,MAAMC,SAAS,GAAI,CAAGD,CAAAA,EAAAA,QAAS,CAA/B,CAAA,CAAA;EACA,MAAME,YAAY,GAAG,WAArB,CAAA;EAEA,MAAMC,cAAc,GAAG,WAAvB,CAAA;EACA,MAAMC,eAAe,GAAG,YAAxB,CAAA;EACA,MAAMC,sBAAsB,GAAG,GAA/B;;EAEA,MAAMC,UAAU,GAAG,MAAnB,CAAA;EACA,MAAMC,UAAU,GAAG,MAAnB,CAAA;EACA,MAAMC,cAAc,GAAG,MAAvB,CAAA;EACA,MAAMC,eAAe,GAAG,OAAxB,CAAA;EAEA,MAAMC,WAAW,GAAI,CAAOT,KAAAA,EAAAA,SAAU,CAAtC,CAAA,CAAA;EACA,MAAMU,UAAU,GAAI,CAAMV,IAAAA,EAAAA,SAAU,CAApC,CAAA,CAAA;EACA,MAAMW,aAAa,GAAI,CAASX,OAAAA,EAAAA,SAAU,CAA1C,CAAA,CAAA;EACA,MAAMY,gBAAgB,GAAI,CAAYZ,UAAAA,EAAAA,SAAU,CAAhD,CAAA,CAAA;EACA,MAAMa,gBAAgB,GAAI,CAAYb,UAAAA,EAAAA,SAAU,CAAhD,CAAA,CAAA;EACA,MAAMc,gBAAgB,GAAI,CAAWd,SAAAA,EAAAA,SAAU,CAA/C,CAAA,CAAA;EACA,MAAMe,mBAAmB,GAAI,CAAA,IAAA,EAAMf,SAAU,CAAA,EAAEC,YAAa,CAA5D,CAAA,CAAA;EACA,MAAMe,oBAAoB,GAAI,CAAA,KAAA,EAAOhB,SAAU,CAAA,EAAEC,YAAa,CAA9D,CAAA,CAAA;EAEA,MAAMgB,mBAAmB,GAAG,UAA5B,CAAA;EACA,MAAMC,iBAAiB,GAAG,QAA1B,CAAA;EACA,MAAMC,gBAAgB,GAAG,OAAzB,CAAA;EACA,MAAMC,cAAc,GAAG,mBAAvB,CAAA;EACA,MAAMC,gBAAgB,GAAG,qBAAzB,CAAA;EACA,MAAMC,eAAe,GAAG,oBAAxB,CAAA;EACA,MAAMC,eAAe,GAAG,oBAAxB,CAAA;EAEA,MAAMC,eAAe,GAAG,SAAxB,CAAA;EACA,MAAMC,aAAa,GAAG,gBAAtB,CAAA;EACA,MAAMC,oBAAoB,GAAGF,eAAe,GAAGC,aAA/C,CAAA;EACA,MAAME,iBAAiB,GAAG,oBAA1B,CAAA;EACA,MAAMC,mBAAmB,GAAG,sBAA5B,CAAA;EACA,MAAMC,mBAAmB,GAAG,qCAA5B,CAAA;EACA,MAAMC,kBAAkB,GAAG,2BAA3B,CAAA;EAEA,MAAMC,gBAAgB,GAAG;IACvB,CAAC7B,cAAD,GAAkBM,eADK;EAEvB,EAAA,CAACL,eAAD,GAAmBI,cAAAA;EAFI,CAAzB,CAAA;EAKA,MAAMyB,OAAO,GAAG;EACdC,EAAAA,QAAQ,EAAE,IADI;EAEdC,EAAAA,QAAQ,EAAE,IAFI;EAGdC,EAAAA,KAAK,EAAE,OAHO;EAIdC,EAAAA,IAAI,EAAE,KAJQ;EAKdC,EAAAA,KAAK,EAAE,IALO;EAMdC,EAAAA,IAAI,EAAE,IAAA;EANQ,CAAhB,CAAA;EASA,MAAMC,WAAW,GAAG;EAClBN,EAAAA,QAAQ,EAAE,kBADQ;EACY;EAC9BC,EAAAA,QAAQ,EAAE,SAFQ;EAGlBC,EAAAA,KAAK,EAAE,kBAHW;EAIlBC,EAAAA,IAAI,EAAE,kBAJY;EAKlBC,EAAAA,KAAK,EAAE,SALW;EAMlBC,EAAAA,IAAI,EAAE,SAAA;EANY,CAApB,CAAA;EASA;EACA;EACA;;EAEA,MAAME,QAAN,SAAuBC,8BAAvB,CAAqC;EACnCC,EAAAA,WAAW,CAACC,OAAD,EAAUC,MAAV,EAAkB;MAC3B,KAAMD,CAAAA,OAAN,EAAeC,MAAf,CAAA,CAAA;MAEA,IAAKC,CAAAA,SAAL,GAAiB,IAAjB,CAAA;MACA,IAAKC,CAAAA,cAAL,GAAsB,IAAtB,CAAA;MACA,IAAKC,CAAAA,UAAL,GAAkB,KAAlB,CAAA;MACA,IAAKC,CAAAA,YAAL,GAAoB,IAApB,CAAA;MACA,IAAKC,CAAAA,YAAL,GAAoB,IAApB,CAAA;MAEA,IAAKC,CAAAA,kBAAL,GAA0BC,+BAAc,CAACC,OAAf,CAAuBxB,mBAAvB,EAA4C,IAAKyB,CAAAA,QAAjD,CAA1B,CAAA;;EACA,IAAA,IAAA,CAAKC,kBAAL,EAAA,CAAA;;EAEA,IAAA,IAAI,KAAKC,OAAL,CAAanB,IAAb,KAAsBnB,mBAA1B,EAA+C;EAC7C,MAAA,IAAA,CAAKuC,KAAL,EAAA,CAAA;EACD,KAAA;EACF,GAhBkC;;;EAmBjB,EAAA,WAAPxB,OAAO,GAAG;EACnB,IAAA,OAAOA,OAAP,CAAA;EACD,GAAA;;EAEqB,EAAA,WAAXO,WAAW,GAAG;EACvB,IAAA,OAAOA,WAAP,CAAA;EACD,GAAA;;EAEc,EAAA,WAAJzC,IAAI,GAAG;EAChB,IAAA,OAAOA,IAAP,CAAA;EACD,GA7BkC;;;EAgCnC2D,EAAAA,IAAI,GAAG;MACL,IAAKC,CAAAA,MAAL,CAAYrD,UAAZ,CAAA,CAAA;EACD,GAAA;;EAEDsD,EAAAA,eAAe,GAAG;EAChB;EACA;EACA;MACA,IAAI,CAACC,QAAQ,CAACC,MAAV,IAAoBC,eAAS,CAAC,IAAA,CAAKT,QAAN,CAAjC,EAAkD;EAChD,MAAA,IAAA,CAAKI,IAAL,EAAA,CAAA;EACD,KAAA;EACF,GAAA;;EAEDM,EAAAA,IAAI,GAAG;MACL,IAAKL,CAAAA,MAAL,CAAYpD,UAAZ,CAAA,CAAA;EACD,GAAA;;EAED6B,EAAAA,KAAK,GAAG;MACN,IAAI,IAAA,CAAKY,UAAT,EAAqB;QACnBiB,0BAAoB,CAAC,IAAKX,CAAAA,QAAN,CAApB,CAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKY,cAAL,EAAA,CAAA;EACD,GAAA;;EAEDT,EAAAA,KAAK,GAAG;EACN,IAAA,IAAA,CAAKS,cAAL,EAAA,CAAA;;EACA,IAAA,IAAA,CAAKC,eAAL,EAAA,CAAA;;EAEA,IAAA,IAAA,CAAKrB,SAAL,GAAiBsB,WAAW,CAAC,MAAM,IAAA,CAAKR,eAAL,EAAP,EAA+B,IAAA,CAAKJ,OAAL,CAAatB,QAA5C,CAA5B,CAAA;EACD,GAAA;;EAEDmC,EAAAA,iBAAiB,GAAG;EAClB,IAAA,IAAI,CAAC,IAAA,CAAKb,OAAL,CAAanB,IAAlB,EAAwB;EACtB,MAAA,OAAA;EACD,KAAA;;MAED,IAAI,IAAA,CAAKW,UAAT,EAAqB;QACnBsB,6BAAY,CAACC,GAAb,CAAiB,IAAKjB,CAAAA,QAAtB,EAAgC3C,UAAhC,EAA4C,MAAM,IAAK8C,CAAAA,KAAL,EAAlD,CAAA,CAAA;EACA,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKA,KAAL,EAAA,CAAA;EACD,GAAA;;IAEDe,EAAE,CAACC,KAAD,EAAQ;EACR,IAAA,MAAMC,KAAK,GAAG,IAAKC,CAAAA,SAAL,EAAd,CAAA;;MACA,IAAIF,KAAK,GAAGC,KAAK,CAACE,MAAN,GAAe,CAAvB,IAA4BH,KAAK,GAAG,CAAxC,EAA2C;EACzC,MAAA,OAAA;EACD,KAAA;;MAED,IAAI,IAAA,CAAKzB,UAAT,EAAqB;EACnBsB,MAAAA,6BAAY,CAACC,GAAb,CAAiB,IAAA,CAAKjB,QAAtB,EAAgC3C,UAAhC,EAA4C,MAAM,IAAA,CAAK6D,EAAL,CAAQC,KAAR,CAAlD,CAAA,CAAA;EACA,MAAA,OAAA;EACD,KAAA;;MAED,MAAMI,WAAW,GAAG,IAAKC,CAAAA,aAAL,CAAmB,IAAKC,CAAAA,UAAL,EAAnB,CAApB,CAAA;;MACA,IAAIF,WAAW,KAAKJ,KAApB,EAA2B;EACzB,MAAA,OAAA;EACD,KAAA;;MAED,MAAMO,KAAK,GAAGP,KAAK,GAAGI,WAAR,GAAsBvE,UAAtB,GAAmCC,UAAjD,CAAA;;EAEA,IAAA,IAAA,CAAKoD,MAAL,CAAYqB,KAAZ,EAAmBN,KAAK,CAACD,KAAD,CAAxB,CAAA,CAAA;EACD,GAAA;;EAEDQ,EAAAA,OAAO,GAAG;MACR,IAAI,IAAA,CAAK/B,YAAT,EAAuB;QACrB,IAAKA,CAAAA,YAAL,CAAkB+B,OAAlB,EAAA,CAAA;EACD,KAAA;;EAED,IAAA,KAAA,CAAMA,OAAN,EAAA,CAAA;EACD,GAxGkC;;;IA2GnCC,iBAAiB,CAACrC,MAAD,EAAS;EACxBA,IAAAA,MAAM,CAACsC,eAAP,GAAyBtC,MAAM,CAACX,QAAhC,CAAA;EACA,IAAA,OAAOW,MAAP,CAAA;EACD,GAAA;;EAEDU,EAAAA,kBAAkB,GAAG;EACnB,IAAA,IAAI,IAAKC,CAAAA,OAAL,CAAarB,QAAjB,EAA2B;EACzBmC,MAAAA,6BAAY,CAACc,EAAb,CAAgB,IAAA,CAAK9B,QAArB,EAA+B1C,aAA/B,EAA8CyE,KAAK,IAAI,IAAA,CAAKC,QAAL,CAAcD,KAAd,CAAvD,CAAA,CAAA;EACD,KAAA;;EAED,IAAA,IAAI,KAAK7B,OAAL,CAAapB,KAAb,KAAuB,OAA3B,EAAoC;QAClCkC,6BAAY,CAACc,EAAb,CAAgB,IAAK9B,CAAAA,QAArB,EAA+BzC,gBAA/B,EAAiD,MAAM,IAAKuB,CAAAA,KAAL,EAAvD,CAAA,CAAA;QACAkC,6BAAY,CAACc,EAAb,CAAgB,IAAK9B,CAAAA,QAArB,EAA+BxC,gBAA/B,EAAiD,MAAM,IAAKuD,CAAAA,iBAAL,EAAvD,CAAA,CAAA;EACD,KAAA;;MAED,IAAI,IAAA,CAAKb,OAAL,CAAalB,KAAb,IAAsBiD,sBAAK,CAACC,WAAN,EAA1B,EAA+C;EAC7C,MAAA,IAAA,CAAKC,uBAAL,EAAA,CAAA;EACD,KAAA;EACF,GAAA;;EAEDA,EAAAA,uBAAuB,GAAG;EACxB,IAAA,KAAK,MAAMC,GAAX,IAAkBtC,+BAAc,CAACuC,IAAf,CAAoB/D,iBAApB,EAAuC,IAAA,CAAK0B,QAA5C,CAAlB,EAAyE;EACvEgB,MAAAA,6BAAY,CAACc,EAAb,CAAgBM,GAAhB,EAAqB3E,gBAArB,EAAuCsE,KAAK,IAAIA,KAAK,CAACO,cAAN,EAAhD,CAAA,CAAA;EACD,KAAA;;MAED,MAAMC,WAAW,GAAG,MAAM;EACxB,MAAA,IAAI,KAAKrC,OAAL,CAAapB,KAAb,KAAuB,OAA3B,EAAoC;EAClC,QAAA,OAAA;EACD,OAHuB;EAMxB;EACA;EACA;EACA;EACA;EACA;;;EAEA,MAAA,IAAA,CAAKA,KAAL,EAAA,CAAA;;QACA,IAAI,IAAA,CAAKa,YAAT,EAAuB;UACrB6C,YAAY,CAAC,IAAK7C,CAAAA,YAAN,CAAZ,CAAA;EACD,OAAA;;EAED,MAAA,IAAA,CAAKA,YAAL,GAAoB8C,UAAU,CAAC,MAAM,IAAK1B,CAAAA,iBAAL,EAAP,EAAiChE,sBAAsB,GAAG,IAAA,CAAKmD,OAAL,CAAatB,QAAvE,CAA9B,CAAA;OAlBF,CAAA;;EAqBA,IAAA,MAAM8D,WAAW,GAAG;QAClBC,YAAY,EAAE,MAAM,IAAA,CAAKtC,MAAL,CAAY,KAAKuC,iBAAL,CAAuB1F,cAAvB,CAAZ,CADF;QAElB2F,aAAa,EAAE,MAAM,IAAA,CAAKxC,MAAL,CAAY,KAAKuC,iBAAL,CAAuBzF,eAAvB,CAAZ,CAFH;EAGlB2F,MAAAA,WAAW,EAAEP,WAAAA;OAHf,CAAA;MAMA,IAAK3C,CAAAA,YAAL,GAAoB,IAAIqC,sBAAJ,CAAU,IAAKjC,CAAAA,QAAf,EAAyB0C,WAAzB,CAApB,CAAA;EACD,GAAA;;IAEDV,QAAQ,CAACD,KAAD,EAAQ;MACd,IAAI,iBAAA,CAAkBgB,IAAlB,CAAuBhB,KAAK,CAACiB,MAAN,CAAaC,OAApC,CAAJ,EAAkD;EAChD,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,MAAMC,SAAS,GAAGxE,gBAAgB,CAACqD,KAAK,CAACoB,GAAP,CAAlC,CAAA;;EACA,IAAA,IAAID,SAAJ,EAAe;EACbnB,MAAAA,KAAK,CAACO,cAAN,EAAA,CAAA;;EACA,MAAA,IAAA,CAAKjC,MAAL,CAAY,IAAA,CAAKuC,iBAAL,CAAuBM,SAAvB,CAAZ,CAAA,CAAA;EACD,KAAA;EACF,GAAA;;IAED1B,aAAa,CAAClC,OAAD,EAAU;EACrB,IAAA,OAAO,KAAK+B,SAAL,EAAA,CAAiB+B,OAAjB,CAAyB9D,OAAzB,CAAP,CAAA;EACD,GAAA;;IAED+D,0BAA0B,CAAClC,KAAD,EAAQ;MAChC,IAAI,CAAC,IAAKtB,CAAAA,kBAAV,EAA8B;EAC5B,MAAA,OAAA;EACD,KAAA;;MAED,MAAMyD,eAAe,GAAGxD,+BAAc,CAACC,OAAf,CAAuB5B,eAAvB,EAAwC,IAAK0B,CAAAA,kBAA7C,CAAxB,CAAA;EAEAyD,IAAAA,eAAe,CAACC,SAAhB,CAA0BC,MAA1B,CAAiC3F,iBAAjC,CAAA,CAAA;MACAyF,eAAe,CAACG,eAAhB,CAAgC,cAAhC,CAAA,CAAA;EAEA,IAAA,MAAMC,kBAAkB,GAAG5D,+BAAc,CAACC,OAAf,CAAwB,CAAqBoB,mBAAAA,EAAAA,KAAM,CAAnD,EAAA,CAAA,EAAwD,IAAKtB,CAAAA,kBAA7D,CAA3B,CAAA;;EAEA,IAAA,IAAI6D,kBAAJ,EAAwB;EACtBA,MAAAA,kBAAkB,CAACH,SAAnB,CAA6BI,GAA7B,CAAiC9F,iBAAjC,CAAA,CAAA;EACA6F,MAAAA,kBAAkB,CAACE,YAAnB,CAAgC,cAAhC,EAAgD,MAAhD,CAAA,CAAA;EACD,KAAA;EACF,GAAA;;EAED/C,EAAAA,eAAe,GAAG;EAChB,IAAA,MAAMvB,OAAO,GAAG,IAAA,CAAKG,cAAL,IAAuB,IAAA,CAAKgC,UAAL,EAAvC,CAAA;;MAEA,IAAI,CAACnC,OAAL,EAAc;EACZ,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,MAAMuE,eAAe,GAAGC,MAAM,CAACC,QAAP,CAAgBzE,OAAO,CAAC0E,YAAR,CAAqB,kBAArB,CAAhB,EAA0D,EAA1D,CAAxB,CAAA;MAEA,IAAK9D,CAAAA,OAAL,CAAatB,QAAb,GAAwBiF,eAAe,IAAI,IAAA,CAAK3D,OAAL,CAAa2B,eAAxD,CAAA;EACD,GAAA;;EAEDxB,EAAAA,MAAM,CAACqB,KAAD,EAAQpC,OAAO,GAAG,IAAlB,EAAwB;MAC5B,IAAI,IAAA,CAAKI,UAAT,EAAqB;EACnB,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,MAAMuE,aAAa,GAAG,IAAKxC,CAAAA,UAAL,EAAtB,CAAA;;EACA,IAAA,MAAMyC,MAAM,GAAGxC,KAAK,KAAK1E,UAAzB,CAAA;EACA,IAAA,MAAMmH,WAAW,GAAG7E,OAAO,IAAI8E,0BAAoB,CAAC,KAAK/C,SAAL,EAAD,EAAmB4C,aAAnB,EAAkCC,MAAlC,EAA0C,KAAKhE,OAAL,CAAajB,IAAvD,CAAnD,CAAA;;MAEA,IAAIkF,WAAW,KAAKF,aAApB,EAAmC;EACjC,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,MAAMI,gBAAgB,GAAG,IAAA,CAAK7C,aAAL,CAAmB2C,WAAnB,CAAzB,CAAA;;MAEA,MAAMG,YAAY,GAAGC,SAAS,IAAI;QAChC,OAAOvD,6BAAY,CAACwD,OAAb,CAAqB,KAAKxE,QAA1B,EAAoCuE,SAApC,EAA+C;EACpDE,QAAAA,aAAa,EAAEN,WADqC;EAEpDjB,QAAAA,SAAS,EAAE,IAAA,CAAKwB,iBAAL,CAAuBhD,KAAvB,CAFyC;EAGpDiD,QAAAA,IAAI,EAAE,IAAA,CAAKnD,aAAL,CAAmByC,aAAnB,CAH8C;EAIpD/C,QAAAA,EAAE,EAAEmD,gBAAAA;EAJgD,OAA/C,CAAP,CAAA;OADF,CAAA;;EASA,IAAA,MAAMO,UAAU,GAAGN,YAAY,CAAClH,WAAD,CAA/B,CAAA;;MAEA,IAAIwH,UAAU,CAACC,gBAAf,EAAiC;EAC/B,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAI,CAACZ,aAAD,IAAkB,CAACE,WAAvB,EAAoC;EAClC;EACA;EACA,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,MAAMW,SAAS,GAAGC,OAAO,CAAC,IAAA,CAAKvF,SAAN,CAAzB,CAAA;EACA,IAAA,IAAA,CAAKV,KAAL,EAAA,CAAA;MAEA,IAAKY,CAAAA,UAAL,GAAkB,IAAlB,CAAA;;MAEA,IAAK2D,CAAAA,0BAAL,CAAgCgB,gBAAhC,CAAA,CAAA;;MACA,IAAK5E,CAAAA,cAAL,GAAsB0E,WAAtB,CAAA;EAEA,IAAA,MAAMa,oBAAoB,GAAGd,MAAM,GAAGlG,gBAAH,GAAsBD,cAAzD,CAAA;EACA,IAAA,MAAMkH,cAAc,GAAGf,MAAM,GAAGjG,eAAH,GAAqBC,eAAlD,CAAA;EAEAiG,IAAAA,WAAW,CAACZ,SAAZ,CAAsBI,GAAtB,CAA0BsB,cAA1B,CAAA,CAAA;MAEAC,YAAM,CAACf,WAAD,CAAN,CAAA;EAEAF,IAAAA,aAAa,CAACV,SAAd,CAAwBI,GAAxB,CAA4BqB,oBAA5B,CAAA,CAAA;EACAb,IAAAA,WAAW,CAACZ,SAAZ,CAAsBI,GAAtB,CAA0BqB,oBAA1B,CAAA,CAAA;;MAEA,MAAMG,gBAAgB,GAAG,MAAM;EAC7BhB,MAAAA,WAAW,CAACZ,SAAZ,CAAsBC,MAAtB,CAA6BwB,oBAA7B,EAAmDC,cAAnD,CAAA,CAAA;EACAd,MAAAA,WAAW,CAACZ,SAAZ,CAAsBI,GAAtB,CAA0B9F,iBAA1B,CAAA,CAAA;QAEAoG,aAAa,CAACV,SAAd,CAAwBC,MAAxB,CAA+B3F,iBAA/B,EAAkDoH,cAAlD,EAAkED,oBAAlE,CAAA,CAAA;QAEA,IAAKtF,CAAAA,UAAL,GAAkB,KAAlB,CAAA;QAEA4E,YAAY,CAACjH,UAAD,CAAZ,CAAA;OARF,CAAA;;MAWA,IAAK+H,CAAAA,cAAL,CAAoBD,gBAApB,EAAsClB,aAAtC,EAAqD,IAAA,CAAKoB,WAAL,EAArD,CAAA,CAAA;;EAEA,IAAA,IAAIP,SAAJ,EAAe;EACb,MAAA,IAAA,CAAK3E,KAAL,EAAA,CAAA;EACD,KAAA;EACF,GAAA;;EAEDkF,EAAAA,WAAW,GAAG;MACZ,OAAO,IAAA,CAAKrF,QAAL,CAAcuD,SAAd,CAAwB+B,QAAxB,CAAiCxH,gBAAjC,CAAP,CAAA;EACD,GAAA;;EAED2D,EAAAA,UAAU,GAAG;MACX,OAAO3B,+BAAc,CAACC,OAAf,CAAuB1B,oBAAvB,EAA6C,IAAA,CAAK2B,QAAlD,CAAP,CAAA;EACD,GAAA;;EAEDqB,EAAAA,SAAS,GAAG;MACV,OAAOvB,+BAAc,CAACuC,IAAf,CAAoBjE,aAApB,EAAmC,IAAA,CAAK4B,QAAxC,CAAP,CAAA;EACD,GAAA;;EAEDY,EAAAA,cAAc,GAAG;MACf,IAAI,IAAA,CAAKpB,SAAT,EAAoB;QAClB+F,aAAa,CAAC,IAAK/F,CAAAA,SAAN,CAAb,CAAA;QACA,IAAKA,CAAAA,SAAL,GAAiB,IAAjB,CAAA;EACD,KAAA;EACF,GAAA;;IAEDoD,iBAAiB,CAACM,SAAD,EAAY;MAC3B,IAAIsC,WAAK,EAAT,EAAa;EACX,MAAA,OAAOtC,SAAS,KAAKhG,cAAd,GAA+BD,UAA/B,GAA4CD,UAAnD,CAAA;EACD,KAAA;;EAED,IAAA,OAAOkG,SAAS,KAAKhG,cAAd,GAA+BF,UAA/B,GAA4CC,UAAnD,CAAA;EACD,GAAA;;IAEDyH,iBAAiB,CAAChD,KAAD,EAAQ;MACvB,IAAI8D,WAAK,EAAT,EAAa;EACX,MAAA,OAAO9D,KAAK,KAAKzE,UAAV,GAAuBC,cAAvB,GAAwCC,eAA/C,CAAA;EACD,KAAA;;EAED,IAAA,OAAOuE,KAAK,KAAKzE,UAAV,GAAuBE,eAAvB,GAAyCD,cAAhD,CAAA;EACD,GAzTkC;;;IA4Tb,OAAfuI,eAAe,CAAClG,MAAD,EAAS;MAC7B,OAAO,IAAA,CAAKmG,IAAL,CAAU,YAAY;QAC3B,MAAMC,IAAI,GAAGxG,QAAQ,CAACyG,mBAAT,CAA6B,IAA7B,EAAmCrG,MAAnC,CAAb,CAAA;;EAEA,MAAA,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;UAC9BoG,IAAI,CAACzE,EAAL,CAAQ3B,MAAR,CAAA,CAAA;EACA,QAAA,OAAA;EACD,OAAA;;EAED,MAAA,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,QAAA,IAAIoG,IAAI,CAACpG,MAAD,CAAJ,KAAiBsG,SAAjB,IAA8BtG,MAAM,CAACuG,UAAP,CAAkB,GAAlB,CAA9B,IAAwDvG,MAAM,KAAK,aAAvE,EAAsF;EACpF,UAAA,MAAM,IAAIwG,SAAJ,CAAe,CAAmBxG,iBAAAA,EAAAA,MAAO,GAAzC,CAAN,CAAA;EACD,SAAA;;UAEDoG,IAAI,CAACpG,MAAD,CAAJ,EAAA,CAAA;EACD,OAAA;EACF,KAfM,CAAP,CAAA;EAgBD,GAAA;;EA7UkC,CAAA;EAgVrC;EACA;EACA;;;AAEAyB,+BAAY,CAACc,EAAb,CAAgBvB,QAAhB,EAA0B5C,oBAA1B,EAAgDa,mBAAhD,EAAqE,UAAUuD,KAAV,EAAiB;EACpF,EAAA,MAAMiB,MAAM,GAAGgD,4BAAsB,CAAC,IAAD,CAArC,CAAA;;EAEA,EAAA,IAAI,CAAChD,MAAD,IAAW,CAACA,MAAM,CAACO,SAAP,CAAiB+B,QAAjB,CAA0B1H,mBAA1B,CAAhB,EAAgE;EAC9D,IAAA,OAAA;EACD,GAAA;;EAEDmE,EAAAA,KAAK,CAACO,cAAN,EAAA,CAAA;EAEA,EAAA,MAAM2D,QAAQ,GAAG9G,QAAQ,CAACyG,mBAAT,CAA6B5C,MAA7B,CAAjB,CAAA;EACA,EAAA,MAAMkD,UAAU,GAAG,IAAA,CAAKlC,YAAL,CAAkB,kBAAlB,CAAnB,CAAA;;EAEA,EAAA,IAAIkC,UAAJ,EAAgB;MACdD,QAAQ,CAAC/E,EAAT,CAAYgF,UAAZ,CAAA,CAAA;;EACAD,IAAAA,QAAQ,CAAClF,iBAAT,EAAA,CAAA;;EACA,IAAA,OAAA;EACD,GAAA;;IAED,IAAIoF,4BAAW,CAACC,gBAAZ,CAA6B,IAA7B,EAAmC,OAAnC,CAAgD,KAAA,MAApD,EAA4D;EAC1DH,IAAAA,QAAQ,CAAC7F,IAAT,EAAA,CAAA;;EACA6F,IAAAA,QAAQ,CAAClF,iBAAT,EAAA,CAAA;;EACA,IAAA,OAAA;EACD,GAAA;;EAEDkF,EAAAA,QAAQ,CAACvF,IAAT,EAAA,CAAA;;EACAuF,EAAAA,QAAQ,CAAClF,iBAAT,EAAA,CAAA;EACD,CA1BD,CAAA,CAAA;AA4BAC,+BAAY,CAACc,EAAb,CAAgBuE,MAAhB,EAAwB3I,mBAAxB,EAA6C,MAAM;EACjD,EAAA,MAAM4I,SAAS,GAAGxG,+BAAc,CAACuC,IAAf,CAAoB5D,kBAApB,CAAlB,CAAA;;EAEA,EAAA,KAAK,MAAMwH,QAAX,IAAuBK,SAAvB,EAAkC;MAChCnH,QAAQ,CAACyG,mBAAT,CAA6BK,QAA7B,CAAA,CAAA;EACD,GAAA;EACF,CAND,CAAA,CAAA;EAQA;EACA;EACA;;AAEAM,0BAAkB,CAACpH,QAAD,CAAlB;;;;;;;;"}