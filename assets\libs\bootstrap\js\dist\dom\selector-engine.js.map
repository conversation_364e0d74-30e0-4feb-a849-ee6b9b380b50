{"version": 3, "file": "selector-engine.js", "sources": ["../../src/dom/selector-engine.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { isDisabled, isVisible } from '../util/index'\n\n/**\n * Constants\n */\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children).filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n    let ancestor = element.parentNode.closest(selector)\n\n    while (ancestor) {\n      parents.push(ancestor)\n      ancestor = ancestor.parentNode.closest(selector)\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n  // TODO: this is now unused; remove later along with prev()\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  },\n\n  focusableChildren(element) {\n    const focusables = [\n      'a',\n      'button',\n      'input',\n      'textarea',\n      'select',\n      'details',\n      '[tabindex]',\n      '[contenteditable=\"true\"]'\n    ].map(selector => `${selector}:not([tabindex^=\"-\"])`).join(',')\n\n    return this.find(focusables, element).filter(el => !isDisabled(el) && isVisible(el))\n  }\n}\n\nexport default SelectorEngine\n"], "names": ["SelectorEngine", "find", "selector", "element", "document", "documentElement", "concat", "Element", "prototype", "querySelectorAll", "call", "findOne", "querySelector", "children", "filter", "child", "matches", "parents", "ancestor", "parentNode", "closest", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "focusableC<PERSON><PERSON>n", "focusables", "map", "join", "el", "isDisabled", "isVisible"], "mappings": ";;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAIA;EACA;EACA;;AAEA,QAAMA,cAAc,GAAG;IACrBC,IAAI,CAACC,QAAD,EAAWC,OAAO,GAAGC,QAAQ,CAACC,eAA9B,EAA+C;EACjD,IAAA,OAAO,GAAGC,MAAH,CAAU,GAAGC,OAAO,CAACC,SAAR,CAAkBC,gBAAlB,CAAmCC,IAAnC,CAAwCP,OAAxC,EAAiDD,QAAjD,CAAb,CAAP,CAAA;KAFmB;;IAKrBS,OAAO,CAACT,QAAD,EAAWC,OAAO,GAAGC,QAAQ,CAACC,eAA9B,EAA+C;MACpD,OAAOE,OAAO,CAACC,SAAR,CAAkBI,aAAlB,CAAgCF,IAAhC,CAAqCP,OAArC,EAA8CD,QAA9C,CAAP,CAAA;KANmB;;EASrBW,EAAAA,QAAQ,CAACV,OAAD,EAAUD,QAAV,EAAoB;EAC1B,IAAA,OAAO,GAAGI,MAAH,CAAU,GAAGH,OAAO,CAACU,QAArB,CAA+BC,CAAAA,MAA/B,CAAsCC,KAAK,IAAIA,KAAK,CAACC,OAAN,CAAcd,QAAd,CAA/C,CAAP,CAAA;KAVmB;;EAarBe,EAAAA,OAAO,CAACd,OAAD,EAAUD,QAAV,EAAoB;MACzB,MAAMe,OAAO,GAAG,EAAhB,CAAA;MACA,IAAIC,QAAQ,GAAGf,OAAO,CAACgB,UAAR,CAAmBC,OAAnB,CAA2BlB,QAA3B,CAAf,CAAA;;EAEA,IAAA,OAAOgB,QAAP,EAAiB;QACfD,OAAO,CAACI,IAAR,CAAaH,QAAb,CAAA,CAAA;QACAA,QAAQ,GAAGA,QAAQ,CAACC,UAAT,CAAoBC,OAApB,CAA4BlB,QAA5B,CAAX,CAAA;EACD,KAAA;;EAED,IAAA,OAAOe,OAAP,CAAA;KAtBmB;;EAyBrBK,EAAAA,IAAI,CAACnB,OAAD,EAAUD,QAAV,EAAoB;EACtB,IAAA,IAAIqB,QAAQ,GAAGpB,OAAO,CAACqB,sBAAvB,CAAA;;EAEA,IAAA,OAAOD,QAAP,EAAiB;EACf,MAAA,IAAIA,QAAQ,CAACP,OAAT,CAAiBd,QAAjB,CAAJ,EAAgC;UAC9B,OAAO,CAACqB,QAAD,CAAP,CAAA;EACD,OAAA;;QAEDA,QAAQ,GAAGA,QAAQ,CAACC,sBAApB,CAAA;EACD,KAAA;;EAED,IAAA,OAAO,EAAP,CAAA;KApCmB;;EAsCrB;EACAC,EAAAA,IAAI,CAACtB,OAAD,EAAUD,QAAV,EAAoB;EACtB,IAAA,IAAIuB,IAAI,GAAGtB,OAAO,CAACuB,kBAAnB,CAAA;;EAEA,IAAA,OAAOD,IAAP,EAAa;EACX,MAAA,IAAIA,IAAI,CAACT,OAAL,CAAad,QAAb,CAAJ,EAA4B;UAC1B,OAAO,CAACuB,IAAD,CAAP,CAAA;EACD,OAAA;;QAEDA,IAAI,GAAGA,IAAI,CAACC,kBAAZ,CAAA;EACD,KAAA;;EAED,IAAA,OAAO,EAAP,CAAA;KAlDmB;;IAqDrBC,iBAAiB,CAACxB,OAAD,EAAU;EACzB,IAAA,MAAMyB,UAAU,GAAG,CACjB,GADiB,EAEjB,QAFiB,EAGjB,OAHiB,EAIjB,UAJiB,EAKjB,QALiB,EAMjB,SANiB,EAOjB,YAPiB,EAQjB,0BARiB,CAAA,CASjBC,GATiB,CASb3B,QAAQ,IAAK,CAAEA,EAAAA,QAAS,CATX,qBAAA,CAAA,CAAA,CASmC4B,IATnC,CASwC,GATxC,CAAnB,CAAA;MAWA,OAAO,IAAA,CAAK7B,IAAL,CAAU2B,UAAV,EAAsBzB,OAAtB,CAAA,CAA+BW,MAA/B,CAAsCiB,EAAE,IAAI,CAACC,gBAAU,CAACD,EAAD,CAAX,IAAmBE,eAAS,CAACF,EAAD,CAAxE,CAAP,CAAA;EACD,GAAA;;EAlEoB;;;;;;;;"}