{"version": 3, "file": "event-handler.js", "sources": ["../../src/dom/event-handler.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * Constants\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\n\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * Private methods\n */\n\nfunction makeEventUid(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getElementEvents(element) {\n  const uid = makeEventUid(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    hydrateObj(event, { delegateTarget: element })\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (const domElement of domElements) {\n        if (domElement !== target) {\n          continue\n        }\n\n        hydrateObj(event, { delegateTarget: target })\n\n        if (handler.oneOff) {\n          EventHandler.off(element, event.type, selector, fn)\n        }\n\n        return fn.apply(target, [event])\n      }\n    }\n  }\n}\n\nfunction findHandler(events, callable, delegationSelector = null) {\n  return Object.values(events)\n    .find(event => event.callable === callable && event.delegationSelector === delegationSelector)\n}\n\nfunction normalizeParameters(originalTypeEvent, handler, delegationFunction) {\n  const isDelegated = typeof handler === 'string'\n  // todo: tooltip passes `false` instead of selector, so we need to check\n  const callable = isDelegated ? delegationFunction : (handler || delegationFunction)\n  let typeEvent = getTypeEvent(originalTypeEvent)\n\n  if (!nativeEvents.has(typeEvent)) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [isDelegated, callable, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFunction, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  let [isDelegated, callable, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n\n  // in case of mouseenter or mouseleave wrap the handler within a function that checks for its DOM position\n  // this prevents the handler from being dispatched the same way as mouseover or mouseout does\n  if (originalTypeEvent in customEvents) {\n    const wrapFunction = fn => {\n      return function (event) {\n        if (!event.relatedTarget || (event.relatedTarget !== event.delegateTarget && !event.delegateTarget.contains(event.relatedTarget))) {\n          return fn.call(this, event)\n        }\n      }\n    }\n\n    callable = wrapFunction(callable)\n  }\n\n  const events = getElementEvents(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFunction = findHandler(handlers, callable, isDelegated ? handler : null)\n\n  if (previousFunction) {\n    previousFunction.oneOff = previousFunction.oneOff && oneOff\n\n    return\n  }\n\n  const uid = makeEventUid(callable, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = isDelegated ?\n    bootstrapDelegationHandler(element, handler, callable) :\n    bootstrapHandler(element, callable)\n\n  fn.delegationSelector = isDelegated ? handler : null\n  fn.callable = callable\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, isDelegated)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  for (const handlerKey of Object.keys(storeElementEvent)) {\n    if (handlerKey.includes(namespace)) {\n      const event = storeElementEvent[handlerKey]\n      removeHandler(element, events, typeEvent, event.callable, event.delegationSelector)\n    }\n  }\n}\n\nfunction getTypeEvent(event) {\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  event = event.replace(stripNameRegex, '')\n  return customEvents[event] || event\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, false)\n  },\n\n  one(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFunction) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [isDelegated, callable, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getElementEvents(element)\n    const storeElementEvent = events[typeEvent] || {}\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof callable !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!Object.keys(storeElementEvent).length) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, callable, isDelegated ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      for (const elementEvent of Object.keys(events)) {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      }\n    }\n\n    for (const keyHandlers of Object.keys(storeElementEvent)) {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        const event = storeElementEvent[keyHandlers]\n        removeHandler(element, events, typeEvent, event.callable, event.delegationSelector)\n      }\n    }\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = getTypeEvent(event)\n    const inNamespace = event !== typeEvent\n\n    let jQueryEvent = null\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    let evt = new Event(event, { bubbles, cancelable: true })\n    evt = hydrateObj(evt, args)\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && jQueryEvent) {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nfunction hydrateObj(obj, meta) {\n  for (const [key, value] of Object.entries(meta || {})) {\n    try {\n      obj[key] = value\n    } catch {\n      Object.defineProperty(obj, key, {\n        configurable: true,\n        get() {\n          return value\n        }\n      })\n    }\n  }\n\n  return obj\n}\n\nexport default EventHandler\n"], "names": ["namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "Set", "makeEventUid", "element", "uid", "getElementEvents", "bootstrapHandler", "fn", "handler", "event", "hydrateObj", "<PERSON><PERSON><PERSON><PERSON>", "oneOff", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "selector", "dom<PERSON><PERSON>s", "querySelectorAll", "target", "parentNode", "dom<PERSON>lement", "<PERSON><PERSON><PERSON><PERSON>", "events", "callable", "delegationSelector", "Object", "values", "find", "normalizeParameters", "originalTypeEvent", "delegationFunction", "isDelegated", "typeEvent", "getTypeEvent", "has", "add<PERSON><PERSON><PERSON>", "wrapFunction", "relatedTarget", "contains", "call", "handlers", "previousFunction", "replace", "addEventListener", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "Boolean", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "keys", "includes", "on", "one", "inNamespace", "isNamespace", "startsWith", "length", "elementEvent", "slice", "keyHandlers", "trigger", "args", "$", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "Event", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "evt", "cancelable", "preventDefault", "dispatchEvent", "obj", "meta", "key", "value", "entries", "defineProperty", "configurable", "get"], "mappings": ";;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAIA;EACA;EACA;;EAEA,MAAMA,cAAc,GAAG,oBAAvB,CAAA;EACA,MAAMC,cAAc,GAAG,MAAvB,CAAA;EACA,MAAMC,aAAa,GAAG,QAAtB,CAAA;EACA,MAAMC,aAAa,GAAG,EAAtB;;EACA,IAAIC,QAAQ,GAAG,CAAf,CAAA;EACA,MAAMC,YAAY,GAAG;EACnBC,EAAAA,UAAU,EAAE,WADO;EAEnBC,EAAAA,UAAU,EAAE,UAAA;EAFO,CAArB,CAAA;EAKA,MAAMC,YAAY,GAAG,IAAIC,GAAJ,CAAQ,CAC3B,OAD2B,EAE3B,UAF2B,EAG3B,SAH2B,EAI3B,WAJ2B,EAK3B,aAL2B,EAM3B,YAN2B,EAO3B,gBAP2B,EAQ3B,WAR2B,EAS3B,UAT2B,EAU3B,WAV2B,EAW3B,aAX2B,EAY3B,WAZ2B,EAa3B,SAb2B,EAc3B,UAd2B,EAe3B,OAf2B,EAgB3B,mBAhB2B,EAiB3B,YAjB2B,EAkB3B,WAlB2B,EAmB3B,UAnB2B,EAoB3B,aApB2B,EAqB3B,aArB2B,EAsB3B,aAtB2B,EAuB3B,WAvB2B,EAwB3B,cAxB2B,EAyB3B,eAzB2B,EA0B3B,cA1B2B,EA2B3B,eA3B2B,EA4B3B,YA5B2B,EA6B3B,OA7B2B,EA8B3B,MA9B2B,EA+B3B,QA/B2B,EAgC3B,OAhC2B,EAiC3B,QAjC2B,EAkC3B,QAlC2B,EAmC3B,SAnC2B,EAoC3B,UApC2B,EAqC3B,MArC2B,EAsC3B,QAtC2B,EAuC3B,cAvC2B,EAwC3B,QAxC2B,EAyC3B,MAzC2B,EA0C3B,kBA1C2B,EA2C3B,kBA3C2B,EA4C3B,OA5C2B,EA6C3B,OA7C2B,EA8C3B,QA9C2B,CAAR,CAArB,CAAA;EAiDA;EACA;EACA;;EAEA,SAASC,YAAT,CAAsBC,OAAtB,EAA+BC,GAA/B,EAAoC;EAClC,EAAA,OAAQA,GAAG,IAAK,CAAEA,EAAAA,GAAI,KAAIR,QAAQ,EAAG,CAA9B,CAAA,IAAoCO,OAAO,CAACP,QAA5C,IAAwDA,QAAQ,EAAvE,CAAA;EACD,CAAA;;EAED,SAASS,gBAAT,CAA0BF,OAA1B,EAAmC;EACjC,EAAA,MAAMC,GAAG,GAAGF,YAAY,CAACC,OAAD,CAAxB,CAAA;IAEAA,OAAO,CAACP,QAAR,GAAmBQ,GAAnB,CAAA;IACAT,aAAa,CAACS,GAAD,CAAb,GAAqBT,aAAa,CAACS,GAAD,CAAb,IAAsB,EAA3C,CAAA;IAEA,OAAOT,aAAa,CAACS,GAAD,CAApB,CAAA;EACD,CAAA;;EAED,SAASE,gBAAT,CAA0BH,OAA1B,EAAmCI,EAAnC,EAAuC;EACrC,EAAA,OAAO,SAASC,OAAT,CAAiBC,KAAjB,EAAwB;MAC7BC,UAAU,CAACD,KAAD,EAAQ;EAAEE,MAAAA,cAAc,EAAER,OAAAA;EAAlB,KAAR,CAAV,CAAA;;MAEA,IAAIK,OAAO,CAACI,MAAZ,EAAoB;QAClBC,YAAY,CAACC,GAAb,CAAiBX,OAAjB,EAA0BM,KAAK,CAACM,IAAhC,EAAsCR,EAAtC,CAAA,CAAA;EACD,KAAA;;MAED,OAAOA,EAAE,CAACS,KAAH,CAASb,OAAT,EAAkB,CAACM,KAAD,CAAlB,CAAP,CAAA;KAPF,CAAA;EASD,CAAA;;EAED,SAASQ,0BAAT,CAAoCd,OAApC,EAA6Ce,QAA7C,EAAuDX,EAAvD,EAA2D;EACzD,EAAA,OAAO,SAASC,OAAT,CAAiBC,KAAjB,EAAwB;EAC7B,IAAA,MAAMU,WAAW,GAAGhB,OAAO,CAACiB,gBAAR,CAAyBF,QAAzB,CAApB,CAAA;;EAEA,IAAA,KAAK,IAAI;EAAEG,MAAAA,MAAAA;EAAF,KAAA,GAAaZ,KAAtB,EAA6BY,MAAM,IAAIA,MAAM,KAAK,IAAlD,EAAwDA,MAAM,GAAGA,MAAM,CAACC,UAAxE,EAAoF;EAClF,MAAA,KAAK,MAAMC,UAAX,IAAyBJ,WAAzB,EAAsC;UACpC,IAAII,UAAU,KAAKF,MAAnB,EAA2B;EACzB,UAAA,SAAA;EACD,SAAA;;UAEDX,UAAU,CAACD,KAAD,EAAQ;EAAEE,UAAAA,cAAc,EAAEU,MAAAA;EAAlB,SAAR,CAAV,CAAA;;UAEA,IAAIb,OAAO,CAACI,MAAZ,EAAoB;YAClBC,YAAY,CAACC,GAAb,CAAiBX,OAAjB,EAA0BM,KAAK,CAACM,IAAhC,EAAsCG,QAAtC,EAAgDX,EAAhD,CAAA,CAAA;EACD,SAAA;;UAED,OAAOA,EAAE,CAACS,KAAH,CAASK,MAAT,EAAiB,CAACZ,KAAD,CAAjB,CAAP,CAAA;EACD,OAAA;EACF,KAAA;KAjBH,CAAA;EAmBD,CAAA;;EAED,SAASe,WAAT,CAAqBC,MAArB,EAA6BC,QAA7B,EAAuCC,kBAAkB,GAAG,IAA5D,EAAkE;IAChE,OAAOC,MAAM,CAACC,MAAP,CAAcJ,MAAd,CACJK,CAAAA,IADI,CACCrB,KAAK,IAAIA,KAAK,CAACiB,QAAN,KAAmBA,QAAnB,IAA+BjB,KAAK,CAACkB,kBAAN,KAA6BA,kBADtE,CAAP,CAAA;EAED,CAAA;;EAED,SAASI,mBAAT,CAA6BC,iBAA7B,EAAgDxB,OAAhD,EAAyDyB,kBAAzD,EAA6E;EAC3E,EAAA,MAAMC,WAAW,GAAG,OAAO1B,OAAP,KAAmB,QAAvC,CAD2E;;IAG3E,MAAMkB,QAAQ,GAAGQ,WAAW,GAAGD,kBAAH,GAAyBzB,OAAO,IAAIyB,kBAAhE,CAAA;EACA,EAAA,IAAIE,SAAS,GAAGC,YAAY,CAACJ,iBAAD,CAA5B,CAAA;;EAEA,EAAA,IAAI,CAAChC,YAAY,CAACqC,GAAb,CAAiBF,SAAjB,CAAL,EAAkC;EAChCA,IAAAA,SAAS,GAAGH,iBAAZ,CAAA;EACD,GAAA;;EAED,EAAA,OAAO,CAACE,WAAD,EAAcR,QAAd,EAAwBS,SAAxB,CAAP,CAAA;EACD,CAAA;;EAED,SAASG,UAAT,CAAoBnC,OAApB,EAA6B6B,iBAA7B,EAAgDxB,OAAhD,EAAyDyB,kBAAzD,EAA6ErB,MAA7E,EAAqF;EACnF,EAAA,IAAI,OAAOoB,iBAAP,KAA6B,QAA7B,IAAyC,CAAC7B,OAA9C,EAAuD;EACrD,IAAA,OAAA;EACD,GAAA;;EAED,EAAA,IAAI,CAAC+B,WAAD,EAAcR,QAAd,EAAwBS,SAAxB,CAAqCJ,GAAAA,mBAAmB,CAACC,iBAAD,EAAoBxB,OAApB,EAA6ByB,kBAA7B,CAA5D,CALmF;EAQnF;;IACA,IAAID,iBAAiB,IAAInC,YAAzB,EAAuC;MACrC,MAAM0C,YAAY,GAAGhC,EAAE,IAAI;QACzB,OAAO,UAAUE,KAAV,EAAiB;UACtB,IAAI,CAACA,KAAK,CAAC+B,aAAP,IAAyB/B,KAAK,CAAC+B,aAAN,KAAwB/B,KAAK,CAACE,cAA9B,IAAgD,CAACF,KAAK,CAACE,cAAN,CAAqB8B,QAArB,CAA8BhC,KAAK,CAAC+B,aAApC,CAA9E,EAAmI;EACjI,UAAA,OAAOjC,EAAE,CAACmC,IAAH,CAAQ,IAAR,EAAcjC,KAAd,CAAP,CAAA;EACD,SAAA;SAHH,CAAA;OADF,CAAA;;EAQAiB,IAAAA,QAAQ,GAAGa,YAAY,CAACb,QAAD,CAAvB,CAAA;EACD,GAAA;;EAED,EAAA,MAAMD,MAAM,GAAGpB,gBAAgB,CAACF,OAAD,CAA/B,CAAA;EACA,EAAA,MAAMwC,QAAQ,GAAGlB,MAAM,CAACU,SAAD,CAAN,KAAsBV,MAAM,CAACU,SAAD,CAAN,GAAoB,EAA1C,CAAjB,CAAA;EACA,EAAA,MAAMS,gBAAgB,GAAGpB,WAAW,CAACmB,QAAD,EAAWjB,QAAX,EAAqBQ,WAAW,GAAG1B,OAAH,GAAa,IAA7C,CAApC,CAAA;;EAEA,EAAA,IAAIoC,gBAAJ,EAAsB;EACpBA,IAAAA,gBAAgB,CAAChC,MAAjB,GAA0BgC,gBAAgB,CAAChC,MAAjB,IAA2BA,MAArD,CAAA;EAEA,IAAA,OAAA;EACD,GAAA;;EAED,EAAA,MAAMR,GAAG,GAAGF,YAAY,CAACwB,QAAD,EAAWM,iBAAiB,CAACa,OAAlB,CAA0BrD,cAA1B,EAA0C,EAA1C,CAAX,CAAxB,CAAA;EACA,EAAA,MAAMe,EAAE,GAAG2B,WAAW,GACpBjB,0BAA0B,CAACd,OAAD,EAAUK,OAAV,EAAmBkB,QAAnB,CADN,GAEpBpB,gBAAgB,CAACH,OAAD,EAAUuB,QAAV,CAFlB,CAAA;EAIAnB,EAAAA,EAAE,CAACoB,kBAAH,GAAwBO,WAAW,GAAG1B,OAAH,GAAa,IAAhD,CAAA;IACAD,EAAE,CAACmB,QAAH,GAAcA,QAAd,CAAA;IACAnB,EAAE,CAACK,MAAH,GAAYA,MAAZ,CAAA;IACAL,EAAE,CAACX,QAAH,GAAcQ,GAAd,CAAA;EACAuC,EAAAA,QAAQ,CAACvC,GAAD,CAAR,GAAgBG,EAAhB,CAAA;EAEAJ,EAAAA,OAAO,CAAC2C,gBAAR,CAAyBX,SAAzB,EAAoC5B,EAApC,EAAwC2B,WAAxC,CAAA,CAAA;EACD,CAAA;;EAED,SAASa,aAAT,CAAuB5C,OAAvB,EAAgCsB,MAAhC,EAAwCU,SAAxC,EAAmD3B,OAAnD,EAA4DmB,kBAA5D,EAAgF;EAC9E,EAAA,MAAMpB,EAAE,GAAGiB,WAAW,CAACC,MAAM,CAACU,SAAD,CAAP,EAAoB3B,OAApB,EAA6BmB,kBAA7B,CAAtB,CAAA;;IAEA,IAAI,CAACpB,EAAL,EAAS;EACP,IAAA,OAAA;EACD,GAAA;;IAEDJ,OAAO,CAAC6C,mBAAR,CAA4Bb,SAA5B,EAAuC5B,EAAvC,EAA2C0C,OAAO,CAACtB,kBAAD,CAAlD,CAAA,CAAA;IACA,OAAOF,MAAM,CAACU,SAAD,CAAN,CAAkB5B,EAAE,CAACX,QAArB,CAAP,CAAA;EACD,CAAA;;EAED,SAASsD,wBAAT,CAAkC/C,OAAlC,EAA2CsB,MAA3C,EAAmDU,SAAnD,EAA8DgB,SAA9D,EAAyE;EACvE,EAAA,MAAMC,iBAAiB,GAAG3B,MAAM,CAACU,SAAD,CAAN,IAAqB,EAA/C,CAAA;;IAEA,KAAK,MAAMkB,UAAX,IAAyBzB,MAAM,CAAC0B,IAAP,CAAYF,iBAAZ,CAAzB,EAAyD;EACvD,IAAA,IAAIC,UAAU,CAACE,QAAX,CAAoBJ,SAApB,CAAJ,EAAoC;EAClC,MAAA,MAAM1C,KAAK,GAAG2C,iBAAiB,CAACC,UAAD,CAA/B,CAAA;EACAN,MAAAA,aAAa,CAAC5C,OAAD,EAAUsB,MAAV,EAAkBU,SAAlB,EAA6B1B,KAAK,CAACiB,QAAnC,EAA6CjB,KAAK,CAACkB,kBAAnD,CAAb,CAAA;EACD,KAAA;EACF,GAAA;EACF,CAAA;;EAED,SAASS,YAAT,CAAsB3B,KAAtB,EAA6B;EAC3B;IACAA,KAAK,GAAGA,KAAK,CAACoC,OAAN,CAAcpD,cAAd,EAA8B,EAA9B,CAAR,CAAA;EACA,EAAA,OAAOI,YAAY,CAACY,KAAD,CAAZ,IAAuBA,KAA9B,CAAA;EACD,CAAA;;AAED,QAAMI,YAAY,GAAG;IACnB2C,EAAE,CAACrD,OAAD,EAAUM,KAAV,EAAiBD,OAAjB,EAA0ByB,kBAA1B,EAA8C;MAC9CK,UAAU,CAACnC,OAAD,EAAUM,KAAV,EAAiBD,OAAjB,EAA0ByB,kBAA1B,EAA8C,KAA9C,CAAV,CAAA;KAFiB;;IAKnBwB,GAAG,CAACtD,OAAD,EAAUM,KAAV,EAAiBD,OAAjB,EAA0ByB,kBAA1B,EAA8C;MAC/CK,UAAU,CAACnC,OAAD,EAAUM,KAAV,EAAiBD,OAAjB,EAA0ByB,kBAA1B,EAA8C,IAA9C,CAAV,CAAA;KANiB;;IASnBnB,GAAG,CAACX,OAAD,EAAU6B,iBAAV,EAA6BxB,OAA7B,EAAsCyB,kBAAtC,EAA0D;EAC3D,IAAA,IAAI,OAAOD,iBAAP,KAA6B,QAA7B,IAAyC,CAAC7B,OAA9C,EAAuD;EACrD,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,MAAM,CAAC+B,WAAD,EAAcR,QAAd,EAAwBS,SAAxB,CAAA,GAAqCJ,mBAAmB,CAACC,iBAAD,EAAoBxB,OAApB,EAA6ByB,kBAA7B,CAA9D,CAAA;EACA,IAAA,MAAMyB,WAAW,GAAGvB,SAAS,KAAKH,iBAAlC,CAAA;EACA,IAAA,MAAMP,MAAM,GAAGpB,gBAAgB,CAACF,OAAD,CAA/B,CAAA;EACA,IAAA,MAAMiD,iBAAiB,GAAG3B,MAAM,CAACU,SAAD,CAAN,IAAqB,EAA/C,CAAA;EACA,IAAA,MAAMwB,WAAW,GAAG3B,iBAAiB,CAAC4B,UAAlB,CAA6B,GAA7B,CAApB,CAAA;;EAEA,IAAA,IAAI,OAAOlC,QAAP,KAAoB,WAAxB,EAAqC;EACnC;QACA,IAAI,CAACE,MAAM,CAAC0B,IAAP,CAAYF,iBAAZ,CAAA,CAA+BS,MAApC,EAA4C;EAC1C,QAAA,OAAA;EACD,OAAA;;EAEDd,MAAAA,aAAa,CAAC5C,OAAD,EAAUsB,MAAV,EAAkBU,SAAlB,EAA6BT,QAA7B,EAAuCQ,WAAW,GAAG1B,OAAH,GAAa,IAA/D,CAAb,CAAA;EACA,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAImD,WAAJ,EAAiB;QACf,KAAK,MAAMG,YAAX,IAA2BlC,MAAM,CAAC0B,IAAP,CAAY7B,MAAZ,CAA3B,EAAgD;EAC9CyB,QAAAA,wBAAwB,CAAC/C,OAAD,EAAUsB,MAAV,EAAkBqC,YAAlB,EAAgC9B,iBAAiB,CAAC+B,KAAlB,CAAwB,CAAxB,CAAhC,CAAxB,CAAA;EACD,OAAA;EACF,KAAA;;MAED,KAAK,MAAMC,WAAX,IAA0BpC,MAAM,CAAC0B,IAAP,CAAYF,iBAAZ,CAA1B,EAA0D;QACxD,MAAMC,UAAU,GAAGW,WAAW,CAACnB,OAAZ,CAAoBnD,aAApB,EAAmC,EAAnC,CAAnB,CAAA;;QAEA,IAAI,CAACgE,WAAD,IAAgB1B,iBAAiB,CAACuB,QAAlB,CAA2BF,UAA3B,CAApB,EAA4D;EAC1D,QAAA,MAAM5C,KAAK,GAAG2C,iBAAiB,CAACY,WAAD,CAA/B,CAAA;EACAjB,QAAAA,aAAa,CAAC5C,OAAD,EAAUsB,MAAV,EAAkBU,SAAlB,EAA6B1B,KAAK,CAACiB,QAAnC,EAA6CjB,KAAK,CAACkB,kBAAnD,CAAb,CAAA;EACD,OAAA;EACF,KAAA;KA3CgB;;EA8CnBsC,EAAAA,OAAO,CAAC9D,OAAD,EAAUM,KAAV,EAAiByD,IAAjB,EAAuB;EAC5B,IAAA,IAAI,OAAOzD,KAAP,KAAiB,QAAjB,IAA6B,CAACN,OAAlC,EAA2C;EACzC,MAAA,OAAO,IAAP,CAAA;EACD,KAAA;;MAED,MAAMgE,CAAC,GAAGC,eAAS,EAAnB,CAAA;EACA,IAAA,MAAMjC,SAAS,GAAGC,YAAY,CAAC3B,KAAD,CAA9B,CAAA;EACA,IAAA,MAAMiD,WAAW,GAAGjD,KAAK,KAAK0B,SAA9B,CAAA;MAEA,IAAIkC,WAAW,GAAG,IAAlB,CAAA;MACA,IAAIC,OAAO,GAAG,IAAd,CAAA;MACA,IAAIC,cAAc,GAAG,IAArB,CAAA;MACA,IAAIC,gBAAgB,GAAG,KAAvB,CAAA;;MAEA,IAAId,WAAW,IAAIS,CAAnB,EAAsB;QACpBE,WAAW,GAAGF,CAAC,CAACM,KAAF,CAAQhE,KAAR,EAAeyD,IAAf,CAAd,CAAA;EAEAC,MAAAA,CAAC,CAAChE,OAAD,CAAD,CAAW8D,OAAX,CAAmBI,WAAnB,CAAA,CAAA;EACAC,MAAAA,OAAO,GAAG,CAACD,WAAW,CAACK,oBAAZ,EAAX,CAAA;EACAH,MAAAA,cAAc,GAAG,CAACF,WAAW,CAACM,6BAAZ,EAAlB,CAAA;EACAH,MAAAA,gBAAgB,GAAGH,WAAW,CAACO,kBAAZ,EAAnB,CAAA;EACD,KAAA;;EAED,IAAA,IAAIC,GAAG,GAAG,IAAIJ,KAAJ,CAAUhE,KAAV,EAAiB;QAAE6D,OAAF;EAAWQ,MAAAA,UAAU,EAAE,IAAA;EAAvB,KAAjB,CAAV,CAAA;EACAD,IAAAA,GAAG,GAAGnE,UAAU,CAACmE,GAAD,EAAMX,IAAN,CAAhB,CAAA;;EAEA,IAAA,IAAIM,gBAAJ,EAAsB;EACpBK,MAAAA,GAAG,CAACE,cAAJ,EAAA,CAAA;EACD,KAAA;;EAED,IAAA,IAAIR,cAAJ,EAAoB;QAClBpE,OAAO,CAAC6E,aAAR,CAAsBH,GAAtB,CAAA,CAAA;EACD,KAAA;;EAED,IAAA,IAAIA,GAAG,CAACL,gBAAJ,IAAwBH,WAA5B,EAAyC;EACvCA,MAAAA,WAAW,CAACU,cAAZ,EAAA,CAAA;EACD,KAAA;;EAED,IAAA,OAAOF,GAAP,CAAA;EACD,GAAA;;EArFkB,EAArB;;EAwFA,SAASnE,UAAT,CAAoBuE,GAApB,EAAyBC,IAAzB,EAA+B;EAC7B,EAAA,KAAK,MAAM,CAACC,GAAD,EAAMC,KAAN,CAAX,IAA2BxD,MAAM,CAACyD,OAAP,CAAeH,IAAI,IAAI,EAAvB,CAA3B,EAAuD;MACrD,IAAI;EACFD,MAAAA,GAAG,CAACE,GAAD,CAAH,GAAWC,KAAX,CAAA;EACD,KAFD,CAEE,OAAM,OAAA,EAAA;EACNxD,MAAAA,MAAM,CAAC0D,cAAP,CAAsBL,GAAtB,EAA2BE,GAA3B,EAAgC;EAC9BI,QAAAA,YAAY,EAAE,IADgB;;EAE9BC,QAAAA,GAAG,GAAG;EACJ,UAAA,OAAOJ,KAAP,CAAA;EACD,SAAA;;SAJH,CAAA,CAAA;EAMD,KAAA;EACF,GAAA;;EAED,EAAA,OAAOH,GAAP,CAAA;EACD;;;;;;;;"}