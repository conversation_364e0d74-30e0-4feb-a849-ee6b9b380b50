{"version": 3, "file": "sanitizer.js", "sources": ["../../src/util/sanitizer.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttributes = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shout-out to Angular https://github.com/angular/angular/blob/12.2.x/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file|sms):|[^#&/:?]*(?:[#/?]|$))/i\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shout-out to Angular https://github.com/angular/angular/blob/12.2.x/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attribute, allowedAttributeList) => {\n  const attributeName = attribute.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attributeName)) {\n    if (uriAttributes.has(attributeName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attribute.nodeValue) || DATA_URL_PATTERN.test(attribute.nodeValue))\n    }\n\n    return true\n  }\n\n  // Check if a regular expression validates the attribute.\n  return allowedAttributeList.filter(attributeRegex => attributeRegex instanceof RegExp)\n    .some(regex => regex.test(attributeName))\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFunction) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFunction && typeof sanitizeFunction === 'function') {\n    return sanitizeFunction(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (const element of elements) {\n    const elementName = element.nodeName.toLowerCase()\n\n    if (!Object.keys(allowList).includes(elementName)) {\n      element.remove()\n\n      continue\n    }\n\n    const attributeList = [].concat(...element.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elementName] || [])\n\n    for (const attribute of attributeList) {\n      if (!allowedAttribute(attribute, allowedAttributes)) {\n        element.removeAttribute(attribute.nodeName)\n      }\n    }\n  }\n\n  return createdDocument.body.innerHTML\n}\n"], "names": ["uriAttributes", "Set", "ARIA_ATTRIBUTE_PATTERN", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attribute", "allowedAttributeList", "attributeName", "nodeName", "toLowerCase", "includes", "has", "Boolean", "test", "nodeValue", "filter", "attributeRegex", "RegExp", "some", "regex", "DefaultAllowlist", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "i", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFunction", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "window", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createdDocument", "parseFromString", "elements", "concat", "body", "querySelectorAll", "element", "elementName", "Object", "keys", "remove", "attributeList", "attributes", "allowedAttributes", "removeAttribute", "innerHTML"], "mappings": ";;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAEA,MAAMA,aAAa,GAAG,IAAIC,GAAJ,CAAQ,CAC5B,YAD4B,EAE5B,MAF4B,EAG5B,MAH4B,EAI5B,UAJ4B,EAK5B,UAL4B,EAM5B,QAN4B,EAO5B,KAP4B,EAQ5B,YAR4B,CAAR,CAAtB,CAAA;EAWA,MAAMC,sBAAsB,GAAG,gBAA/B,CAAA;EAEA;EACA;EACA;EACA;EACA;;EACA,MAAMC,gBAAgB,GAAG,gEAAzB,CAAA;EAEA;EACA;EACA;EACA;EACA;;EACA,MAAMC,gBAAgB,GAAG,oIAAzB,CAAA;;EAEA,MAAMC,gBAAgB,GAAG,CAACC,SAAD,EAAYC,oBAAZ,KAAqC;EAC5D,EAAA,MAAMC,aAAa,GAAGF,SAAS,CAACG,QAAV,CAAmBC,WAAnB,EAAtB,CAAA;;EAEA,EAAA,IAAIH,oBAAoB,CAACI,QAArB,CAA8BH,aAA9B,CAAJ,EAAkD;EAChD,IAAA,IAAIR,aAAa,CAACY,GAAd,CAAkBJ,aAAlB,CAAJ,EAAsC;EACpC,MAAA,OAAOK,OAAO,CAACV,gBAAgB,CAACW,IAAjB,CAAsBR,SAAS,CAACS,SAAhC,CAA8CX,IAAAA,gBAAgB,CAACU,IAAjB,CAAsBR,SAAS,CAACS,SAAhC,CAA/C,CAAd,CAAA;EACD,KAAA;;EAED,IAAA,OAAO,IAAP,CAAA;EACD,GAT2D;;;IAY5D,OAAOR,oBAAoB,CAACS,MAArB,CAA4BC,cAAc,IAAIA,cAAc,YAAYC,MAAxE,CAAA,CACJC,IADI,CACCC,KAAK,IAAIA,KAAK,CAACN,IAAN,CAAWN,aAAX,CADV,CAAP,CAAA;EAED,CAdD,CAAA;;AAgBO,QAAMa,gBAAgB,GAAG;EAC9B;EACA,EAAA,GAAA,EAAK,CAAC,OAAD,EAAU,KAAV,EAAiB,IAAjB,EAAuB,MAAvB,EAA+B,MAA/B,EAAuCnB,sBAAvC,CAFyB;IAG9BoB,CAAC,EAAE,CAAC,QAAD,EAAW,MAAX,EAAmB,OAAnB,EAA4B,KAA5B,CAH2B;EAI9BC,EAAAA,IAAI,EAAE,EAJwB;EAK9BC,EAAAA,CAAC,EAAE,EAL2B;EAM9BC,EAAAA,EAAE,EAAE,EAN0B;EAO9BC,EAAAA,GAAG,EAAE,EAPyB;EAQ9BC,EAAAA,IAAI,EAAE,EARwB;EAS9BC,EAAAA,GAAG,EAAE,EATyB;EAU9BC,EAAAA,EAAE,EAAE,EAV0B;EAW9BC,EAAAA,EAAE,EAAE,EAX0B;EAY9BC,EAAAA,EAAE,EAAE,EAZ0B;EAa9BC,EAAAA,EAAE,EAAE,EAb0B;EAc9BC,EAAAA,EAAE,EAAE,EAd0B;EAe9BC,EAAAA,EAAE,EAAE,EAf0B;EAgB9BC,EAAAA,EAAE,EAAE,EAhB0B;EAiB9BC,EAAAA,EAAE,EAAE,EAjB0B;EAkB9BC,EAAAA,CAAC,EAAE,EAlB2B;EAmB9BC,EAAAA,GAAG,EAAE,CAAC,KAAD,EAAQ,QAAR,EAAkB,KAAlB,EAAyB,OAAzB,EAAkC,OAAlC,EAA2C,QAA3C,CAnByB;EAoB9BC,EAAAA,EAAE,EAAE,EApB0B;EAqB9BC,EAAAA,EAAE,EAAE,EArB0B;EAsB9BC,EAAAA,CAAC,EAAE,EAtB2B;EAuB9BC,EAAAA,GAAG,EAAE,EAvByB;EAwB9BC,EAAAA,CAAC,EAAE,EAxB2B;EAyB9BC,EAAAA,KAAK,EAAE,EAzBuB;EA0B9BC,EAAAA,IAAI,EAAE,EA1BwB;EA2B9BC,EAAAA,GAAG,EAAE,EA3ByB;EA4B9BC,EAAAA,GAAG,EAAE,EA5ByB;EA6B9BC,EAAAA,MAAM,EAAE,EA7BsB;EA8B9BC,EAAAA,CAAC,EAAE,EA9B2B;EA+B9BC,EAAAA,EAAE,EAAE,EAAA;EA/B0B,EAAzB;EAkCA,SAASC,YAAT,CAAsBC,UAAtB,EAAkCC,SAAlC,EAA6CC,gBAA7C,EAA+D;EACpE,EAAA,IAAI,CAACF,UAAU,CAACG,MAAhB,EAAwB;EACtB,IAAA,OAAOH,UAAP,CAAA;EACD,GAAA;;EAED,EAAA,IAAIE,gBAAgB,IAAI,OAAOA,gBAAP,KAA4B,UAApD,EAAgE;MAC9D,OAAOA,gBAAgB,CAACF,UAAD,CAAvB,CAAA;EACD,GAAA;;EAED,EAAA,MAAMI,SAAS,GAAG,IAAIC,MAAM,CAACC,SAAX,EAAlB,CAAA;IACA,MAAMC,eAAe,GAAGH,SAAS,CAACI,eAAV,CAA0BR,UAA1B,EAAsC,WAAtC,CAAxB,CAAA;EACA,EAAA,MAAMS,QAAQ,GAAG,EAAGC,CAAAA,MAAH,CAAU,GAAGH,eAAe,CAACI,IAAhB,CAAqBC,gBAArB,CAAsC,GAAtC,CAAb,CAAjB,CAAA;;EAEA,EAAA,KAAK,MAAMC,OAAX,IAAsBJ,QAAtB,EAAgC;EAC9B,IAAA,MAAMK,WAAW,GAAGD,OAAO,CAACxD,QAAR,CAAiBC,WAAjB,EAApB,CAAA;;MAEA,IAAI,CAACyD,MAAM,CAACC,IAAP,CAAYf,SAAZ,CAAA,CAAuB1C,QAAvB,CAAgCuD,WAAhC,CAAL,EAAmD;EACjDD,MAAAA,OAAO,CAACI,MAAR,EAAA,CAAA;EAEA,MAAA,SAAA;EACD,KAAA;;MAED,MAAMC,aAAa,GAAG,EAAGR,CAAAA,MAAH,CAAU,GAAGG,OAAO,CAACM,UAArB,CAAtB,CAAA;EACA,IAAA,MAAMC,iBAAiB,GAAG,EAAA,CAAGV,MAAH,CAAUT,SAAS,CAAC,GAAD,CAAT,IAAkB,EAA5B,EAAgCA,SAAS,CAACa,WAAD,CAAT,IAA0B,EAA1D,CAA1B,CAAA;;EAEA,IAAA,KAAK,MAAM5D,SAAX,IAAwBgE,aAAxB,EAAuC;EACrC,MAAA,IAAI,CAACjE,gBAAgB,CAACC,SAAD,EAAYkE,iBAAZ,CAArB,EAAqD;EACnDP,QAAAA,OAAO,CAACQ,eAAR,CAAwBnE,SAAS,CAACG,QAAlC,CAAA,CAAA;EACD,OAAA;EACF,KAAA;EACF,GAAA;;EAED,EAAA,OAAOkD,eAAe,CAACI,IAAhB,CAAqBW,SAA5B,CAAA;EACD;;;;;;;;;;;"}