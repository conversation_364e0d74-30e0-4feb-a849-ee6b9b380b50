{"version": 3, "file": "simplebar-core.esm.js", "sources": ["../src/helpers.js", "../src/scrollbar-width.js", "../src/simplebar.js"], "sourcesContent": ["// Helper function to retrieve options from element attributes\nexport const getOptions = function(obj) {\n  const options = Array.prototype.reduce.call(\n    obj,\n    (acc, attribute) => {\n      const option = attribute.name.match(/data-simplebar-(.+)/);\n      if (option) {\n        const key = option[1].replace(/\\W+(.)/g, (x, chr) => chr.toUpperCase());\n        switch (attribute.value) {\n          case 'true':\n            acc[key] = true;\n            break;\n          case 'false':\n            acc[key] = false;\n            break;\n          case undefined:\n            acc[key] = true;\n            break;\n          default:\n            acc[key] = attribute.value;\n        }\n      }\n      return acc;\n    },\n    {}\n  );\n  return options;\n};\n\nexport function getElementWindow(element) {\n  if (\n    !element ||\n    !element.ownerDocument ||\n    !element.ownerDocument.defaultView\n  ) {\n    return window;\n  }\n  return element.ownerDocument.defaultView;\n}\n\nexport function getElementDocument(element) {\n  if (!element || !element.ownerDocument) {\n    return document;\n  }\n  return element.ownerDocument;\n}\n", "import canUseDOM from 'can-use-dom';\nimport { getElementDocument } from \"./helpers\";\n\nlet cachedScrollbarWidth = null;\nlet cachedDevicePixelRatio = null;\n\nif (canUseDOM) {\n  window.addEventListener('resize', () => {\n    if (cachedDevicePixelRatio !== window.devicePixelRatio) {\n      cachedDevicePixelRatio = window.devicePixelRatio;\n      cachedScrollbarWidth = null;\n    }\n  });\n}\n\nexport default function scrollbarWidth(el) {\n  if (cachedScrollbarWidth === null) {\n    \n    const document = getElementDocument(el);\n    \n    if (typeof document === 'undefined') {\n      cachedScrollbarWidth = 0;\n      return cachedScrollbarWidth;\n    }\n    const body = document.body;\n    const box = document.createElement('div');\n\n    box.classList.add('simplebar-hide-scrollbar');\n\n    body.appendChild(box);\n\n    const width = box.getBoundingClientRect().right;\n\n    body.removeChild(box);\n\n    cachedScrollbarWidth = width;\n  }\n\n  return cachedScrollbarWidth;\n}\n", "import throttle from 'lodash.throttle';\nimport debounce from 'lodash.debounce';\nimport memoize from 'lodash.memoize';\nimport { ResizeObserver } from '@juggle/resize-observer';\nimport canUseDOM from 'can-use-dom';\nimport scrollbarWidth from './scrollbar-width';\nimport { getElementWindow, getElementDocument } from './helpers';\n\nexport default class SimpleBar {\n  constructor(element, options) {\n    this.el = element;\n    this.minScrollbarWidth = 20;\n    this.options = { ...SimpleBar.defaultOptions, ...options };\n    this.classNames = {\n      ...SimpleBar.defaultOptions.classNames,\n      ...this.options.classNames\n    };\n    this.axis = {\n      x: {\n        scrollOffsetAttr: 'scrollLeft',\n        sizeAttr: 'width',\n        scrollSizeAttr: 'scrollWidth',\n        offsetSizeAttr: 'offsetWidth',\n        offsetAttr: 'left',\n        overflowAttr: 'overflowX',\n        dragOffset: 0,\n        isOverflowing: true,\n        isVisible: false,\n        forceVisible: false,\n        track: {},\n        scrollbar: {}\n      },\n      y: {\n        scrollOffsetAttr: 'scrollTop',\n        sizeAttr: 'height',\n        scrollSizeAttr: 'scrollHeight',\n        offsetSizeAttr: 'offsetHeight',\n        offsetAttr: 'top',\n        overflowAttr: 'overflowY',\n        dragOffset: 0,\n        isOverflowing: true,\n        isVisible: false,\n        forceVisible: false,\n        track: {},\n        scrollbar: {}\n      }\n    };\n    this.removePreventClickId = null;\n\n    // Don't re-instantiate over an existing one\n    if (SimpleBar.instances.has(this.el)) {\n      return;\n    }\n\n    this.recalculate = throttle(this.recalculate.bind(this), 64);\n    this.onMouseMove = throttle(this.onMouseMove.bind(this), 64);\n    this.hideScrollbars = debounce(\n      this.hideScrollbars.bind(this),\n      this.options.timeout\n    );\n    this.onWindowResize = debounce(this.onWindowResize.bind(this), 64, {\n      leading: true\n    });\n\n    SimpleBar.getRtlHelpers = memoize(SimpleBar.getRtlHelpers);\n\n    this.init();\n  }\n\n  /**\n   * Static properties\n   */\n\n  /**\n   * Helper to fix browsers inconsistency on RTL:\n   *  - Firefox inverts the scrollbar initial position\n   *  - IE11 inverts both scrollbar position and scrolling offset\n   * Directly inspired by @KingSora's OverlayScrollbars https://github.com/KingSora/OverlayScrollbars/blob/master/js/OverlayScrollbars.js#L1634\n   */\n  static getRtlHelpers() {\n    const dummyDiv = document.createElement('div');\n    dummyDiv.innerHTML =\n      '<div class=\"hs-dummy-scrollbar-size\"><div style=\"height: 200%; width: 200%; margin: 10px 0;\"></div></div>';\n    const scrollbarDummyEl = dummyDiv.firstElementChild;\n    document.body.appendChild(scrollbarDummyEl);\n    const dummyContainerChild = scrollbarDummyEl.firstElementChild;\n    scrollbarDummyEl.scrollLeft = 0;\n    const dummyContainerOffset = SimpleBar.getOffset(scrollbarDummyEl);\n    const dummyContainerChildOffset = SimpleBar.getOffset(dummyContainerChild);\n    scrollbarDummyEl.scrollLeft = 999;\n    const dummyContainerScrollOffsetAfterScroll = SimpleBar.getOffset(\n      dummyContainerChild\n    );\n\n    return {\n      // determines if the scrolling is responding with negative values\n      isRtlScrollingInverted:\n        dummyContainerOffset.left !== dummyContainerChildOffset.left &&\n        dummyContainerChildOffset.left -\n          dummyContainerScrollOffsetAfterScroll.left !==\n          0,\n      // determines if the origin scrollbar position is inverted or not (positioned on left or right)\n      isRtlScrollbarInverted:\n        dummyContainerOffset.left !== dummyContainerChildOffset.left\n    };\n  }\n\n  static defaultOptions = {\n    autoHide: true,\n    forceVisible: false,\n    clickOnTrack: true,\n    clickOnTrackSpeed: 40,\n    classNames: {\n      contentEl: 'simplebar-content',\n      contentWrapper: 'simplebar-content-wrapper',\n      offset: 'simplebar-offset',\n      mask: 'simplebar-mask',\n      wrapper: 'simplebar-wrapper',\n      placeholder: 'simplebar-placeholder',\n      scrollbar: 'simplebar-scrollbar',\n      track: 'simplebar-track',\n      heightAutoObserverWrapperEl: 'simplebar-height-auto-observer-wrapper',\n      heightAutoObserverEl: 'simplebar-height-auto-observer',\n      visible: 'simplebar-visible',\n      horizontal: 'simplebar-horizontal',\n      vertical: 'simplebar-vertical',\n      hover: 'simplebar-hover',\n      dragging: 'simplebar-dragging'\n    },\n    scrollbarMinSize: 25,\n    scrollbarMaxSize: 0,\n    timeout: 1000\n  };\n\n  static getOffset(el) {\n    const rect = el.getBoundingClientRect();\n    const elDocument = getElementDocument(el);\n    const elWindow = getElementWindow(el);\n\n    return {\n      top:\n        rect.top +\n        (elWindow.pageYOffset || elDocument.documentElement.scrollTop),\n      left:\n        rect.left +\n        (elWindow.pageXOffset || elDocument.documentElement.scrollLeft)\n    };\n  }\n\n  static instances = new WeakMap();\n\n  init() {\n    // Save a reference to the instance, so we know this DOM node has already been instancied\n    SimpleBar.instances.set(this.el, this);\n\n    // We stop here on server-side\n    if (canUseDOM) {\n      this.initDOM();\n\n      this.setAccessibilityAttributes();\n\n      this.scrollbarWidth = this.getScrollbarWidth();\n\n      this.recalculate();\n\n      this.initListeners();\n    }\n  }\n\n  initDOM() {\n    // make sure this element doesn't have the elements yet\n    if (\n      Array.prototype.filter.call(this.el.children, child =>\n        child.classList.contains(this.classNames.wrapper)\n      ).length\n    ) {\n      // assume that element has his DOM already initiated\n      this.wrapperEl = this.el.querySelector(`.${this.classNames.wrapper}`);\n      this.contentWrapperEl =\n        this.options.scrollableNode ||\n        this.el.querySelector(`.${this.classNames.contentWrapper}`);\n      this.contentEl =\n        this.options.contentNode ||\n        this.el.querySelector(`.${this.classNames.contentEl}`);\n\n      this.offsetEl = this.el.querySelector(`.${this.classNames.offset}`);\n      this.maskEl = this.el.querySelector(`.${this.classNames.mask}`);\n\n      this.placeholderEl = this.findChild(\n        this.wrapperEl,\n        `.${this.classNames.placeholder}`\n      );\n      this.heightAutoObserverWrapperEl = this.el.querySelector(\n        `.${this.classNames.heightAutoObserverWrapperEl}`\n      );\n      this.heightAutoObserverEl = this.el.querySelector(\n        `.${this.classNames.heightAutoObserverEl}`\n      );\n      this.axis.x.track.el = this.findChild(\n        this.el,\n        `.${this.classNames.track}.${this.classNames.horizontal}`\n      );\n      this.axis.y.track.el = this.findChild(\n        this.el,\n        `.${this.classNames.track}.${this.classNames.vertical}`\n      );\n    } else {\n      // Prepare DOM\n      this.wrapperEl = document.createElement('div');\n      this.contentWrapperEl = document.createElement('div');\n      this.offsetEl = document.createElement('div');\n      this.maskEl = document.createElement('div');\n      this.contentEl = document.createElement('div');\n      this.placeholderEl = document.createElement('div');\n      this.heightAutoObserverWrapperEl = document.createElement('div');\n      this.heightAutoObserverEl = document.createElement('div');\n\n      this.wrapperEl.classList.add(this.classNames.wrapper);\n      this.contentWrapperEl.classList.add(this.classNames.contentWrapper);\n      this.offsetEl.classList.add(this.classNames.offset);\n      this.maskEl.classList.add(this.classNames.mask);\n      this.contentEl.classList.add(this.classNames.contentEl);\n      this.placeholderEl.classList.add(this.classNames.placeholder);\n      this.heightAutoObserverWrapperEl.classList.add(\n        this.classNames.heightAutoObserverWrapperEl\n      );\n      this.heightAutoObserverEl.classList.add(\n        this.classNames.heightAutoObserverEl\n      );\n\n      while (this.el.firstChild) {\n        this.contentEl.appendChild(this.el.firstChild);\n      }\n\n      this.contentWrapperEl.appendChild(this.contentEl);\n      this.offsetEl.appendChild(this.contentWrapperEl);\n      this.maskEl.appendChild(this.offsetEl);\n      this.heightAutoObserverWrapperEl.appendChild(this.heightAutoObserverEl);\n      this.wrapperEl.appendChild(this.heightAutoObserverWrapperEl);\n      this.wrapperEl.appendChild(this.maskEl);\n      this.wrapperEl.appendChild(this.placeholderEl);\n      this.el.appendChild(this.wrapperEl);\n    }\n\n    if (!this.axis.x.track.el || !this.axis.y.track.el) {\n      const track = document.createElement('div');\n      const scrollbar = document.createElement('div');\n\n      track.classList.add(this.classNames.track);\n      scrollbar.classList.add(this.classNames.scrollbar);\n\n      track.appendChild(scrollbar);\n\n      this.axis.x.track.el = track.cloneNode(true);\n      this.axis.x.track.el.classList.add(this.classNames.horizontal);\n\n      this.axis.y.track.el = track.cloneNode(true);\n      this.axis.y.track.el.classList.add(this.classNames.vertical);\n\n      this.el.appendChild(this.axis.x.track.el);\n      this.el.appendChild(this.axis.y.track.el);\n    }\n\n    this.axis.x.scrollbar.el = this.axis.x.track.el.querySelector(\n      `.${this.classNames.scrollbar}`\n    );\n    this.axis.y.scrollbar.el = this.axis.y.track.el.querySelector(\n      `.${this.classNames.scrollbar}`\n    );\n\n    if (!this.options.autoHide) {\n      this.axis.x.scrollbar.el.classList.add(this.classNames.visible);\n      this.axis.y.scrollbar.el.classList.add(this.classNames.visible);\n    }\n\n    this.el.setAttribute('data-simplebar', 'init');\n  }\n\n  setAccessibilityAttributes() {\n    const ariaLabel = this.options.ariaLabel || 'scrollable content';\n\n    this.contentWrapperEl.setAttribute('tabindex', '0');\n    this.contentWrapperEl.setAttribute('role', 'region');\n    this.contentWrapperEl.setAttribute('aria-label', ariaLabel);\n  }\n\n  initListeners() {\n    const elWindow = getElementWindow(this.el);\n    // Event listeners\n    if (this.options.autoHide) {\n      this.el.addEventListener('mouseenter', this.onMouseEnter);\n    }\n\n    ['mousedown', 'click', 'dblclick'].forEach(e => {\n      this.el.addEventListener(e, this.onPointerEvent, true);\n    });\n\n    ['touchstart', 'touchend', 'touchmove'].forEach(e => {\n      this.el.addEventListener(e, this.onPointerEvent, {\n        capture: true,\n        passive: true\n      });\n    });\n\n    this.el.addEventListener('mousemove', this.onMouseMove);\n    this.el.addEventListener('mouseleave', this.onMouseLeave);\n\n    this.contentWrapperEl.addEventListener('scroll', this.onScroll);\n\n    // Browser zoom triggers a window resize\n    elWindow.addEventListener('resize', this.onWindowResize);\n\n    // Hack for https://github.com/WICG/ResizeObserver/issues/38\n    let resizeObserverStarted = false;\n    const resizeObserver = elWindow.ResizeObserver || ResizeObserver;\n    this.resizeObserver = new resizeObserver(() => {\n      if (!resizeObserverStarted) return;\n      this.recalculate();\n    });\n\n    this.resizeObserver.observe(this.el);\n    this.resizeObserver.observe(this.contentEl);\n\n    elWindow.requestAnimationFrame(() => {\n      resizeObserverStarted = true;\n    });\n\n    // This is required to detect horizontal scroll. Vertical scroll only needs the resizeObserver.\n    this.mutationObserver = new elWindow.MutationObserver(this.recalculate);\n\n    this.mutationObserver.observe(this.contentEl, {\n      childList: true,\n      subtree: true,\n      characterData: true\n    });\n  }\n\n  recalculate() {\n    const elWindow = getElementWindow(this.el);\n    this.elStyles = elWindow.getComputedStyle(this.el);\n    this.isRtl = this.elStyles.direction === 'rtl';\n\n    const isHeightAuto = this.heightAutoObserverEl.offsetHeight <= 1;\n    const isWidthAuto = this.heightAutoObserverEl.offsetWidth <= 1;\n    const contentElOffsetWidth = this.contentEl.offsetWidth;\n\n    const contentWrapperElOffsetWidth = this.contentWrapperEl.offsetWidth;\n\n    const elOverflowX = this.elStyles.overflowX;\n    const elOverflowY = this.elStyles.overflowY;\n\n    this.contentEl.style.padding = `${this.elStyles.paddingTop} ${this.elStyles.paddingRight} ${this.elStyles.paddingBottom} ${this.elStyles.paddingLeft}`;\n    this.wrapperEl.style.margin = `-${this.elStyles.paddingTop} -${this.elStyles.paddingRight} -${this.elStyles.paddingBottom} -${this.elStyles.paddingLeft}`;\n\n    const contentElScrollHeight = this.contentEl.scrollHeight;\n    const contentElScrollWidth = this.contentEl.scrollWidth;\n\n    this.contentWrapperEl.style.height = isHeightAuto ? 'auto' : '100%';\n\n    // Determine placeholder size\n    this.placeholderEl.style.width = isWidthAuto\n      ? `${contentElOffsetWidth}px`\n      : 'auto';\n    this.placeholderEl.style.height = `${contentElScrollHeight}px`;\n\n    const contentWrapperElOffsetHeight = this.contentWrapperEl.offsetHeight;\n\n    this.axis.x.isOverflowing = contentElScrollWidth > contentElOffsetWidth;\n    this.axis.y.isOverflowing =\n      contentElScrollHeight > contentWrapperElOffsetHeight;\n\n    // Set isOverflowing to false if user explicitely set hidden overflow\n    this.axis.x.isOverflowing =\n      elOverflowX === 'hidden' ? false : this.axis.x.isOverflowing;\n    this.axis.y.isOverflowing =\n      elOverflowY === 'hidden' ? false : this.axis.y.isOverflowing;\n\n    this.axis.x.forceVisible =\n      this.options.forceVisible === 'x' || this.options.forceVisible === true;\n    this.axis.y.forceVisible =\n      this.options.forceVisible === 'y' || this.options.forceVisible === true;\n\n    this.hideNativeScrollbar();\n\n    // Set isOverflowing to false if scrollbar is not necessary (content is shorter than offset)\n    let offsetForXScrollbar = this.axis.x.isOverflowing\n      ? this.scrollbarWidth\n      : 0;\n    let offsetForYScrollbar = this.axis.y.isOverflowing\n      ? this.scrollbarWidth\n      : 0;\n\n    this.axis.x.isOverflowing =\n      this.axis.x.isOverflowing &&\n      contentElScrollWidth > contentWrapperElOffsetWidth - offsetForYScrollbar;\n    this.axis.y.isOverflowing =\n      this.axis.y.isOverflowing &&\n      contentElScrollHeight >\n        contentWrapperElOffsetHeight - offsetForXScrollbar;\n\n    this.axis.x.scrollbar.size = this.getScrollbarSize('x');\n    this.axis.y.scrollbar.size = this.getScrollbarSize('y');\n\n    this.axis.x.scrollbar.el.style.width = `${this.axis.x.scrollbar.size}px`;\n    this.axis.y.scrollbar.el.style.height = `${this.axis.y.scrollbar.size}px`;\n\n    this.positionScrollbar('x');\n    this.positionScrollbar('y');\n\n    this.toggleTrackVisibility('x');\n    this.toggleTrackVisibility('y');\n  }\n\n  /**\n   * Calculate scrollbar size\n   */\n  getScrollbarSize(axis = 'y') {\n    if (!this.axis[axis].isOverflowing) {\n      return 0;\n    }\n\n    const contentSize = this.contentEl[this.axis[axis].scrollSizeAttr];\n    const trackSize = this.axis[axis].track.el[this.axis[axis].offsetSizeAttr];\n    let scrollbarSize;\n\n    let scrollbarRatio = trackSize / contentSize;\n\n    // Calculate new height/position of drag handle.\n    scrollbarSize = Math.max(\n      ~~(scrollbarRatio * trackSize),\n      this.options.scrollbarMinSize\n    );\n\n    if (this.options.scrollbarMaxSize) {\n      scrollbarSize = Math.min(scrollbarSize, this.options.scrollbarMaxSize);\n    }\n\n    return scrollbarSize;\n  }\n\n  positionScrollbar(axis = 'y') {\n    if (!this.axis[axis].isOverflowing) {\n      return;\n    }\n\n    const contentSize = this.contentWrapperEl[this.axis[axis].scrollSizeAttr];\n    const trackSize = this.axis[axis].track.el[this.axis[axis].offsetSizeAttr];\n    const hostSize = parseInt(this.elStyles[this.axis[axis].sizeAttr], 10);\n    const scrollbar = this.axis[axis].scrollbar;\n\n    let scrollOffset = this.contentWrapperEl[this.axis[axis].scrollOffsetAttr];\n    scrollOffset =\n      axis === 'x' &&\n      this.isRtl &&\n      SimpleBar.getRtlHelpers().isRtlScrollingInverted\n        ? -scrollOffset\n        : scrollOffset;\n    let scrollPourcent = scrollOffset / (contentSize - hostSize);\n\n    let handleOffset = ~~((trackSize - scrollbar.size) * scrollPourcent);\n    handleOffset =\n      axis === 'x' &&\n      this.isRtl &&\n      SimpleBar.getRtlHelpers().isRtlScrollbarInverted\n        ? handleOffset + (trackSize - scrollbar.size)\n        : handleOffset;\n\n    scrollbar.el.style.transform =\n      axis === 'x'\n        ? `translate3d(${handleOffset}px, 0, 0)`\n        : `translate3d(0, ${handleOffset}px, 0)`;\n  }\n\n  toggleTrackVisibility(axis = 'y') {\n    const track = this.axis[axis].track.el;\n    const scrollbar = this.axis[axis].scrollbar.el;\n\n    if (this.axis[axis].isOverflowing || this.axis[axis].forceVisible) {\n      track.style.visibility = 'visible';\n      this.contentWrapperEl.style[this.axis[axis].overflowAttr] = 'scroll';\n    } else {\n      track.style.visibility = 'hidden';\n      this.contentWrapperEl.style[this.axis[axis].overflowAttr] = 'hidden';\n    }\n\n    // Even if forceVisible is enabled, scrollbar itself should be hidden\n    if (this.axis[axis].isOverflowing) {\n      scrollbar.style.display = 'block';\n    } else {\n      scrollbar.style.display = 'none';\n    }\n  }\n\n  hideNativeScrollbar() {\n    this.offsetEl.style[this.isRtl ? 'left' : 'right'] =\n      this.axis.y.isOverflowing || this.axis.y.forceVisible\n        ? `-${this.scrollbarWidth}px`\n        : 0;\n    this.offsetEl.style.bottom =\n      this.axis.x.isOverflowing || this.axis.x.forceVisible\n        ? `-${this.scrollbarWidth}px`\n        : 0;\n  }\n\n  /**\n   * On scroll event handling\n   */\n  onScroll = () => {\n    const elWindow = getElementWindow(this.el);\n    if (!this.scrollXTicking) {\n      elWindow.requestAnimationFrame(this.scrollX);\n      this.scrollXTicking = true;\n    }\n\n    if (!this.scrollYTicking) {\n      elWindow.requestAnimationFrame(this.scrollY);\n      this.scrollYTicking = true;\n    }\n  };\n\n  scrollX = () => {\n    if (this.axis.x.isOverflowing) {\n      this.showScrollbar('x');\n      this.positionScrollbar('x');\n    }\n\n    this.scrollXTicking = false;\n  };\n\n  scrollY = () => {\n    if (this.axis.y.isOverflowing) {\n      this.showScrollbar('y');\n      this.positionScrollbar('y');\n    }\n\n    this.scrollYTicking = false;\n  };\n\n  onMouseEnter = () => {\n    this.showScrollbar('x');\n    this.showScrollbar('y');\n  };\n\n  onMouseMove = e => {\n    this.mouseX = e.clientX;\n    this.mouseY = e.clientY;\n\n    if (this.axis.x.isOverflowing || this.axis.x.forceVisible) {\n      this.onMouseMoveForAxis('x');\n    }\n\n    if (this.axis.y.isOverflowing || this.axis.y.forceVisible) {\n      this.onMouseMoveForAxis('y');\n    }\n  };\n\n  onMouseMoveForAxis(axis = 'y') {\n    this.axis[axis].track.rect = this.axis[\n      axis\n    ].track.el.getBoundingClientRect();\n    this.axis[axis].scrollbar.rect = this.axis[\n      axis\n    ].scrollbar.el.getBoundingClientRect();\n\n    const isWithinScrollbarBoundsX = this.isWithinBounds(\n      this.axis[axis].scrollbar.rect\n    );\n\n    if (isWithinScrollbarBoundsX) {\n      this.axis[axis].scrollbar.el.classList.add(this.classNames.hover);\n    } else {\n      this.axis[axis].scrollbar.el.classList.remove(this.classNames.hover);\n    }\n\n    if (this.isWithinBounds(this.axis[axis].track.rect)) {\n      this.showScrollbar(axis);\n      this.axis[axis].track.el.classList.add(this.classNames.hover);\n    } else {\n      this.axis[axis].track.el.classList.remove(this.classNames.hover);\n    }\n  }\n\n  onMouseLeave = () => {\n    this.onMouseMove.cancel();\n\n    if (this.axis.x.isOverflowing || this.axis.x.forceVisible) {\n      this.onMouseLeaveForAxis('x');\n    }\n\n    if (this.axis.y.isOverflowing || this.axis.y.forceVisible) {\n      this.onMouseLeaveForAxis('y');\n    }\n\n    this.mouseX = -1;\n    this.mouseY = -1;\n  };\n\n  onMouseLeaveForAxis(axis = 'y') {\n    this.axis[axis].track.el.classList.remove(this.classNames.hover);\n    this.axis[axis].scrollbar.el.classList.remove(this.classNames.hover);\n  }\n\n  onWindowResize = () => {\n    // Recalculate scrollbarWidth in case it's a zoom\n    this.scrollbarWidth = this.getScrollbarWidth();\n\n    this.hideNativeScrollbar();\n  };\n\n  /**\n   * Show scrollbar\n   */\n  showScrollbar(axis = 'y') {\n    let scrollbar = this.axis[axis].scrollbar.el;\n\n    if (!this.axis[axis].isVisible) {\n      scrollbar.classList.add(this.classNames.visible);\n      this.axis[axis].isVisible = true;\n    }\n\n    if (this.options.autoHide) {\n      this.hideScrollbars();\n    }\n  }\n\n  /**\n   * Hide Scrollbar\n   */\n  hideScrollbars = () => {\n    this.axis.x.track.rect = this.axis.x.track.el.getBoundingClientRect();\n    this.axis.y.track.rect = this.axis.y.track.el.getBoundingClientRect();\n\n    if (!this.isWithinBounds(this.axis.y.track.rect)) {\n      this.axis.y.scrollbar.el.classList.remove(this.classNames.visible);\n      this.axis.y.isVisible = false;\n    }\n\n    if (!this.isWithinBounds(this.axis.x.track.rect)) {\n      this.axis.x.scrollbar.el.classList.remove(this.classNames.visible);\n      this.axis.x.isVisible = false;\n    }\n  };\n\n  onPointerEvent = e => {\n    let isWithinTrackXBounds, isWithinTrackYBounds;\n\n    this.axis.x.track.rect = this.axis.x.track.el.getBoundingClientRect();\n    this.axis.y.track.rect = this.axis.y.track.el.getBoundingClientRect();\n\n    if (this.axis.x.isOverflowing || this.axis.x.forceVisible) {\n      isWithinTrackXBounds = this.isWithinBounds(this.axis.x.track.rect);\n    }\n\n    if (this.axis.y.isOverflowing || this.axis.y.forceVisible) {\n      isWithinTrackYBounds = this.isWithinBounds(this.axis.y.track.rect);\n    }\n\n    // If any pointer event is called on the scrollbar\n    if (isWithinTrackXBounds || isWithinTrackYBounds) {\n      // Preventing the event's default action stops text being\n      // selectable during the drag.\n      e.preventDefault();\n      // Prevent event leaking\n      e.stopPropagation();\n\n      if (e.type === 'mousedown') {\n        if (isWithinTrackXBounds) {\n          this.axis.x.scrollbar.rect = this.axis.x.scrollbar.el.getBoundingClientRect();\n\n          if (this.isWithinBounds(this.axis.x.scrollbar.rect)) {\n            this.onDragStart(e, 'x');\n          } else {\n            this.onTrackClick(e, 'x');\n          }\n        }\n\n        if (isWithinTrackYBounds) {\n          this.axis.y.scrollbar.rect = this.axis.y.scrollbar.el.getBoundingClientRect();\n\n          if (this.isWithinBounds(this.axis.y.scrollbar.rect)) {\n            this.onDragStart(e, 'y');\n          } else {\n            this.onTrackClick(e, 'y');\n          }\n        }\n      }\n    }\n  };\n\n  /**\n   * on scrollbar handle drag movement starts\n   */\n  onDragStart(e, axis = 'y') {\n    const elDocument = getElementDocument(this.el);\n    const elWindow = getElementWindow(this.el);\n    const scrollbar = this.axis[axis].scrollbar;\n\n    // Measure how far the user's mouse is from the top of the scrollbar drag handle.\n    const eventOffset = axis === 'y' ? e.pageY : e.pageX;\n    this.axis[axis].dragOffset =\n      eventOffset - scrollbar.rect[this.axis[axis].offsetAttr];\n    this.draggedAxis = axis;\n\n    this.el.classList.add(this.classNames.dragging);\n\n    elDocument.addEventListener('mousemove', this.drag, true);\n    elDocument.addEventListener('mouseup', this.onEndDrag, true);\n    if (this.removePreventClickId === null) {\n      elDocument.addEventListener('click', this.preventClick, true);\n      elDocument.addEventListener('dblclick', this.preventClick, true);\n    } else {\n      elWindow.clearTimeout(this.removePreventClickId);\n      this.removePreventClickId = null;\n    }\n  }\n\n  /**\n   * Drag scrollbar handle\n   */\n  drag = e => {\n    let eventOffset;\n    const track = this.axis[this.draggedAxis].track;\n    const trackSize = track.rect[this.axis[this.draggedAxis].sizeAttr];\n    const scrollbar = this.axis[this.draggedAxis].scrollbar;\n    const contentSize = this.contentWrapperEl[\n      this.axis[this.draggedAxis].scrollSizeAttr\n    ];\n    const hostSize = parseInt(\n      this.elStyles[this.axis[this.draggedAxis].sizeAttr],\n      10\n    );\n\n    e.preventDefault();\n    e.stopPropagation();\n\n    if (this.draggedAxis === 'y') {\n      eventOffset = e.pageY;\n    } else {\n      eventOffset = e.pageX;\n    }\n\n    // Calculate how far the user's mouse is from the top/left of the scrollbar (minus the dragOffset).\n    let dragPos =\n      eventOffset -\n      track.rect[this.axis[this.draggedAxis].offsetAttr] -\n      this.axis[this.draggedAxis].dragOffset;\n    // Convert the mouse position into a percentage of the scrollbar height/width.\n    let dragPerc = dragPos / (trackSize - scrollbar.size);\n\n    // Scroll the content by the same percentage.\n    let scrollPos = dragPerc * (contentSize - hostSize);\n\n    // Fix browsers inconsistency on RTL\n    if (this.draggedAxis === 'x') {\n      scrollPos =\n        this.isRtl && SimpleBar.getRtlHelpers().isRtlScrollbarInverted\n          ? scrollPos - (trackSize + scrollbar.size)\n          : scrollPos;\n      scrollPos =\n        this.isRtl && SimpleBar.getRtlHelpers().isRtlScrollingInverted\n          ? -scrollPos\n          : scrollPos;\n    }\n\n    this.contentWrapperEl[\n      this.axis[this.draggedAxis].scrollOffsetAttr\n    ] = scrollPos;\n  };\n\n  /**\n   * End scroll handle drag\n   */\n  onEndDrag = e => {\n    const elDocument = getElementDocument(this.el);\n    const elWindow = getElementWindow(this.el);\n    e.preventDefault();\n    e.stopPropagation();\n\n    this.el.classList.remove(this.classNames.dragging);\n\n    elDocument.removeEventListener('mousemove', this.drag, true);\n    elDocument.removeEventListener('mouseup', this.onEndDrag, true);\n    this.removePreventClickId = elWindow.setTimeout(() => {\n      // Remove these asynchronously so we still suppress click events\n      // generated simultaneously with mouseup.\n      elDocument.removeEventListener('click', this.preventClick, true);\n      elDocument.removeEventListener('dblclick', this.preventClick, true);\n      this.removePreventClickId = null;\n    });\n  };\n\n  /**\n   * Handler to ignore click events during drag\n   */\n  preventClick = e => {\n    e.preventDefault();\n    e.stopPropagation();\n  };\n\n  onTrackClick(e, axis = 'y') {\n    if (!this.options.clickOnTrack) return;\n\n    const elWindow = getElementWindow(this.el);\n    this.axis[axis].scrollbar.rect = this.axis[\n      axis\n    ].scrollbar.el.getBoundingClientRect();\n    const scrollbar = this.axis[axis].scrollbar;\n    const scrollbarOffset = scrollbar.rect[this.axis[axis].offsetAttr];\n    const hostSize = parseInt(this.elStyles[this.axis[axis].sizeAttr], 10);\n    let scrolled = this.contentWrapperEl[this.axis[axis].scrollOffsetAttr];\n    const t =\n      axis === 'y'\n        ? this.mouseY - scrollbarOffset\n        : this.mouseX - scrollbarOffset;\n    const dir = t < 0 ? -1 : 1;\n    const scrollSize = dir === -1 ? scrolled - hostSize : scrolled + hostSize;\n\n    const scrollTo = () => {\n      if (dir === -1) {\n        if (scrolled > scrollSize) {\n          scrolled -= this.options.clickOnTrackSpeed;\n          this.contentWrapperEl.scrollTo({\n            [this.axis[axis].offsetAttr]: scrolled\n          });\n          elWindow.requestAnimationFrame(scrollTo);\n        }\n      } else {\n        if (scrolled < scrollSize) {\n          scrolled += this.options.clickOnTrackSpeed;\n          this.contentWrapperEl.scrollTo({\n            [this.axis[axis].offsetAttr]: scrolled\n          });\n          elWindow.requestAnimationFrame(scrollTo);\n        }\n      }\n    };\n\n    scrollTo();\n  }\n\n  /**\n   * Getter for content element\n   */\n  getContentElement() {\n    return this.contentEl;\n  }\n\n  /**\n   * Getter for original scrolling element\n   */\n  getScrollElement() {\n    return this.contentWrapperEl;\n  }\n\n  getScrollbarWidth() {\n    // Try/catch for FF 56 throwing on undefined computedStyles\n    try {\n      // Detect browsers supporting CSS scrollbar styling and do not calculate\n      if (\n        getComputedStyle(this.contentWrapperEl, '::-webkit-scrollbar')\n          .display === 'none' ||\n        'scrollbarWidth' in document.documentElement.style ||\n        '-ms-overflow-style' in document.documentElement.style\n      ) {\n        return 0;\n      } else {\n        return scrollbarWidth(this.el);\n      }\n    } catch (e) {\n      return scrollbarWidth(this.el);\n    }\n  }\n\n  removeListeners() {\n    const elWindow = getElementWindow(this.el);\n    // Event listeners\n    if (this.options.autoHide) {\n      this.el.removeEventListener('mouseenter', this.onMouseEnter);\n    }\n\n    ['mousedown', 'click', 'dblclick'].forEach(e => {\n      this.el.removeEventListener(e, this.onPointerEvent, true);\n    });\n\n    ['touchstart', 'touchend', 'touchmove'].forEach(e => {\n      this.el.removeEventListener(e, this.onPointerEvent, {\n        capture: true,\n        passive: true\n      });\n    });\n\n    this.el.removeEventListener('mousemove', this.onMouseMove);\n    this.el.removeEventListener('mouseleave', this.onMouseLeave);\n\n    if (this.contentWrapperEl) {\n      this.contentWrapperEl.removeEventListener('scroll', this.onScroll);\n    }\n\n    elWindow.removeEventListener('resize', this.onWindowResize);\n\n    if (this.mutationObserver) {\n      this.mutationObserver.disconnect();\n    }\n\n    if (this.resizeObserver) {\n      this.resizeObserver.disconnect();\n    }\n\n    // Cancel all debounced functions\n    this.recalculate.cancel();\n    this.onMouseMove.cancel();\n    this.hideScrollbars.cancel();\n    this.onWindowResize.cancel();\n  }\n\n  /**\n   * UnMount mutation observer and delete SimpleBar instance from DOM element\n   */\n  unMount() {\n    this.removeListeners();\n    SimpleBar.instances.delete(this.el);\n  }\n\n  /**\n   * Check if mouse is within bounds\n   */\n  isWithinBounds(bbox) {\n    return (\n      this.mouseX >= bbox.left &&\n      this.mouseX <= bbox.left + bbox.width &&\n      this.mouseY >= bbox.top &&\n      this.mouseY <= bbox.top + bbox.height\n    );\n  }\n\n  /**\n   * Find element children matches query\n   */\n  findChild(el, query) {\n    const matches =\n      el.matches ||\n      el.webkitMatchesSelector ||\n      el.mozMatchesSelector ||\n      el.msMatchesSelector;\n    return Array.prototype.filter.call(el.children, child =>\n      matches.call(child, query)\n    )[0];\n  }\n}\n"], "names": ["getElementWindow", "element", "ownerDocument", "defaultView", "window", "getElementDocument", "document", "cachedScrollbarWidth", "cachedDevicePixelRatio", "canUseDOM", "addEventListener", "devicePixelRatio", "scrollbarWidth", "el", "body", "box", "createElement", "classList", "add", "append<PERSON><PERSON><PERSON>", "width", "getBoundingClientRect", "right", "<PERSON><PERSON><PERSON><PERSON>", "SimpleBar", "options", "onScroll", "<PERSON><PERSON><PERSON><PERSON>", "scrollXTicking", "requestAnimationFrame", "scrollX", "scrollYTicking", "scrollY", "axis", "x", "isOverflowing", "showScrollbar", "positionScrollbar", "y", "onMouseEnter", "onMouseMove", "e", "mouseX", "clientX", "mouseY", "clientY", "forceVisible", "onMouseMoveForAxis", "onMouseLeave", "cancel", "onMouseLeaveForAxis", "onWindowResize", "getScrollbarWidth", "hideNativeScrollbar", "hideScrollbars", "track", "rect", "isWithinBounds", "scrollbar", "remove", "classNames", "visible", "isVisible", "onPointerEvent", "isWithinTrackXBounds", "isWithinTrackYBounds", "preventDefault", "stopPropagation", "type", "onDragStart", "onTrackClick", "drag", "eventOffset", "<PERSON><PERSON><PERSON><PERSON>", "trackSize", "sizeAttr", "contentSize", "contentWrapperEl", "scrollSizeAttr", "hostSize", "parseInt", "elStyles", "pageY", "pageX", "dragPos", "offsetAttr", "dragOffset", "dragPerc", "size", "scrollPos", "isRtl", "getRtlHelpers", "isRtlScrollbarInverted", "isRtlScrollingInverted", "scrollOffsetAttr", "onEndDrag", "elDocument", "dragging", "removeEventListener", "removePreventClickId", "setTimeout", "preventClick", "minScrollbarWidth", "defaultOptions", "offsetSizeAttr", "overflowAttr", "instances", "has", "recalculate", "throttle", "bind", "debounce", "timeout", "leading", "memoize", "init", "dummyDiv", "innerHTML", "scrollbarDummyEl", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "du<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scrollLeft", "dummyContainerOffset", "getOffset", "dummyContainerChildOffset", "dummyContainerScrollOffsetAfterScroll", "left", "top", "pageYOffset", "documentElement", "scrollTop", "pageXOffset", "set", "initDOM", "setAccessibilityAttributes", "initListeners", "Array", "prototype", "filter", "call", "children", "child", "contains", "wrapper", "length", "wrapperEl", "querySelector", "scrollableNode", "contentWrapper", "contentEl", "contentNode", "offsetEl", "offset", "maskEl", "mask", "placeholder<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "placeholder", "heightAutoObserverWrapperEl", "heightAutoObserverEl", "horizontal", "vertical", "<PERSON><PERSON><PERSON><PERSON>", "cloneNode", "autoHide", "setAttribute", "aria<PERSON><PERSON><PERSON>", "for<PERSON>ach", "capture", "passive", "resizeObserverStarted", "resizeObserver", "ResizeObserver", "observe", "mutationObserver", "MutationObserver", "childList", "subtree", "characterData", "getComputedStyle", "direction", "isHeightAuto", "offsetHeight", "isWidthAuto", "offsetWidth", "contentElOffsetWidth", "contentWrapperElOffsetWidth", "elOverflowX", "overflowX", "elOverflowY", "overflowY", "style", "padding", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft", "margin", "contentElScrollHeight", "scrollHeight", "contentElScrollWidth", "scrollWidth", "height", "contentWrapperElOffsetHeight", "offsetForXScrollbar", "offsetForYScrollbar", "getScrollbarSize", "toggleTrackVisibility", "scrollbarSize", "scrollbarRatio", "Math", "max", "scrollbarMinSize", "scrollbarMaxSize", "min", "scrollOffset", "scrollPourcent", "handleOffset", "transform", "visibility", "display", "bottom", "isWithinScrollbarBoundsX", "hover", "clearTimeout", "clickOnTrack", "scrollbarOffset", "scrolled", "t", "dir", "scrollSize", "scrollTo", "clickOnTrackSpeed", "getContentElement", "getScrollElement", "removeListeners", "disconnect", "unMount", "delete", "bbox", "query", "matches", "webkitMatchesSelector", "mozMatchesSelector", "msMatchesSelector", "WeakMap"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BO,SAASA,gBAAT,CAA0BC,OAA1B,EAAmC;MAEtC,CAACA,OAAD,IACA,CAACA,OAAO,CAACC,aADT,IAEA,CAACD,OAAO,CAACC,aAAR,CAAsBC,WAHzB,EAIE;WACOC,MAAP;;;SAEKH,OAAO,CAACC,aAAR,CAAsBC,WAA7B;;AAGK,SAASE,kBAAT,CAA4BJ,OAA5B,EAAqC;MACtC,CAACA,OAAD,IAAY,CAACA,OAAO,CAACC,aAAzB,EAAwC;WAC/BI,QAAP;;;SAEKL,OAAO,CAACC,aAAf;;;ACzCF,IAAIK,oBAAoB,GAAG,IAA3B;AACA,IAAIC,sBAAsB,GAAG,IAA7B;;AAEA,IAAIC,SAAJ,EAAe;EACbL,MAAM,CAACM,gBAAP,CAAwB,QAAxB,EAAkC,YAAM;QAClCF,sBAAsB,KAAKJ,MAAM,CAACO,gBAAtC,EAAwD;MACtDH,sBAAsB,GAAGJ,MAAM,CAACO,gBAAhC;MACAJ,oBAAoB,GAAG,IAAvB;;GAHJ;;;AAQa,SAASK,cAAT,CAAwBC,EAAxB,EAA4B;MACrCN,oBAAoB,KAAK,IAA7B,EAAmC;QAE3BD,QAAQ,GAAGD,kBAAkB,CAACQ,EAAD,CAAnC;;QAEI,OAAOP,QAAP,KAAoB,WAAxB,EAAqC;MACnCC,oBAAoB,GAAG,CAAvB;aACOA,oBAAP;;;QAEIO,IAAI,GAAGR,QAAQ,CAACQ,IAAtB;QACMC,GAAG,GAAGT,QAAQ,CAACU,aAAT,CAAuB,KAAvB,CAAZ;IAEAD,GAAG,CAACE,SAAJ,CAAcC,GAAd,CAAkB,0BAAlB;IAEAJ,IAAI,CAACK,WAAL,CAAiBJ,GAAjB;QAEMK,KAAK,GAAGL,GAAG,CAACM,qBAAJ,GAA4BC,KAA1C;IAEAR,IAAI,CAACS,WAAL,CAAiBR,GAAjB;IAEAR,oBAAoB,GAAGa,KAAvB;;;SAGKb,oBAAP;;;IC9BmBiB;;;qBACPvB,OAAZ,EAAqBwB,OAArB,EAA8B;;;SAkf9BC,QAlf8B,GAkfnB,YAAM;UACTC,QAAQ,GAAG3B,gBAAgB,CAAC,KAAI,CAACa,EAAN,CAAjC;;UACI,CAAC,KAAI,CAACe,cAAV,EAA0B;QACxBD,QAAQ,CAACE,qBAAT,CAA+B,KAAI,CAACC,OAApC;QACA,KAAI,CAACF,cAAL,GAAsB,IAAtB;;;UAGE,CAAC,KAAI,CAACG,cAAV,EAA0B;QACxBJ,QAAQ,CAACE,qBAAT,CAA+B,KAAI,CAACG,OAApC;QACA,KAAI,CAACD,cAAL,GAAsB,IAAtB;;KA3f0B;;SA+f9BD,OA/f8B,GA+fpB,YAAM;UACV,KAAI,CAACG,IAAL,CAAUC,CAAV,CAAYC,aAAhB,EAA+B;QAC7B,KAAI,CAACC,aAAL,CAAmB,GAAnB;;QACA,KAAI,CAACC,iBAAL,CAAuB,GAAvB;;;MAGF,KAAI,CAACT,cAAL,GAAsB,KAAtB;KArgB4B;;SAwgB9BI,OAxgB8B,GAwgBpB,YAAM;UACV,KAAI,CAACC,IAAL,CAAUK,CAAV,CAAYH,aAAhB,EAA+B;QAC7B,KAAI,CAACC,aAAL,CAAmB,GAAnB;;QACA,KAAI,CAACC,iBAAL,CAAuB,GAAvB;;;MAGF,KAAI,CAACN,cAAL,GAAsB,KAAtB;KA9gB4B;;SAihB9BQ,YAjhB8B,GAihBf,YAAM;MACnB,KAAI,CAACH,aAAL,CAAmB,GAAnB;;MACA,KAAI,CAACA,aAAL,CAAmB,GAAnB;KAnhB4B;;SAshB9BI,WAthB8B,GAshBhB,UAAAC,CAAC,EAAI;MACjB,KAAI,CAACC,MAAL,GAAcD,CAAC,CAACE,OAAhB;MACA,KAAI,CAACC,MAAL,GAAcH,CAAC,CAACI,OAAhB;;UAEI,KAAI,CAACZ,IAAL,CAAUC,CAAV,CAAYC,aAAZ,IAA6B,KAAI,CAACF,IAAL,CAAUC,CAAV,CAAYY,YAA7C,EAA2D;QACzD,KAAI,CAACC,kBAAL,CAAwB,GAAxB;;;UAGE,KAAI,CAACd,IAAL,CAAUK,CAAV,CAAYH,aAAZ,IAA6B,KAAI,CAACF,IAAL,CAAUK,CAAV,CAAYQ,YAA7C,EAA2D;QACzD,KAAI,CAACC,kBAAL,CAAwB,GAAxB;;KA/hB0B;;SA6jB9BC,YA7jB8B,GA6jBf,YAAM;MACnB,KAAI,CAACR,WAAL,CAAiBS,MAAjB;;UAEI,KAAI,CAAChB,IAAL,CAAUC,CAAV,CAAYC,aAAZ,IAA6B,KAAI,CAACF,IAAL,CAAUC,CAAV,CAAYY,YAA7C,EAA2D;QACzD,KAAI,CAACI,mBAAL,CAAyB,GAAzB;;;UAGE,KAAI,CAACjB,IAAL,CAAUK,CAAV,CAAYH,aAAZ,IAA6B,KAAI,CAACF,IAAL,CAAUK,CAAV,CAAYQ,YAA7C,EAA2D;QACzD,KAAI,CAACI,mBAAL,CAAyB,GAAzB;;;MAGF,KAAI,CAACR,MAAL,GAAc,CAAC,CAAf;MACA,KAAI,CAACE,MAAL,GAAc,CAAC,CAAf;KAzkB4B;;SAilB9BO,cAjlB8B,GAilBb,YAAM;;MAErB,KAAI,CAACvC,cAAL,GAAsB,KAAI,CAACwC,iBAAL,EAAtB;;MAEA,KAAI,CAACC,mBAAL;KArlB4B;;SA2mB9BC,cA3mB8B,GA2mBb,YAAM;MACrB,KAAI,CAACrB,IAAL,CAAUC,CAAV,CAAYqB,KAAZ,CAAkBC,IAAlB,GAAyB,KAAI,CAACvB,IAAL,CAAUC,CAAV,CAAYqB,KAAZ,CAAkB1C,EAAlB,CAAqBQ,qBAArB,EAAzB;MACA,KAAI,CAACY,IAAL,CAAUK,CAAV,CAAYiB,KAAZ,CAAkBC,IAAlB,GAAyB,KAAI,CAACvB,IAAL,CAAUK,CAAV,CAAYiB,KAAZ,CAAkB1C,EAAlB,CAAqBQ,qBAArB,EAAzB;;UAEI,CAAC,KAAI,CAACoC,cAAL,CAAoB,KAAI,CAACxB,IAAL,CAAUK,CAAV,CAAYiB,KAAZ,CAAkBC,IAAtC,CAAL,EAAkD;QAChD,KAAI,CAACvB,IAAL,CAAUK,CAAV,CAAYoB,SAAZ,CAAsB7C,EAAtB,CAAyBI,SAAzB,CAAmC0C,MAAnC,CAA0C,KAAI,CAACC,UAAL,CAAgBC,OAA1D;;QACA,KAAI,CAAC5B,IAAL,CAAUK,CAAV,CAAYwB,SAAZ,GAAwB,KAAxB;;;UAGE,CAAC,KAAI,CAACL,cAAL,CAAoB,KAAI,CAACxB,IAAL,CAAUC,CAAV,CAAYqB,KAAZ,CAAkBC,IAAtC,CAAL,EAAkD;QAChD,KAAI,CAACvB,IAAL,CAAUC,CAAV,CAAYwB,SAAZ,CAAsB7C,EAAtB,CAAyBI,SAAzB,CAAmC0C,MAAnC,CAA0C,KAAI,CAACC,UAAL,CAAgBC,OAA1D;;QACA,KAAI,CAAC5B,IAAL,CAAUC,CAAV,CAAY4B,SAAZ,GAAwB,KAAxB;;KAtnB0B;;SA0nB9BC,cA1nB8B,GA0nBb,UAAAtB,CAAC,EAAI;UAChBuB,oBAAJ,EAA0BC,oBAA1B;MAEA,KAAI,CAAChC,IAAL,CAAUC,CAAV,CAAYqB,KAAZ,CAAkBC,IAAlB,GAAyB,KAAI,CAACvB,IAAL,CAAUC,CAAV,CAAYqB,KAAZ,CAAkB1C,EAAlB,CAAqBQ,qBAArB,EAAzB;MACA,KAAI,CAACY,IAAL,CAAUK,CAAV,CAAYiB,KAAZ,CAAkBC,IAAlB,GAAyB,KAAI,CAACvB,IAAL,CAAUK,CAAV,CAAYiB,KAAZ,CAAkB1C,EAAlB,CAAqBQ,qBAArB,EAAzB;;UAEI,KAAI,CAACY,IAAL,CAAUC,CAAV,CAAYC,aAAZ,IAA6B,KAAI,CAACF,IAAL,CAAUC,CAAV,CAAYY,YAA7C,EAA2D;QACzDkB,oBAAoB,GAAG,KAAI,CAACP,cAAL,CAAoB,KAAI,CAACxB,IAAL,CAAUC,CAAV,CAAYqB,KAAZ,CAAkBC,IAAtC,CAAvB;;;UAGE,KAAI,CAACvB,IAAL,CAAUK,CAAV,CAAYH,aAAZ,IAA6B,KAAI,CAACF,IAAL,CAAUK,CAAV,CAAYQ,YAA7C,EAA2D;QACzDmB,oBAAoB,GAAG,KAAI,CAACR,cAAL,CAAoB,KAAI,CAACxB,IAAL,CAAUK,CAAV,CAAYiB,KAAZ,CAAkBC,IAAtC,CAAvB;OAXkB;;;UAehBQ,oBAAoB,IAAIC,oBAA5B,EAAkD;;;QAGhDxB,CAAC,CAACyB,cAAF,GAHgD;;QAKhDzB,CAAC,CAAC0B,eAAF;;YAEI1B,CAAC,CAAC2B,IAAF,KAAW,WAAf,EAA4B;cACtBJ,oBAAJ,EAA0B;YACxB,KAAI,CAAC/B,IAAL,CAAUC,CAAV,CAAYwB,SAAZ,CAAsBF,IAAtB,GAA6B,KAAI,CAACvB,IAAL,CAAUC,CAAV,CAAYwB,SAAZ,CAAsB7C,EAAtB,CAAyBQ,qBAAzB,EAA7B;;gBAEI,KAAI,CAACoC,cAAL,CAAoB,KAAI,CAACxB,IAAL,CAAUC,CAAV,CAAYwB,SAAZ,CAAsBF,IAA1C,CAAJ,EAAqD;cACnD,KAAI,CAACa,WAAL,CAAiB5B,CAAjB,EAAoB,GAApB;aADF,MAEO;cACL,KAAI,CAAC6B,YAAL,CAAkB7B,CAAlB,EAAqB,GAArB;;;;cAIAwB,oBAAJ,EAA0B;YACxB,KAAI,CAAChC,IAAL,CAAUK,CAAV,CAAYoB,SAAZ,CAAsBF,IAAtB,GAA6B,KAAI,CAACvB,IAAL,CAAUK,CAAV,CAAYoB,SAAZ,CAAsB7C,EAAtB,CAAyBQ,qBAAzB,EAA7B;;gBAEI,KAAI,CAACoC,cAAL,CAAoB,KAAI,CAACxB,IAAL,CAAUK,CAAV,CAAYoB,SAAZ,CAAsBF,IAA1C,CAAJ,EAAqD;cACnD,KAAI,CAACa,WAAL,CAAiB5B,CAAjB,EAAoB,GAApB;aADF,MAEO;cACL,KAAI,CAAC6B,YAAL,CAAkB7B,CAAlB,EAAqB,GAArB;;;;;KAjqBoB;;SAssB9B8B,IAtsB8B,GAssBvB,UAAA9B,CAAC,EAAI;UACN+B,WAAJ;UACMjB,KAAK,GAAG,KAAI,CAACtB,IAAL,CAAU,KAAI,CAACwC,WAAf,EAA4BlB,KAA1C;UACMmB,SAAS,GAAGnB,KAAK,CAACC,IAAN,CAAW,KAAI,CAACvB,IAAL,CAAU,KAAI,CAACwC,WAAf,EAA4BE,QAAvC,CAAlB;UACMjB,SAAS,GAAG,KAAI,CAACzB,IAAL,CAAU,KAAI,CAACwC,WAAf,EAA4Bf,SAA9C;UACMkB,WAAW,GAAG,KAAI,CAACC,gBAAL,CAClB,KAAI,CAAC5C,IAAL,CAAU,KAAI,CAACwC,WAAf,EAA4BK,cADV,CAApB;UAGMC,QAAQ,GAAGC,QAAQ,CACvB,KAAI,CAACC,QAAL,CAAc,KAAI,CAAChD,IAAL,CAAU,KAAI,CAACwC,WAAf,EAA4BE,QAA1C,CADuB,EAEvB,EAFuB,CAAzB;MAKAlC,CAAC,CAACyB,cAAF;MACAzB,CAAC,CAAC0B,eAAF;;UAEI,KAAI,CAACM,WAAL,KAAqB,GAAzB,EAA8B;QAC5BD,WAAW,GAAG/B,CAAC,CAACyC,KAAhB;OADF,MAEO;QACLV,WAAW,GAAG/B,CAAC,CAAC0C,KAAhB;OAnBQ;;;UAuBNC,OAAO,GACTZ,WAAW,GACXjB,KAAK,CAACC,IAAN,CAAW,KAAI,CAACvB,IAAL,CAAU,KAAI,CAACwC,WAAf,EAA4BY,UAAvC,CADA,GAEA,KAAI,CAACpD,IAAL,CAAU,KAAI,CAACwC,WAAf,EAA4Ba,UAH9B,CAvBU;;UA4BNC,QAAQ,GAAGH,OAAO,IAAIV,SAAS,GAAGhB,SAAS,CAAC8B,IAA1B,CAAtB,CA5BU;;UA+BNC,SAAS,GAAGF,QAAQ,IAAIX,WAAW,GAAGG,QAAlB,CAAxB,CA/BU;;UAkCN,KAAI,CAACN,WAAL,KAAqB,GAAzB,EAA8B;QAC5BgB,SAAS,GACP,KAAI,CAACC,KAAL,IAAclE,SAAS,CAACmE,aAAV,GAA0BC,sBAAxC,GACIH,SAAS,IAAIf,SAAS,GAAGhB,SAAS,CAAC8B,IAA1B,CADb,GAEIC,SAHN;QAIAA,SAAS,GACP,KAAI,CAACC,KAAL,IAAclE,SAAS,CAACmE,aAAV,GAA0BE,sBAAxC,GACI,CAACJ,SADL,GAEIA,SAHN;;;MAMF,KAAI,CAACZ,gBAAL,CACE,KAAI,CAAC5C,IAAL,CAAU,KAAI,CAACwC,WAAf,EAA4BqB,gBAD9B,IAEIL,SAFJ;KAnvB4B;;SA2vB9BM,SA3vB8B,GA2vBlB,UAAAtD,CAAC,EAAI;UACTuD,UAAU,GAAG3F,kBAAkB,CAAC,KAAI,CAACQ,EAAN,CAArC;UACMc,QAAQ,GAAG3B,gBAAgB,CAAC,KAAI,CAACa,EAAN,CAAjC;MACA4B,CAAC,CAACyB,cAAF;MACAzB,CAAC,CAAC0B,eAAF;;MAEA,KAAI,CAACtD,EAAL,CAAQI,SAAR,CAAkB0C,MAAlB,CAAyB,KAAI,CAACC,UAAL,CAAgBqC,QAAzC;;MAEAD,UAAU,CAACE,mBAAX,CAA+B,WAA/B,EAA4C,KAAI,CAAC3B,IAAjD,EAAuD,IAAvD;MACAyB,UAAU,CAACE,mBAAX,CAA+B,SAA/B,EAA0C,KAAI,CAACH,SAA/C,EAA0D,IAA1D;MACA,KAAI,CAACI,oBAAL,GAA4BxE,QAAQ,CAACyE,UAAT,CAAoB,YAAM;;;QAGpDJ,UAAU,CAACE,mBAAX,CAA+B,OAA/B,EAAwC,KAAI,CAACG,YAA7C,EAA2D,IAA3D;QACAL,UAAU,CAACE,mBAAX,CAA+B,UAA/B,EAA2C,KAAI,CAACG,YAAhD,EAA8D,IAA9D;QACA,KAAI,CAACF,oBAAL,GAA4B,IAA5B;OAL0B,CAA5B;KArwB4B;;SAixB9BE,YAjxB8B,GAixBf,UAAA5D,CAAC,EAAI;MAClBA,CAAC,CAACyB,cAAF;MACAzB,CAAC,CAAC0B,eAAF;KAnxB4B;;SACvBtD,EAAL,GAAUZ,OAAV;SACKqG,iBAAL,GAAyB,EAAzB;SACK7E,OAAL,qBAAoBD,SAAS,CAAC+E,cAA9B,MAAiD9E,OAAjD;SACKmC,UAAL,qBACKpC,SAAS,CAAC+E,cAAV,CAAyB3C,UAD9B,MAEK,KAAKnC,OAAL,CAAamC,UAFlB;SAIK3B,IAAL,GAAY;MACVC,CAAC,EAAE;QACD4D,gBAAgB,EAAE,YADjB;QAEDnB,QAAQ,EAAE,OAFT;QAGDG,cAAc,EAAE,aAHf;QAID0B,cAAc,EAAE,aAJf;QAKDnB,UAAU,EAAE,MALX;QAMDoB,YAAY,EAAE,WANb;QAODnB,UAAU,EAAE,CAPX;QAQDnD,aAAa,EAAE,IARd;QASD2B,SAAS,EAAE,KATV;QAUDhB,YAAY,EAAE,KAVb;QAWDS,KAAK,EAAE,EAXN;QAYDG,SAAS,EAAE;OAbH;MAeVpB,CAAC,EAAE;QACDwD,gBAAgB,EAAE,WADjB;QAEDnB,QAAQ,EAAE,QAFT;QAGDG,cAAc,EAAE,cAHf;QAID0B,cAAc,EAAE,cAJf;QAKDnB,UAAU,EAAE,KALX;QAMDoB,YAAY,EAAE,WANb;QAODnB,UAAU,EAAE,CAPX;QAQDnD,aAAa,EAAE,IARd;QASD2B,SAAS,EAAE,KATV;QAUDhB,YAAY,EAAE,KAVb;QAWDS,KAAK,EAAE,EAXN;QAYDG,SAAS,EAAE;;KA3Bf;SA8BKyC,oBAAL,GAA4B,IAA5B,CAtC4B;;QAyCxB3E,SAAS,CAACkF,SAAV,CAAoBC,GAApB,CAAwB,KAAK9F,EAA7B,CAAJ,EAAsC;;;;SAIjC+F,WAAL,GAAmBC,QAAQ,CAAC,KAAKD,WAAL,CAAiBE,IAAjB,CAAsB,IAAtB,CAAD,EAA8B,EAA9B,CAA3B;SACKtE,WAAL,GAAmBqE,QAAQ,CAAC,KAAKrE,WAAL,CAAiBsE,IAAjB,CAAsB,IAAtB,CAAD,EAA8B,EAA9B,CAA3B;SACKxD,cAAL,GAAsByD,QAAQ,CAC5B,KAAKzD,cAAL,CAAoBwD,IAApB,CAAyB,IAAzB,CAD4B,EAE5B,KAAKrF,OAAL,CAAauF,OAFe,CAA9B;SAIK7D,cAAL,GAAsB4D,QAAQ,CAAC,KAAK5D,cAAL,CAAoB2D,IAApB,CAAyB,IAAzB,CAAD,EAAiC,EAAjC,EAAqC;MACjEG,OAAO,EAAE;KADmB,CAA9B;IAIAzF,SAAS,CAACmE,aAAV,GAA0BuB,OAAO,CAAC1F,SAAS,CAACmE,aAAX,CAAjC;SAEKwB,IAAL;;;;;;;;;;;;;;YAaKxB,gBAAP,yBAAuB;QACfyB,QAAQ,GAAG9G,QAAQ,CAACU,aAAT,CAAuB,KAAvB,CAAjB;IACAoG,QAAQ,CAACC,SAAT,GACE,2GADF;QAEMC,gBAAgB,GAAGF,QAAQ,CAACG,iBAAlC;IACAjH,QAAQ,CAACQ,IAAT,CAAcK,WAAd,CAA0BmG,gBAA1B;QACME,mBAAmB,GAAGF,gBAAgB,CAACC,iBAA7C;IACAD,gBAAgB,CAACG,UAAjB,GAA8B,CAA9B;QACMC,oBAAoB,GAAGlG,SAAS,CAACmG,SAAV,CAAoBL,gBAApB,CAA7B;QACMM,yBAAyB,GAAGpG,SAAS,CAACmG,SAAV,CAAoBH,mBAApB,CAAlC;IACAF,gBAAgB,CAACG,UAAjB,GAA8B,GAA9B;QACMI,qCAAqC,GAAGrG,SAAS,CAACmG,SAAV,CAC5CH,mBAD4C,CAA9C;WAIO;;MAEL3B,sBAAsB,EACpB6B,oBAAoB,CAACI,IAArB,KAA8BF,yBAAyB,CAACE,IAAxD,IACAF,yBAAyB,CAACE,IAA1B,GACED,qCAAqC,CAACC,IADxC,KAEE,CANC;;MAQLlC,sBAAsB,EACpB8B,oBAAoB,CAACI,IAArB,KAA8BF,yBAAyB,CAACE;KAT5D;;;YAwCKH,YAAP,mBAAiB9G,EAAjB,EAAqB;QACb2C,IAAI,GAAG3C,EAAE,CAACQ,qBAAH,EAAb;QACM2E,UAAU,GAAG3F,kBAAkB,CAACQ,EAAD,CAArC;QACMc,QAAQ,GAAG3B,gBAAgB,CAACa,EAAD,CAAjC;WAEO;MACLkH,GAAG,EACDvE,IAAI,CAACuE,GAAL,IACCpG,QAAQ,CAACqG,WAAT,IAAwBhC,UAAU,CAACiC,eAAX,CAA2BC,SADpD,CAFG;MAILJ,IAAI,EACFtE,IAAI,CAACsE,IAAL,IACCnG,QAAQ,CAACwG,WAAT,IAAwBnC,UAAU,CAACiC,eAAX,CAA2BR,UADpD;KALJ;;;;;SAYFN,OAAA,gBAAO;;IAEL3F,SAAS,CAACkF,SAAV,CAAoB0B,GAApB,CAAwB,KAAKvH,EAA7B,EAAiC,IAAjC,EAFK;;QAKDJ,SAAJ,EAAe;WACR4H,OAAL;WAEKC,0BAAL;WAEK1H,cAAL,GAAsB,KAAKwC,iBAAL,EAAtB;WAEKwD,WAAL;WAEK2B,aAAL;;;;SAIJF,UAAA,mBAAU;;;;QAGNG,KAAK,CAACC,SAAN,CAAgBC,MAAhB,CAAuBC,IAAvB,CAA4B,KAAK9H,EAAL,CAAQ+H,QAApC,EAA8C,UAAAC,KAAK;aACjDA,KAAK,CAAC5H,SAAN,CAAgB6H,QAAhB,CAAyB,MAAI,CAAClF,UAAL,CAAgBmF,OAAzC,CADiD;KAAnD,EAEEC,MAHJ,EAIE;;WAEKC,SAAL,GAAiB,KAAKpI,EAAL,CAAQqI,aAAR,OAA0B,KAAKtF,UAAL,CAAgBmF,OAA1C,CAAjB;WACKlE,gBAAL,GACE,KAAKpD,OAAL,CAAa0H,cAAb,IACA,KAAKtI,EAAL,CAAQqI,aAAR,OAA0B,KAAKtF,UAAL,CAAgBwF,cAA1C,CAFF;WAGKC,SAAL,GACE,KAAK5H,OAAL,CAAa6H,WAAb,IACA,KAAKzI,EAAL,CAAQqI,aAAR,OAA0B,KAAKtF,UAAL,CAAgByF,SAA1C,CAFF;WAIKE,QAAL,GAAgB,KAAK1I,EAAL,CAAQqI,aAAR,OAA0B,KAAKtF,UAAL,CAAgB4F,MAA1C,CAAhB;WACKC,MAAL,GAAc,KAAK5I,EAAL,CAAQqI,aAAR,OAA0B,KAAKtF,UAAL,CAAgB8F,IAA1C,CAAd;WAEKC,aAAL,GAAqB,KAAKC,SAAL,CACnB,KAAKX,SADc,QAEf,KAAKrF,UAAL,CAAgBiG,WAFD,CAArB;WAIKC,2BAAL,GAAmC,KAAKjJ,EAAL,CAAQqI,aAAR,OAC7B,KAAKtF,UAAL,CAAgBkG,2BADa,CAAnC;WAGKC,oBAAL,GAA4B,KAAKlJ,EAAL,CAAQqI,aAAR,OACtB,KAAKtF,UAAL,CAAgBmG,oBADM,CAA5B;WAGK9H,IAAL,CAAUC,CAAV,CAAYqB,KAAZ,CAAkB1C,EAAlB,GAAuB,KAAK+I,SAAL,CACrB,KAAK/I,EADgB,QAEjB,KAAK+C,UAAL,CAAgBL,KAFC,SAEQ,KAAKK,UAAL,CAAgBoG,UAFxB,CAAvB;WAIK/H,IAAL,CAAUK,CAAV,CAAYiB,KAAZ,CAAkB1C,EAAlB,GAAuB,KAAK+I,SAAL,CACrB,KAAK/I,EADgB,QAEjB,KAAK+C,UAAL,CAAgBL,KAFC,SAEQ,KAAKK,UAAL,CAAgBqG,QAFxB,CAAvB;KA/BF,MAmCO;;WAEAhB,SAAL,GAAiB3I,QAAQ,CAACU,aAAT,CAAuB,KAAvB,CAAjB;WACK6D,gBAAL,GAAwBvE,QAAQ,CAACU,aAAT,CAAuB,KAAvB,CAAxB;WACKuI,QAAL,GAAgBjJ,QAAQ,CAACU,aAAT,CAAuB,KAAvB,CAAhB;WACKyI,MAAL,GAAcnJ,QAAQ,CAACU,aAAT,CAAuB,KAAvB,CAAd;WACKqI,SAAL,GAAiB/I,QAAQ,CAACU,aAAT,CAAuB,KAAvB,CAAjB;WACK2I,aAAL,GAAqBrJ,QAAQ,CAACU,aAAT,CAAuB,KAAvB,CAArB;WACK8I,2BAAL,GAAmCxJ,QAAQ,CAACU,aAAT,CAAuB,KAAvB,CAAnC;WACK+I,oBAAL,GAA4BzJ,QAAQ,CAACU,aAAT,CAAuB,KAAvB,CAA5B;WAEKiI,SAAL,CAAehI,SAAf,CAAyBC,GAAzB,CAA6B,KAAK0C,UAAL,CAAgBmF,OAA7C;WACKlE,gBAAL,CAAsB5D,SAAtB,CAAgCC,GAAhC,CAAoC,KAAK0C,UAAL,CAAgBwF,cAApD;WACKG,QAAL,CAActI,SAAd,CAAwBC,GAAxB,CAA4B,KAAK0C,UAAL,CAAgB4F,MAA5C;WACKC,MAAL,CAAYxI,SAAZ,CAAsBC,GAAtB,CAA0B,KAAK0C,UAAL,CAAgB8F,IAA1C;WACKL,SAAL,CAAepI,SAAf,CAAyBC,GAAzB,CAA6B,KAAK0C,UAAL,CAAgByF,SAA7C;WACKM,aAAL,CAAmB1I,SAAnB,CAA6BC,GAA7B,CAAiC,KAAK0C,UAAL,CAAgBiG,WAAjD;WACKC,2BAAL,CAAiC7I,SAAjC,CAA2CC,GAA3C,CACE,KAAK0C,UAAL,CAAgBkG,2BADlB;WAGKC,oBAAL,CAA0B9I,SAA1B,CAAoCC,GAApC,CACE,KAAK0C,UAAL,CAAgBmG,oBADlB;;aAIO,KAAKlJ,EAAL,CAAQqJ,UAAf,EAA2B;aACpBb,SAAL,CAAelI,WAAf,CAA2B,KAAKN,EAAL,CAAQqJ,UAAnC;;;WAGGrF,gBAAL,CAAsB1D,WAAtB,CAAkC,KAAKkI,SAAvC;WACKE,QAAL,CAAcpI,WAAd,CAA0B,KAAK0D,gBAA/B;WACK4E,MAAL,CAAYtI,WAAZ,CAAwB,KAAKoI,QAA7B;WACKO,2BAAL,CAAiC3I,WAAjC,CAA6C,KAAK4I,oBAAlD;WACKd,SAAL,CAAe9H,WAAf,CAA2B,KAAK2I,2BAAhC;WACKb,SAAL,CAAe9H,WAAf,CAA2B,KAAKsI,MAAhC;WACKR,SAAL,CAAe9H,WAAf,CAA2B,KAAKwI,aAAhC;WACK9I,EAAL,CAAQM,WAAR,CAAoB,KAAK8H,SAAzB;;;QAGE,CAAC,KAAKhH,IAAL,CAAUC,CAAV,CAAYqB,KAAZ,CAAkB1C,EAAnB,IAAyB,CAAC,KAAKoB,IAAL,CAAUK,CAAV,CAAYiB,KAAZ,CAAkB1C,EAAhD,EAAoD;UAC5C0C,KAAK,GAAGjD,QAAQ,CAACU,aAAT,CAAuB,KAAvB,CAAd;UACM0C,SAAS,GAAGpD,QAAQ,CAACU,aAAT,CAAuB,KAAvB,CAAlB;MAEAuC,KAAK,CAACtC,SAAN,CAAgBC,GAAhB,CAAoB,KAAK0C,UAAL,CAAgBL,KAApC;MACAG,SAAS,CAACzC,SAAV,CAAoBC,GAApB,CAAwB,KAAK0C,UAAL,CAAgBF,SAAxC;MAEAH,KAAK,CAACpC,WAAN,CAAkBuC,SAAlB;WAEKzB,IAAL,CAAUC,CAAV,CAAYqB,KAAZ,CAAkB1C,EAAlB,GAAuB0C,KAAK,CAAC4G,SAAN,CAAgB,IAAhB,CAAvB;WACKlI,IAAL,CAAUC,CAAV,CAAYqB,KAAZ,CAAkB1C,EAAlB,CAAqBI,SAArB,CAA+BC,GAA/B,CAAmC,KAAK0C,UAAL,CAAgBoG,UAAnD;WAEK/H,IAAL,CAAUK,CAAV,CAAYiB,KAAZ,CAAkB1C,EAAlB,GAAuB0C,KAAK,CAAC4G,SAAN,CAAgB,IAAhB,CAAvB;WACKlI,IAAL,CAAUK,CAAV,CAAYiB,KAAZ,CAAkB1C,EAAlB,CAAqBI,SAArB,CAA+BC,GAA/B,CAAmC,KAAK0C,UAAL,CAAgBqG,QAAnD;WAEKpJ,EAAL,CAAQM,WAAR,CAAoB,KAAKc,IAAL,CAAUC,CAAV,CAAYqB,KAAZ,CAAkB1C,EAAtC;WACKA,EAAL,CAAQM,WAAR,CAAoB,KAAKc,IAAL,CAAUK,CAAV,CAAYiB,KAAZ,CAAkB1C,EAAtC;;;SAGGoB,IAAL,CAAUC,CAAV,CAAYwB,SAAZ,CAAsB7C,EAAtB,GAA2B,KAAKoB,IAAL,CAAUC,CAAV,CAAYqB,KAAZ,CAAkB1C,EAAlB,CAAqBqI,aAArB,OACrB,KAAKtF,UAAL,CAAgBF,SADK,CAA3B;SAGKzB,IAAL,CAAUK,CAAV,CAAYoB,SAAZ,CAAsB7C,EAAtB,GAA2B,KAAKoB,IAAL,CAAUK,CAAV,CAAYiB,KAAZ,CAAkB1C,EAAlB,CAAqBqI,aAArB,OACrB,KAAKtF,UAAL,CAAgBF,SADK,CAA3B;;QAII,CAAC,KAAKjC,OAAL,CAAa2I,QAAlB,EAA4B;WACrBnI,IAAL,CAAUC,CAAV,CAAYwB,SAAZ,CAAsB7C,EAAtB,CAAyBI,SAAzB,CAAmCC,GAAnC,CAAuC,KAAK0C,UAAL,CAAgBC,OAAvD;WACK5B,IAAL,CAAUK,CAAV,CAAYoB,SAAZ,CAAsB7C,EAAtB,CAAyBI,SAAzB,CAAmCC,GAAnC,CAAuC,KAAK0C,UAAL,CAAgBC,OAAvD;;;SAGGhD,EAAL,CAAQwJ,YAAR,CAAqB,gBAArB,EAAuC,MAAvC;;;SAGF/B,6BAAA,sCAA6B;QACrBgC,SAAS,GAAG,KAAK7I,OAAL,CAAa6I,SAAb,IAA0B,oBAA5C;SAEKzF,gBAAL,CAAsBwF,YAAtB,CAAmC,UAAnC,EAA+C,GAA/C;SACKxF,gBAAL,CAAsBwF,YAAtB,CAAmC,MAAnC,EAA2C,QAA3C;SACKxF,gBAAL,CAAsBwF,YAAtB,CAAmC,YAAnC,EAAiDC,SAAjD;;;SAGF/B,gBAAA,yBAAgB;;;QACR5G,QAAQ,GAAG3B,gBAAgB,CAAC,KAAKa,EAAN,CAAjC,CADc;;QAGV,KAAKY,OAAL,CAAa2I,QAAjB,EAA2B;WACpBvJ,EAAL,CAAQH,gBAAR,CAAyB,YAAzB,EAAuC,KAAK6B,YAA5C;;;KAGD,WAAD,EAAc,OAAd,EAAuB,UAAvB,EAAmCgI,OAAnC,CAA2C,UAAA9H,CAAC,EAAI;MAC9C,MAAI,CAAC5B,EAAL,CAAQH,gBAAR,CAAyB+B,CAAzB,EAA4B,MAAI,CAACsB,cAAjC,EAAiD,IAAjD;KADF;KAIC,YAAD,EAAe,UAAf,EAA2B,WAA3B,EAAwCwG,OAAxC,CAAgD,UAAA9H,CAAC,EAAI;MACnD,MAAI,CAAC5B,EAAL,CAAQH,gBAAR,CAAyB+B,CAAzB,EAA4B,MAAI,CAACsB,cAAjC,EAAiD;QAC/CyG,OAAO,EAAE,IADsC;QAE/CC,OAAO,EAAE;OAFX;KADF;SAOK5J,EAAL,CAAQH,gBAAR,CAAyB,WAAzB,EAAsC,KAAK8B,WAA3C;SACK3B,EAAL,CAAQH,gBAAR,CAAyB,YAAzB,EAAuC,KAAKsC,YAA5C;SAEK6B,gBAAL,CAAsBnE,gBAAtB,CAAuC,QAAvC,EAAiD,KAAKgB,QAAtD,EArBc;;IAwBdC,QAAQ,CAACjB,gBAAT,CAA0B,QAA1B,EAAoC,KAAKyC,cAAzC,EAxBc;;QA2BVuH,qBAAqB,GAAG,KAA5B;QACMC,cAAc,GAAGhJ,QAAQ,CAACiJ,cAAT,IAA2BA,cAAlD;SACKD,cAAL,GAAsB,IAAIA,cAAJ,CAAmB,YAAM;UACzC,CAACD,qBAAL,EAA4B;;MAC5B,MAAI,CAAC9D,WAAL;KAFoB,CAAtB;SAKK+D,cAAL,CAAoBE,OAApB,CAA4B,KAAKhK,EAAjC;SACK8J,cAAL,CAAoBE,OAApB,CAA4B,KAAKxB,SAAjC;IAEA1H,QAAQ,CAACE,qBAAT,CAA+B,YAAM;MACnC6I,qBAAqB,GAAG,IAAxB;KADF,EArCc;;SA0CTI,gBAAL,GAAwB,IAAInJ,QAAQ,CAACoJ,gBAAb,CAA8B,KAAKnE,WAAnC,CAAxB;SAEKkE,gBAAL,CAAsBD,OAAtB,CAA8B,KAAKxB,SAAnC,EAA8C;MAC5C2B,SAAS,EAAE,IADiC;MAE5CC,OAAO,EAAE,IAFmC;MAG5CC,aAAa,EAAE;KAHjB;;;SAOFtE,cAAA,uBAAc;QACNjF,QAAQ,GAAG3B,gBAAgB,CAAC,KAAKa,EAAN,CAAjC;SACKoE,QAAL,GAAgBtD,QAAQ,CAACwJ,gBAAT,CAA0B,KAAKtK,EAA/B,CAAhB;SACK6E,KAAL,GAAa,KAAKT,QAAL,CAAcmG,SAAd,KAA4B,KAAzC;QAEMC,YAAY,GAAG,KAAKtB,oBAAL,CAA0BuB,YAA1B,IAA0C,CAA/D;QACMC,WAAW,GAAG,KAAKxB,oBAAL,CAA0ByB,WAA1B,IAAyC,CAA7D;QACMC,oBAAoB,GAAG,KAAKpC,SAAL,CAAemC,WAA5C;QAEME,2BAA2B,GAAG,KAAK7G,gBAAL,CAAsB2G,WAA1D;QAEMG,WAAW,GAAG,KAAK1G,QAAL,CAAc2G,SAAlC;QACMC,WAAW,GAAG,KAAK5G,QAAL,CAAc6G,SAAlC;SAEKzC,SAAL,CAAe0C,KAAf,CAAqBC,OAArB,GAAkC,KAAK/G,QAAL,CAAcgH,UAAhD,SAA8D,KAAKhH,QAAL,CAAciH,YAA5E,SAA4F,KAAKjH,QAAL,CAAckH,aAA1G,SAA2H,KAAKlH,QAAL,CAAcmH,WAAzI;SACKnD,SAAL,CAAe8C,KAAf,CAAqBM,MAArB,SAAkC,KAAKpH,QAAL,CAAcgH,UAAhD,UAA+D,KAAKhH,QAAL,CAAciH,YAA7E,UAA8F,KAAKjH,QAAL,CAAckH,aAA5G,UAA8H,KAAKlH,QAAL,CAAcmH,WAA5I;QAEME,qBAAqB,GAAG,KAAKjD,SAAL,CAAekD,YAA7C;QACMC,oBAAoB,GAAG,KAAKnD,SAAL,CAAeoD,WAA5C;SAEK5H,gBAAL,CAAsBkH,KAAtB,CAA4BW,MAA5B,GAAqCrB,YAAY,GAAG,MAAH,GAAY,MAA7D,CApBY;;SAuBP1B,aAAL,CAAmBoC,KAAnB,CAAyB3K,KAAzB,GAAiCmK,WAAW,GACrCE,oBADqC,UAExC,MAFJ;SAGK9B,aAAL,CAAmBoC,KAAnB,CAAyBW,MAAzB,GAAqCJ,qBAArC;QAEMK,4BAA4B,GAAG,KAAK9H,gBAAL,CAAsByG,YAA3D;SAEKrJ,IAAL,CAAUC,CAAV,CAAYC,aAAZ,GAA4BqK,oBAAoB,GAAGf,oBAAnD;SACKxJ,IAAL,CAAUK,CAAV,CAAYH,aAAZ,GACEmK,qBAAqB,GAAGK,4BAD1B,CA/BY;;SAmCP1K,IAAL,CAAUC,CAAV,CAAYC,aAAZ,GACEwJ,WAAW,KAAK,QAAhB,GAA2B,KAA3B,GAAmC,KAAK1J,IAAL,CAAUC,CAAV,CAAYC,aADjD;SAEKF,IAAL,CAAUK,CAAV,CAAYH,aAAZ,GACE0J,WAAW,KAAK,QAAhB,GAA2B,KAA3B,GAAmC,KAAK5J,IAAL,CAAUK,CAAV,CAAYH,aADjD;SAGKF,IAAL,CAAUC,CAAV,CAAYY,YAAZ,GACE,KAAKrB,OAAL,CAAaqB,YAAb,KAA8B,GAA9B,IAAqC,KAAKrB,OAAL,CAAaqB,YAAb,KAA8B,IADrE;SAEKb,IAAL,CAAUK,CAAV,CAAYQ,YAAZ,GACE,KAAKrB,OAAL,CAAaqB,YAAb,KAA8B,GAA9B,IAAqC,KAAKrB,OAAL,CAAaqB,YAAb,KAA8B,IADrE;SAGKO,mBAAL,GA7CY;;QAgDRuJ,mBAAmB,GAAG,KAAK3K,IAAL,CAAUC,CAAV,CAAYC,aAAZ,GACtB,KAAKvB,cADiB,GAEtB,CAFJ;QAGIiM,mBAAmB,GAAG,KAAK5K,IAAL,CAAUK,CAAV,CAAYH,aAAZ,GACtB,KAAKvB,cADiB,GAEtB,CAFJ;SAIKqB,IAAL,CAAUC,CAAV,CAAYC,aAAZ,GACE,KAAKF,IAAL,CAAUC,CAAV,CAAYC,aAAZ,IACAqK,oBAAoB,GAAGd,2BAA2B,GAAGmB,mBAFvD;SAGK5K,IAAL,CAAUK,CAAV,CAAYH,aAAZ,GACE,KAAKF,IAAL,CAAUK,CAAV,CAAYH,aAAZ,IACAmK,qBAAqB,GACnBK,4BAA4B,GAAGC,mBAHnC;SAKK3K,IAAL,CAAUC,CAAV,CAAYwB,SAAZ,CAAsB8B,IAAtB,GAA6B,KAAKsH,gBAAL,CAAsB,GAAtB,CAA7B;SACK7K,IAAL,CAAUK,CAAV,CAAYoB,SAAZ,CAAsB8B,IAAtB,GAA6B,KAAKsH,gBAAL,CAAsB,GAAtB,CAA7B;SAEK7K,IAAL,CAAUC,CAAV,CAAYwB,SAAZ,CAAsB7C,EAAtB,CAAyBkL,KAAzB,CAA+B3K,KAA/B,GAA0C,KAAKa,IAAL,CAAUC,CAAV,CAAYwB,SAAZ,CAAsB8B,IAAhE;SACKvD,IAAL,CAAUK,CAAV,CAAYoB,SAAZ,CAAsB7C,EAAtB,CAAyBkL,KAAzB,CAA+BW,MAA/B,GAA2C,KAAKzK,IAAL,CAAUK,CAAV,CAAYoB,SAAZ,CAAsB8B,IAAjE;SAEKnD,iBAAL,CAAuB,GAAvB;SACKA,iBAAL,CAAuB,GAAvB;SAEK0K,qBAAL,CAA2B,GAA3B;SACKA,qBAAL,CAA2B,GAA3B;;;;;;;SAMFD,mBAAA,0BAAiB7K,IAAjB,EAA6B;QAAZA,IAAY;MAAZA,IAAY,GAAL,GAAK;;;QACvB,CAAC,KAAKA,IAAL,CAAUA,IAAV,EAAgBE,aAArB,EAAoC;aAC3B,CAAP;;;QAGIyC,WAAW,GAAG,KAAKyE,SAAL,CAAe,KAAKpH,IAAL,CAAUA,IAAV,EAAgB6C,cAA/B,CAApB;QACMJ,SAAS,GAAG,KAAKzC,IAAL,CAAUA,IAAV,EAAgBsB,KAAhB,CAAsB1C,EAAtB,CAAyB,KAAKoB,IAAL,CAAUA,IAAV,EAAgBuE,cAAzC,CAAlB;QACIwG,aAAJ;QAEIC,cAAc,GAAGvI,SAAS,GAAGE,WAAjC,CAT2B;;IAY3BoI,aAAa,GAAGE,IAAI,CAACC,GAAL,CACd,CAAC,EAAEF,cAAc,GAAGvI,SAAnB,CADa,EAEd,KAAKjD,OAAL,CAAa2L,gBAFC,CAAhB;;QAKI,KAAK3L,OAAL,CAAa4L,gBAAjB,EAAmC;MACjCL,aAAa,GAAGE,IAAI,CAACI,GAAL,CAASN,aAAT,EAAwB,KAAKvL,OAAL,CAAa4L,gBAArC,CAAhB;;;WAGKL,aAAP;;;SAGF3K,oBAAA,2BAAkBJ,IAAlB,EAA8B;QAAZA,IAAY;MAAZA,IAAY,GAAL,GAAK;;;QACxB,CAAC,KAAKA,IAAL,CAAUA,IAAV,EAAgBE,aAArB,EAAoC;;;;QAI9ByC,WAAW,GAAG,KAAKC,gBAAL,CAAsB,KAAK5C,IAAL,CAAUA,IAAV,EAAgB6C,cAAtC,CAApB;QACMJ,SAAS,GAAG,KAAKzC,IAAL,CAAUA,IAAV,EAAgBsB,KAAhB,CAAsB1C,EAAtB,CAAyB,KAAKoB,IAAL,CAAUA,IAAV,EAAgBuE,cAAzC,CAAlB;QACMzB,QAAQ,GAAGC,QAAQ,CAAC,KAAKC,QAAL,CAAc,KAAKhD,IAAL,CAAUA,IAAV,EAAgB0C,QAA9B,CAAD,EAA0C,EAA1C,CAAzB;QACMjB,SAAS,GAAG,KAAKzB,IAAL,CAAUA,IAAV,EAAgByB,SAAlC;QAEI6J,YAAY,GAAG,KAAK1I,gBAAL,CAAsB,KAAK5C,IAAL,CAAUA,IAAV,EAAgB6D,gBAAtC,CAAnB;IACAyH,YAAY,GACVtL,IAAI,KAAK,GAAT,IACA,KAAKyD,KADL,IAEAlE,SAAS,CAACmE,aAAV,GAA0BE,sBAF1B,GAGI,CAAC0H,YAHL,GAIIA,YALN;QAMIC,cAAc,GAAGD,YAAY,IAAI3I,WAAW,GAAGG,QAAlB,CAAjC;QAEI0I,YAAY,GAAG,CAAC,EAAE,CAAC/I,SAAS,GAAGhB,SAAS,CAAC8B,IAAvB,IAA+BgI,cAAjC,CAApB;IACAC,YAAY,GACVxL,IAAI,KAAK,GAAT,IACA,KAAKyD,KADL,IAEAlE,SAAS,CAACmE,aAAV,GAA0BC,sBAF1B,GAGI6H,YAAY,IAAI/I,SAAS,GAAGhB,SAAS,CAAC8B,IAA1B,CAHhB,GAIIiI,YALN;IAOA/J,SAAS,CAAC7C,EAAV,CAAakL,KAAb,CAAmB2B,SAAnB,GACEzL,IAAI,KAAK,GAAT,oBACmBwL,YADnB,qCAEsBA,YAFtB,WADF;;;SAMFV,wBAAA,+BAAsB9K,IAAtB,EAAkC;QAAZA,IAAY;MAAZA,IAAY,GAAL,GAAK;;;QAC1BsB,KAAK,GAAG,KAAKtB,IAAL,CAAUA,IAAV,EAAgBsB,KAAhB,CAAsB1C,EAApC;QACM6C,SAAS,GAAG,KAAKzB,IAAL,CAAUA,IAAV,EAAgByB,SAAhB,CAA0B7C,EAA5C;;QAEI,KAAKoB,IAAL,CAAUA,IAAV,EAAgBE,aAAhB,IAAiC,KAAKF,IAAL,CAAUA,IAAV,EAAgBa,YAArD,EAAmE;MACjES,KAAK,CAACwI,KAAN,CAAY4B,UAAZ,GAAyB,SAAzB;WACK9I,gBAAL,CAAsBkH,KAAtB,CAA4B,KAAK9J,IAAL,CAAUA,IAAV,EAAgBwE,YAA5C,IAA4D,QAA5D;KAFF,MAGO;MACLlD,KAAK,CAACwI,KAAN,CAAY4B,UAAZ,GAAyB,QAAzB;WACK9I,gBAAL,CAAsBkH,KAAtB,CAA4B,KAAK9J,IAAL,CAAUA,IAAV,EAAgBwE,YAA5C,IAA4D,QAA5D;KAT8B;;;QAa5B,KAAKxE,IAAL,CAAUA,IAAV,EAAgBE,aAApB,EAAmC;MACjCuB,SAAS,CAACqI,KAAV,CAAgB6B,OAAhB,GAA0B,OAA1B;KADF,MAEO;MACLlK,SAAS,CAACqI,KAAV,CAAgB6B,OAAhB,GAA0B,MAA1B;;;;SAIJvK,sBAAA,+BAAsB;SACfkG,QAAL,CAAcwC,KAAd,CAAoB,KAAKrG,KAAL,GAAa,MAAb,GAAsB,OAA1C,IACE,KAAKzD,IAAL,CAAUK,CAAV,CAAYH,aAAZ,IAA6B,KAAKF,IAAL,CAAUK,CAAV,CAAYQ,YAAzC,SACQ,KAAKlC,cADb,UAEI,CAHN;SAIK2I,QAAL,CAAcwC,KAAd,CAAoB8B,MAApB,GACE,KAAK5L,IAAL,CAAUC,CAAV,CAAYC,aAAZ,IAA6B,KAAKF,IAAL,CAAUC,CAAV,CAAYY,YAAzC,SACQ,KAAKlC,cADb,UAEI,CAHN;;;;;;;SA0DFmC,qBAAA,4BAAmBd,IAAnB,EAA+B;QAAZA,IAAY;MAAZA,IAAY,GAAL,GAAK;;;SACxBA,IAAL,CAAUA,IAAV,EAAgBsB,KAAhB,CAAsBC,IAAtB,GAA6B,KAAKvB,IAAL,CAC3BA,IAD2B,EAE3BsB,KAF2B,CAErB1C,EAFqB,CAElBQ,qBAFkB,EAA7B;SAGKY,IAAL,CAAUA,IAAV,EAAgByB,SAAhB,CAA0BF,IAA1B,GAAiC,KAAKvB,IAAL,CAC/BA,IAD+B,EAE/ByB,SAF+B,CAErB7C,EAFqB,CAElBQ,qBAFkB,EAAjC;QAIMyM,wBAAwB,GAAG,KAAKrK,cAAL,CAC/B,KAAKxB,IAAL,CAAUA,IAAV,EAAgByB,SAAhB,CAA0BF,IADK,CAAjC;;QAIIsK,wBAAJ,EAA8B;WACvB7L,IAAL,CAAUA,IAAV,EAAgByB,SAAhB,CAA0B7C,EAA1B,CAA6BI,SAA7B,CAAuCC,GAAvC,CAA2C,KAAK0C,UAAL,CAAgBmK,KAA3D;KADF,MAEO;WACA9L,IAAL,CAAUA,IAAV,EAAgByB,SAAhB,CAA0B7C,EAA1B,CAA6BI,SAA7B,CAAuC0C,MAAvC,CAA8C,KAAKC,UAAL,CAAgBmK,KAA9D;;;QAGE,KAAKtK,cAAL,CAAoB,KAAKxB,IAAL,CAAUA,IAAV,EAAgBsB,KAAhB,CAAsBC,IAA1C,CAAJ,EAAqD;WAC9CpB,aAAL,CAAmBH,IAAnB;WACKA,IAAL,CAAUA,IAAV,EAAgBsB,KAAhB,CAAsB1C,EAAtB,CAAyBI,SAAzB,CAAmCC,GAAnC,CAAuC,KAAK0C,UAAL,CAAgBmK,KAAvD;KAFF,MAGO;WACA9L,IAAL,CAAUA,IAAV,EAAgBsB,KAAhB,CAAsB1C,EAAtB,CAAyBI,SAAzB,CAAmC0C,MAAnC,CAA0C,KAAKC,UAAL,CAAgBmK,KAA1D;;;;SAmBJ7K,sBAAA,6BAAoBjB,IAApB,EAAgC;QAAZA,IAAY;MAAZA,IAAY,GAAL,GAAK;;;SACzBA,IAAL,CAAUA,IAAV,EAAgBsB,KAAhB,CAAsB1C,EAAtB,CAAyBI,SAAzB,CAAmC0C,MAAnC,CAA0C,KAAKC,UAAL,CAAgBmK,KAA1D;SACK9L,IAAL,CAAUA,IAAV,EAAgByB,SAAhB,CAA0B7C,EAA1B,CAA6BI,SAA7B,CAAuC0C,MAAvC,CAA8C,KAAKC,UAAL,CAAgBmK,KAA9D;;;;;;SAaF3L,gBAAA,uBAAcH,IAAd,EAA0B;QAAZA,IAAY;MAAZA,IAAY,GAAL,GAAK;;;QACpByB,SAAS,GAAG,KAAKzB,IAAL,CAAUA,IAAV,EAAgByB,SAAhB,CAA0B7C,EAA1C;;QAEI,CAAC,KAAKoB,IAAL,CAAUA,IAAV,EAAgB6B,SAArB,EAAgC;MAC9BJ,SAAS,CAACzC,SAAV,CAAoBC,GAApB,CAAwB,KAAK0C,UAAL,CAAgBC,OAAxC;WACK5B,IAAL,CAAUA,IAAV,EAAgB6B,SAAhB,GAA4B,IAA5B;;;QAGE,KAAKrC,OAAL,CAAa2I,QAAjB,EAA2B;WACpB9G,cAAL;;;;;;;;;;;SAuEJe,cAAA,qBAAY5B,CAAZ,EAAeR,IAAf,EAA2B;QAAZA,IAAY;MAAZA,IAAY,GAAL,GAAK;;;QACnB+D,UAAU,GAAG3F,kBAAkB,CAAC,KAAKQ,EAAN,CAArC;QACMc,QAAQ,GAAG3B,gBAAgB,CAAC,KAAKa,EAAN,CAAjC;QACM6C,SAAS,GAAG,KAAKzB,IAAL,CAAUA,IAAV,EAAgByB,SAAlC,CAHyB;;QAMnBc,WAAW,GAAGvC,IAAI,KAAK,GAAT,GAAeQ,CAAC,CAACyC,KAAjB,GAAyBzC,CAAC,CAAC0C,KAA/C;SACKlD,IAAL,CAAUA,IAAV,EAAgBqD,UAAhB,GACEd,WAAW,GAAGd,SAAS,CAACF,IAAV,CAAe,KAAKvB,IAAL,CAAUA,IAAV,EAAgBoD,UAA/B,CADhB;SAEKZ,WAAL,GAAmBxC,IAAnB;SAEKpB,EAAL,CAAQI,SAAR,CAAkBC,GAAlB,CAAsB,KAAK0C,UAAL,CAAgBqC,QAAtC;IAEAD,UAAU,CAACtF,gBAAX,CAA4B,WAA5B,EAAyC,KAAK6D,IAA9C,EAAoD,IAApD;IACAyB,UAAU,CAACtF,gBAAX,CAA4B,SAA5B,EAAuC,KAAKqF,SAA5C,EAAuD,IAAvD;;QACI,KAAKI,oBAAL,KAA8B,IAAlC,EAAwC;MACtCH,UAAU,CAACtF,gBAAX,CAA4B,OAA5B,EAAqC,KAAK2F,YAA1C,EAAwD,IAAxD;MACAL,UAAU,CAACtF,gBAAX,CAA4B,UAA5B,EAAwC,KAAK2F,YAA7C,EAA2D,IAA3D;KAFF,MAGO;MACL1E,QAAQ,CAACqM,YAAT,CAAsB,KAAK7H,oBAA3B;WACKA,oBAAL,GAA4B,IAA5B;;;;;;;;SAuFJ7B,eAAA,sBAAa7B,CAAb,EAAgBR,IAAhB,EAA4B;;;QAAZA,IAAY;MAAZA,IAAY,GAAL,GAAK;;;QACtB,CAAC,KAAKR,OAAL,CAAawM,YAAlB,EAAgC;QAE1BtM,QAAQ,GAAG3B,gBAAgB,CAAC,KAAKa,EAAN,CAAjC;SACKoB,IAAL,CAAUA,IAAV,EAAgByB,SAAhB,CAA0BF,IAA1B,GAAiC,KAAKvB,IAAL,CAC/BA,IAD+B,EAE/ByB,SAF+B,CAErB7C,EAFqB,CAElBQ,qBAFkB,EAAjC;QAGMqC,SAAS,GAAG,KAAKzB,IAAL,CAAUA,IAAV,EAAgByB,SAAlC;QACMwK,eAAe,GAAGxK,SAAS,CAACF,IAAV,CAAe,KAAKvB,IAAL,CAAUA,IAAV,EAAgBoD,UAA/B,CAAxB;QACMN,QAAQ,GAAGC,QAAQ,CAAC,KAAKC,QAAL,CAAc,KAAKhD,IAAL,CAAUA,IAAV,EAAgB0C,QAA9B,CAAD,EAA0C,EAA1C,CAAzB;QACIwJ,QAAQ,GAAG,KAAKtJ,gBAAL,CAAsB,KAAK5C,IAAL,CAAUA,IAAV,EAAgB6D,gBAAtC,CAAf;QACMsI,CAAC,GACLnM,IAAI,KAAK,GAAT,GACI,KAAKW,MAAL,GAAcsL,eADlB,GAEI,KAAKxL,MAAL,GAAcwL,eAHpB;QAIMG,GAAG,GAAGD,CAAC,GAAG,CAAJ,GAAQ,CAAC,CAAT,GAAa,CAAzB;QACME,UAAU,GAAGD,GAAG,KAAK,CAAC,CAAT,GAAaF,QAAQ,GAAGpJ,QAAxB,GAAmCoJ,QAAQ,GAAGpJ,QAAjE;;QAEMwJ,QAAQ,GAAG,SAAXA,QAAW,GAAM;UACjBF,GAAG,KAAK,CAAC,CAAb,EAAgB;YACVF,QAAQ,GAAGG,UAAf,EAA2B;;;UACzBH,QAAQ,IAAI,MAAI,CAAC1M,OAAL,CAAa+M,iBAAzB;;UACA,MAAI,CAAC3J,gBAAL,CAAsB0J,QAAtB,oDACG,MAAI,CAACtM,IAAL,CAAUA,IAAV,EAAgBoD,UADnB,IACgC8I,QADhC;;UAGAxM,QAAQ,CAACE,qBAAT,CAA+B0M,QAA/B;;OANJ,MAQO;YACDJ,QAAQ,GAAGG,UAAf,EAA2B;;;UACzBH,QAAQ,IAAI,MAAI,CAAC1M,OAAL,CAAa+M,iBAAzB;;UACA,MAAI,CAAC3J,gBAAL,CAAsB0J,QAAtB,sDACG,MAAI,CAACtM,IAAL,CAAUA,IAAV,EAAgBoD,UADnB,IACgC8I,QADhC;;UAGAxM,QAAQ,CAACE,qBAAT,CAA+B0M,QAA/B;;;KAfN;;IAoBAA,QAAQ;;;;;;;SAMVE,oBAAA,6BAAoB;WACX,KAAKpF,SAAZ;;;;;;;SAMFqF,mBAAA,4BAAmB;WACV,KAAK7J,gBAAZ;;;SAGFzB,oBAAA,6BAAoB;;QAEd;;UAGA+H,gBAAgB,CAAC,KAAKtG,gBAAN,EAAwB,qBAAxB,CAAhB,CACG+I,OADH,KACe,MADf,IAEA,oBAAoBtN,QAAQ,CAAC2H,eAAT,CAAyB8D,KAF7C,IAGA,wBAAwBzL,QAAQ,CAAC2H,eAAT,CAAyB8D,KAJnD,EAKE;eACO,CAAP;OANF,MAOO;eACEnL,cAAc,CAAC,KAAKC,EAAN,CAArB;;KAVJ,CAYE,OAAO4B,CAAP,EAAU;aACH7B,cAAc,CAAC,KAAKC,EAAN,CAArB;;;;SAIJ8N,kBAAA,2BAAkB;;;QACVhN,QAAQ,GAAG3B,gBAAgB,CAAC,KAAKa,EAAN,CAAjC,CADgB;;QAGZ,KAAKY,OAAL,CAAa2I,QAAjB,EAA2B;WACpBvJ,EAAL,CAAQqF,mBAAR,CAA4B,YAA5B,EAA0C,KAAK3D,YAA/C;;;KAGD,WAAD,EAAc,OAAd,EAAuB,UAAvB,EAAmCgI,OAAnC,CAA2C,UAAA9H,CAAC,EAAI;MAC9C,MAAI,CAAC5B,EAAL,CAAQqF,mBAAR,CAA4BzD,CAA5B,EAA+B,MAAI,CAACsB,cAApC,EAAoD,IAApD;KADF;KAIC,YAAD,EAAe,UAAf,EAA2B,WAA3B,EAAwCwG,OAAxC,CAAgD,UAAA9H,CAAC,EAAI;MACnD,MAAI,CAAC5B,EAAL,CAAQqF,mBAAR,CAA4BzD,CAA5B,EAA+B,MAAI,CAACsB,cAApC,EAAoD;QAClDyG,OAAO,EAAE,IADyC;QAElDC,OAAO,EAAE;OAFX;KADF;SAOK5J,EAAL,CAAQqF,mBAAR,CAA4B,WAA5B,EAAyC,KAAK1D,WAA9C;SACK3B,EAAL,CAAQqF,mBAAR,CAA4B,YAA5B,EAA0C,KAAKlD,YAA/C;;QAEI,KAAK6B,gBAAT,EAA2B;WACpBA,gBAAL,CAAsBqB,mBAAtB,CAA0C,QAA1C,EAAoD,KAAKxE,QAAzD;;;IAGFC,QAAQ,CAACuE,mBAAT,CAA6B,QAA7B,EAAuC,KAAK/C,cAA5C;;QAEI,KAAK2H,gBAAT,EAA2B;WACpBA,gBAAL,CAAsB8D,UAAtB;;;QAGE,KAAKjE,cAAT,EAAyB;WAClBA,cAAL,CAAoBiE,UAApB;KAhCc;;;SAoCXhI,WAAL,CAAiB3D,MAAjB;SACKT,WAAL,CAAiBS,MAAjB;SACKK,cAAL,CAAoBL,MAApB;SACKE,cAAL,CAAoBF,MAApB;;;;;;;SAMF4L,UAAA,mBAAU;SACHF,eAAL;IACAnN,SAAS,CAACkF,SAAV,CAAoBoI,MAApB,CAA2B,KAAKjO,EAAhC;;;;;;;SAMF4C,iBAAA,wBAAesL,IAAf,EAAqB;WAEjB,KAAKrM,MAAL,IAAeqM,IAAI,CAACjH,IAApB,IACA,KAAKpF,MAAL,IAAeqM,IAAI,CAACjH,IAAL,GAAYiH,IAAI,CAAC3N,KADhC,IAEA,KAAKwB,MAAL,IAAemM,IAAI,CAAChH,GAFpB,IAGA,KAAKnF,MAAL,IAAemM,IAAI,CAAChH,GAAL,GAAWgH,IAAI,CAACrC,MAJjC;;;;;;;SAWF9C,YAAA,mBAAU/I,EAAV,EAAcmO,KAAd,EAAqB;QACbC,OAAO,GACXpO,EAAE,CAACoO,OAAH,IACApO,EAAE,CAACqO,qBADH,IAEArO,EAAE,CAACsO,kBAFH,IAGAtO,EAAE,CAACuO,iBAJL;WAKO5G,KAAK,CAACC,SAAN,CAAgBC,MAAhB,CAAuBC,IAAvB,CAA4B9H,EAAE,CAAC+H,QAA/B,EAAyC,UAAAC,KAAK;aACnDoG,OAAO,CAACtG,IAAR,CAAaE,KAAb,EAAoBmG,KAApB,CADmD;KAA9C,EAEL,CAFK,CAAP;;;;;;AAx6BiBxN,UAmGZ+E,iBAAiB;EACtB6D,QAAQ,EAAE,IADY;EAEtBtH,YAAY,EAAE,KAFQ;EAGtBmL,YAAY,EAAE,IAHQ;EAItBO,iBAAiB,EAAE,EAJG;EAKtB5K,UAAU,EAAE;IACVyF,SAAS,EAAE,mBADD;IAEVD,cAAc,EAAE,2BAFN;IAGVI,MAAM,EAAE,kBAHE;IAIVE,IAAI,EAAE,gBAJI;IAKVX,OAAO,EAAE,mBALC;IAMVc,WAAW,EAAE,uBANH;IAOVnG,SAAS,EAAE,qBAPD;IAQVH,KAAK,EAAE,iBARG;IASVuG,2BAA2B,EAAE,wCATnB;IAUVC,oBAAoB,EAAE,gCAVZ;IAWVlG,OAAO,EAAE,mBAXC;IAYVmG,UAAU,EAAE,sBAZF;IAaVC,QAAQ,EAAE,oBAbA;IAcV8D,KAAK,EAAE,iBAdG;IAeV9H,QAAQ,EAAE;GApBU;EAsBtBmH,gBAAgB,EAAE,EAtBI;EAuBtBC,gBAAgB,EAAE,CAvBI;EAwBtBrG,OAAO,EAAE;;AA3HQxF,UA6IZkF,YAAY,IAAI2I,OAAJ;;"}