{"version": 3, "file": "component-functions.js", "sources": ["../../src/util/component-functions.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): util/component-functions.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { getElementFromSelector, isDisabled } from './index'\n\nconst enableDismissTrigger = (component, method = 'hide') => {\n  const clickEvent = `click.dismiss${component.EVENT_KEY}`\n  const name = component.NAME\n\n  EventHandler.on(document, clickEvent, `[data-bs-dismiss=\"${name}\"]`, function (event) {\n    if (['A', 'AREA'].includes(this.tagName)) {\n      event.preventDefault()\n    }\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const target = getElementFromSelector(this) || this.closest(`.${name}`)\n    const instance = component.getOrCreateInstance(target)\n\n    // Method argument is left, for Alert and only, as it doesn't implement the 'hide' method\n    instance[method]()\n  })\n}\n\nexport {\n  enableDismissTrigger\n}\n"], "names": ["enableDismissTrigger", "component", "method", "clickEvent", "EVENT_KEY", "name", "NAME", "EventHandler", "on", "document", "event", "includes", "tagName", "preventDefault", "isDisabled", "target", "getElementFromSelector", "closest", "instance", "getOrCreateInstance"], "mappings": ";;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;;AAKMA,QAAAA,oBAAoB,GAAG,CAACC,SAAD,EAAYC,MAAM,GAAG,MAArB,KAAgC;EAC3D,EAAA,MAAMC,UAAU,GAAI,CAAA,aAAA,EAAeF,SAAS,CAACG,SAAU,CAAvD,CAAA,CAAA;EACA,EAAA,MAAMC,IAAI,GAAGJ,SAAS,CAACK,IAAvB,CAAA;EAEAC,EAAAA,6BAAY,CAACC,EAAb,CAAgBC,QAAhB,EAA0BN,UAA1B,EAAuC,CAAA,kBAAA,EAAoBE,IAAK,CAAA,EAAA,CAAhE,EAAqE,UAAUK,KAAV,EAAiB;MACpF,IAAI,CAAC,GAAD,EAAM,MAAN,CAAA,CAAcC,QAAd,CAAuB,IAAA,CAAKC,OAA5B,CAAJ,EAA0C;EACxCF,MAAAA,KAAK,CAACG,cAAN,EAAA,CAAA;EACD,KAAA;;EAED,IAAA,IAAIC,gBAAU,CAAC,IAAD,CAAd,EAAsB;EACpB,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,MAAMC,MAAM,GAAGC,4BAAsB,CAAC,IAAD,CAAtB,IAAgC,IAAA,CAAKC,OAAL,CAAc,CAAGZ,CAAAA,EAAAA,IAAK,EAAtB,CAA/C,CAAA;MACA,MAAMa,QAAQ,GAAGjB,SAAS,CAACkB,mBAAV,CAA8BJ,MAA9B,CAAjB,CAVoF;;MAapFG,QAAQ,CAAChB,MAAD,CAAR,EAAA,CAAA;KAbF,CAAA,CAAA;EAeD;;;;;;;;;;"}