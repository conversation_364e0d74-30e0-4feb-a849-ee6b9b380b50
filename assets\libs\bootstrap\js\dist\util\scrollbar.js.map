{"version": 3, "file": "scrollbar.js", "sources": ["../../src/util/scrollbar.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine'\nimport Manipulator from '../dom/manipulator'\nimport { isElement } from './index'\n\n/**\n * Constants\n */\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\nconst PROPERTY_PADDING = 'padding-right'\nconst PROPERTY_MARGIN = 'margin-right'\n\n/**\n * Class definition\n */\n\nclass ScrollBarHelper {\n  constructor() {\n    this._element = document.body\n  }\n\n  // Public\n  getWidth() {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n    const documentWidth = document.documentElement.clientWidth\n    return Math.abs(window.innerWidth - documentWidth)\n  }\n\n  hide() {\n    const width = this.getWidth()\n    this._disableOverFlow()\n    // give padding to element to balance the hidden scrollbar width\n    this._setElementAttributes(this._element, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements to keep showing fullwidth\n    this._setElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    this._setElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN, calculatedValue => calculatedValue - width)\n  }\n\n  reset() {\n    this._resetElementAttributes(this._element, 'overflow')\n    this._resetElementAttributes(this._element, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN)\n  }\n\n  isOverflowing() {\n    return this.getWidth() > 0\n  }\n\n  // Private\n  _disableOverFlow() {\n    this._saveInitialAttribute(this._element, 'overflow')\n    this._element.style.overflow = 'hidden'\n  }\n\n  _setElementAttributes(selector, styleProperty, callback) {\n    const scrollbarWidth = this.getWidth()\n    const manipulationCallBack = element => {\n      if (element !== this._element && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      this._saveInitialAttribute(element, styleProperty)\n      const calculatedValue = window.getComputedStyle(element).getPropertyValue(styleProperty)\n      element.style.setProperty(styleProperty, `${callback(Number.parseFloat(calculatedValue))}px`)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _saveInitialAttribute(element, styleProperty) {\n    const actualValue = element.style.getPropertyValue(styleProperty)\n    if (actualValue) {\n      Manipulator.setDataAttribute(element, styleProperty, actualValue)\n    }\n  }\n\n  _resetElementAttributes(selector, styleProperty) {\n    const manipulationCallBack = element => {\n      const value = Manipulator.getDataAttribute(element, styleProperty)\n      // We only want to remove the property if the value is `null`; the value can also be zero\n      if (value === null) {\n        element.style.removeProperty(styleProperty)\n        return\n      }\n\n      Manipulator.removeDataAttribute(element, styleProperty)\n      element.style.setProperty(styleProperty, value)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _applyManipulationCallback(selector, callBack) {\n    if (isElement(selector)) {\n      callBack(selector)\n      return\n    }\n\n    for (const sel of SelectorEngine.find(selector, this._element)) {\n      callBack(sel)\n    }\n  }\n}\n\nexport default ScrollBarHelper\n"], "names": ["SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "PROPERTY_PADDING", "PROPERTY_MARGIN", "ScrollBarHelper", "constructor", "_element", "document", "body", "getWidth", "documentWidth", "documentElement", "clientWidth", "Math", "abs", "window", "innerWidth", "hide", "width", "_disableOver<PERSON>low", "_setElementAttributes", "calculatedValue", "reset", "_resetElementAttributes", "isOverflowing", "_saveInitialAttribute", "style", "overflow", "selector", "styleProperty", "callback", "scrollbarWidth", "manipulationCallBack", "element", "getComputedStyle", "getPropertyValue", "setProperty", "Number", "parseFloat", "_applyManipulationCallback", "actualValue", "Manipulator", "setDataAttribute", "value", "getDataAttribute", "removeProperty", "removeDataAttribute", "callBack", "isElement", "sel", "SelectorEngine", "find"], "mappings": ";;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAMA;EACA;EACA;;EAEA,MAAMA,sBAAsB,GAAG,mDAA/B,CAAA;EACA,MAAMC,uBAAuB,GAAG,aAAhC,CAAA;EACA,MAAMC,gBAAgB,GAAG,eAAzB,CAAA;EACA,MAAMC,eAAe,GAAG,cAAxB,CAAA;EAEA;EACA;EACA;;EAEA,MAAMC,eAAN,CAAsB;EACpBC,EAAAA,WAAW,GAAG;EACZ,IAAA,IAAA,CAAKC,QAAL,GAAgBC,QAAQ,CAACC,IAAzB,CAAA;EACD,GAHmB;;;EAMpBC,EAAAA,QAAQ,GAAG;EACT;EACA,IAAA,MAAMC,aAAa,GAAGH,QAAQ,CAACI,eAAT,CAAyBC,WAA/C,CAAA;MACA,OAAOC,IAAI,CAACC,GAAL,CAASC,MAAM,CAACC,UAAP,GAAoBN,aAA7B,CAAP,CAAA;EACD,GAAA;;EAEDO,EAAAA,IAAI,GAAG;EACL,IAAA,MAAMC,KAAK,GAAG,IAAKT,CAAAA,QAAL,EAAd,CAAA;;MACA,IAAKU,CAAAA,gBAAL,GAFK;;;EAIL,IAAA,IAAA,CAAKC,qBAAL,CAA2B,IAAKd,CAAAA,QAAhC,EAA0CJ,gBAA1C,EAA4DmB,eAAe,IAAIA,eAAe,GAAGH,KAAjG,EAJK;;;MAML,IAAKE,CAAAA,qBAAL,CAA2BpB,sBAA3B,EAAmDE,gBAAnD,EAAqEmB,eAAe,IAAIA,eAAe,GAAGH,KAA1G,CAAA,CAAA;;MACA,IAAKE,CAAAA,qBAAL,CAA2BnB,uBAA3B,EAAoDE,eAApD,EAAqEkB,eAAe,IAAIA,eAAe,GAAGH,KAA1G,CAAA,CAAA;EACD,GAAA;;EAEDI,EAAAA,KAAK,GAAG;EACN,IAAA,IAAA,CAAKC,uBAAL,CAA6B,IAAKjB,CAAAA,QAAlC,EAA4C,UAA5C,CAAA,CAAA;;EACA,IAAA,IAAA,CAAKiB,uBAAL,CAA6B,IAAKjB,CAAAA,QAAlC,EAA4CJ,gBAA5C,CAAA,CAAA;;EACA,IAAA,IAAA,CAAKqB,uBAAL,CAA6BvB,sBAA7B,EAAqDE,gBAArD,CAAA,CAAA;;EACA,IAAA,IAAA,CAAKqB,uBAAL,CAA6BtB,uBAA7B,EAAsDE,eAAtD,CAAA,CAAA;EACD,GAAA;;EAEDqB,EAAAA,aAAa,GAAG;MACd,OAAO,IAAA,CAAKf,QAAL,EAAA,GAAkB,CAAzB,CAAA;EACD,GA/BmB;;;EAkCpBU,EAAAA,gBAAgB,GAAG;EACjB,IAAA,IAAA,CAAKM,qBAAL,CAA2B,IAAKnB,CAAAA,QAAhC,EAA0C,UAA1C,CAAA,CAAA;;EACA,IAAA,IAAA,CAAKA,QAAL,CAAcoB,KAAd,CAAoBC,QAApB,GAA+B,QAA/B,CAAA;EACD,GAAA;;EAEDP,EAAAA,qBAAqB,CAACQ,QAAD,EAAWC,aAAX,EAA0BC,QAA1B,EAAoC;EACvD,IAAA,MAAMC,cAAc,GAAG,IAAKtB,CAAAA,QAAL,EAAvB,CAAA;;MACA,MAAMuB,oBAAoB,GAAGC,OAAO,IAAI;EACtC,MAAA,IAAIA,OAAO,KAAK,IAAK3B,CAAAA,QAAjB,IAA6BS,MAAM,CAACC,UAAP,GAAoBiB,OAAO,CAACrB,WAAR,GAAsBmB,cAA3E,EAA2F;EACzF,QAAA,OAAA;EACD,OAAA;;EAED,MAAA,IAAA,CAAKN,qBAAL,CAA2BQ,OAA3B,EAAoCJ,aAApC,CAAA,CAAA;;QACA,MAAMR,eAAe,GAAGN,MAAM,CAACmB,gBAAP,CAAwBD,OAAxB,CAAiCE,CAAAA,gBAAjC,CAAkDN,aAAlD,CAAxB,CAAA;EACAI,MAAAA,OAAO,CAACP,KAAR,CAAcU,WAAd,CAA0BP,aAA1B,EAA0C,CAAA,EAAEC,QAAQ,CAACO,MAAM,CAACC,UAAP,CAAkBjB,eAAlB,CAAD,CAAqC,CAAzF,EAAA,CAAA,CAAA,CAAA;OAPF,CAAA;;EAUA,IAAA,IAAA,CAAKkB,0BAAL,CAAgCX,QAAhC,EAA0CI,oBAA1C,CAAA,CAAA;EACD,GAAA;;EAEDP,EAAAA,qBAAqB,CAACQ,OAAD,EAAUJ,aAAV,EAAyB;MAC5C,MAAMW,WAAW,GAAGP,OAAO,CAACP,KAAR,CAAcS,gBAAd,CAA+BN,aAA/B,CAApB,CAAA;;EACA,IAAA,IAAIW,WAAJ,EAAiB;EACfC,MAAAA,4BAAW,CAACC,gBAAZ,CAA6BT,OAA7B,EAAsCJ,aAAtC,EAAqDW,WAArD,CAAA,CAAA;EACD,KAAA;EACF,GAAA;;EAEDjB,EAAAA,uBAAuB,CAACK,QAAD,EAAWC,aAAX,EAA0B;MAC/C,MAAMG,oBAAoB,GAAGC,OAAO,IAAI;QACtC,MAAMU,KAAK,GAAGF,4BAAW,CAACG,gBAAZ,CAA6BX,OAA7B,EAAsCJ,aAAtC,CAAd,CADsC;;QAGtC,IAAIc,KAAK,KAAK,IAAd,EAAoB;EAClBV,QAAAA,OAAO,CAACP,KAAR,CAAcmB,cAAd,CAA6BhB,aAA7B,CAAA,CAAA;EACA,QAAA,OAAA;EACD,OAAA;;EAEDY,MAAAA,4BAAW,CAACK,mBAAZ,CAAgCb,OAAhC,EAAyCJ,aAAzC,CAAA,CAAA;EACAI,MAAAA,OAAO,CAACP,KAAR,CAAcU,WAAd,CAA0BP,aAA1B,EAAyCc,KAAzC,CAAA,CAAA;OATF,CAAA;;EAYA,IAAA,IAAA,CAAKJ,0BAAL,CAAgCX,QAAhC,EAA0CI,oBAA1C,CAAA,CAAA;EACD,GAAA;;EAEDO,EAAAA,0BAA0B,CAACX,QAAD,EAAWmB,QAAX,EAAqB;EAC7C,IAAA,IAAIC,eAAS,CAACpB,QAAD,CAAb,EAAyB;QACvBmB,QAAQ,CAACnB,QAAD,CAAR,CAAA;EACA,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,KAAK,MAAMqB,GAAX,IAAkBC,+BAAc,CAACC,IAAf,CAAoBvB,QAApB,EAA8B,IAAA,CAAKtB,QAAnC,CAAlB,EAAgE;QAC9DyC,QAAQ,CAACE,GAAD,CAAR,CAAA;EACD,KAAA;EACF,GAAA;;EAtFmB;;;;;;;;"}